@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";
@import "dist/ui-kit/assets/import";

.breadcrumb-wrapper {
	margin-bottom: 25px;
}

.category-name {
	color: $preto02;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 100%;

	&.navigation {
		cursor: pointer;
	}
}

.lower-menu {
	display: inline-block;
	color: $pretoPri;
	font-size: 24px;
	font-style: normal;
	font-weight: 700;
	cursor: pointer;

	.back {
		display: flex;
		align-items: center;

		i {
			margin-right: 5px;
			font-size: 24px;
			font-style: normal;
			font-weight: 400;
			cursor: pointer;
		}

		span {
			margin-bottom: -3px;
		}
	}

	&.module-crossfit {
		color: primaryColor(pacto, base);
	}

	&.module-treino {
		color: primaryColor(pacto, base);
	}
}
