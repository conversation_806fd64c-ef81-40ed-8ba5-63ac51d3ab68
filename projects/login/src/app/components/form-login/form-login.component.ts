import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	Inject,
	LOCALE_ID,
	OnDestroy,
	OnInit,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	ApiResponseSingle,
	AuthDTO,
	LoginAppApiDataUserService,
	LoginAppApiLoginService,
} from "login-app-api";
import { SnotifyService } from "ng-snotify";
import { Subscription } from "rxjs";
import { environment } from "../../../environments/environment";
import {
	RecentUsers,
	RecentUsersService,
} from "../../services/recent-users.service";
import { DomSanitizer } from "@angular/platform-browser";
import { DOCUMENT, Location } from "@angular/common";
import { IpService } from "../../services/ip.service";
import { LoginType } from "../../interfaces/login-type.enum";
import { debounceTime } from "rxjs/operators";
import { TraducoesXinglingComponent } from "ui-kit";
import { RecaptchaComponent } from "ng-recaptcha";
import { valueToBoolean } from "sdk";

@Component({
	selector: "login-form",
	templateUrl: "./form-login.component.html",
	styleUrls: ["./form-login.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class FormLoginComponent implements OnInit, OnDestroy {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("captcha", { static: false }) captcha: RecaptchaComponent;

	form: FormGroup = new FormGroup({});
	passwordInputType: "password" | "text" = "password";
	pswIcon = "pct-eye";
	showLoader = false;
	logandoAzureAd = false;
	recaptchaToken: string;
	environment = environment;

	subscriptions: Array<Subscription> = new Array<Subscription>();

	recentUsers: Array<RecentUsers>;
	recentUserFilterControl: FormControl = new FormControl();
	recentUsersFiltered: Array<RecentUsers>;

	loginType: LoginType;
	loginTypeEnum = LoginType;
	enableCaptcha: boolean;

	constructor(
		@Inject(DOCUMENT) private document,
		@Inject(LOCALE_ID) public locale,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private autenticacaoService: LoginAppApiLoginService,
		private notificationService: SnotifyService,
		private ipService: IpService,
		private loginAppDataUser: LoginAppApiDataUserService,
		private recentUsersService: RecentUsersService,
		public domSanitizer: DomSanitizer,
		private _location: Location,
		private cd: ChangeDetectorRef,
		private readonly route: ActivatedRoute
	) {
		this.route.queryParams.subscribe((params) => {
			if (params["azAd"]) {
				this.showLoader = true;
				this.logandoAzureAd = true;
				const subscription = this.autenticacaoService
					.loginAzAd(params["azAd"])
					.subscribe(
						(response) => {
							if (response.result) {
								this.logandoAzureAd = false;
								this.router.navigate(["dashboard"]);
								this.recentUsersService.updateRecentUsers();
							}
						},
						(err) => {
							this.logandoAzureAd = false;
							this.resetCaptcha();
							const responseError = err.error as ApiResponseSingle<any>;
							if (responseError) {
								if (responseError.meta.error) {
									this.notificationService.error(
										this.traducoes.getLabel(responseError.meta.error)
									);
								} else {
									const message = responseError.meta.message
										? responseError.meta.message
										: responseError.meta.error;
									this.notificationService.error(message);
								}
							} else {
								this.notificationService.error("erro desconhecido");
							}
						}
					);
				this.subscriptions.push(subscription);
			}
		});
	}

	ngOnInit() {
		this.recentUsers = this.recentUsersService.getRecentUsers();
		this.enableCaptcha = valueToBoolean(this.environment.enableCaptcha);
		if (!this.recentUsers) {
			this.recentUsers = new Array<RecentUsers>();
		}
		if (this.activatedRoute.snapshot.data) {
			this.loginType = this.activatedRoute.snapshot.data.loginType;
		} else {
			this.router.navigate([".."], {
				relativeTo: this.activatedRoute,
			});
		}
		if (this.loginType === LoginType.CELULAR) {
			this.recentUsers = this.recentUsers.filter((ru) => ru.celular);
		}
		this.recentUsersFiltered = this.recentUsers;
		this.recentUserFilterControl.valueChanges
			.pipe(debounceTime(300))
			.subscribe((v) => {
				this.recentUsersFiltered = this.recentUsers.filter(
					(ru) =>
						ru.email.toUpperCase().includes(v.toUpperCase()) ||
						ru.name.toUpperCase().includes(v.toUpperCase()) ||
						ru.celular.includes(
							v
								.replace("(", "")
								.replace(")", "")
								.replace(" ", "")
								.replace("-", "")
						)
				);
			});
		this.createForm();
	}

	ngOnDestroy() {
		this.subscriptions.forEach((s) => s.unsubscribe());
	}

	convertToSafeStyle(image: string) {
		return this.domSanitizer.bypassSecurityTrustStyle(`url(${image})`);
	}

	createForm() {
		this.form = new FormGroup({
			usuario: new FormControl("", [Validators.required]),
			senha: new FormControl("", Validators.required),
		});
		if (this.loginType === LoginType.EMAIL) {
			this.form
				.get("usuario")
				.setValidators([Validators.required, Validators.email]);
			this.form.get("usuario").updateValueAndValidity();
		}
	}

	login() {
		if (this.form.get("usuario").valid) {
			if (this.form.valid) {
				if (this.enableCaptcha && !this.recaptchaToken) {
					this.notificationService.error(
						this.traducoes.getLabel("erro-captcha")
					);
					return;
				}
				const authDTO = new AuthDTO();
				authDTO.username = this.form.get("usuario").value;
				authDTO.senha = this.form.get("senha").value;
				authDTO.recaptchaToken = this.recaptchaToken;
				authDTO.ddi = "+55";
				if (this.activatedRoute.snapshot.paramMap.has("chave")) {
					authDTO.chave = this.activatedRoute.snapshot.paramMap.get("chave");
				}
				this.showLoader = true;
				const subscription = this.autenticacaoService.login(authDTO).subscribe(
					(response) => {
						if (response.result) {
							this.showLoader = false;
							this.router.navigate(["dashboard"]);
							this.recentUsersService.updateRecentUsers();
						}
					},
					(err) => {
						this.showLoader = false;
						this.resetCaptcha();
						const responseError = err.error as ApiResponseSingle<any>;
						if (responseError) {
							if (responseError.meta.error) {
								this.notificationService.error(
									this.traducoes.getLabel(responseError.meta.error)
								);
							} else {
								const message = responseError.meta.message
									? responseError.meta.message
									: responseError.meta.error;
								this.notificationService.error(message);
							}
						} else {
							this.notificationService.error("erro desconhecido");
						}
					}
				);
				this.subscriptions.push(subscription);
			}
		}
	}

	changePswInputType() {
		if (this.passwordInputType === "password") {
			this.passwordInputType = "text";
			this.pswIcon = "pct-eye-off";
		} else {
			this.passwordInputType = "password";
			this.pswIcon = "pct-eye";
		}
	}

	resolved(event: any) {
		this.recaptchaToken = event;
		// G_TOKEN
	}

	error(event: []) {
		this.recaptchaToken = undefined;
		this.resetCaptcha();
	}

	resetCaptcha() {
		if (this.captcha) {
			this.captcha.reset();
			this.cd.detectChanges();
		}
	}

	logarRecentUser(recentUser: RecentUsers) {
		if (this.loginType === LoginType.EMAIL) {
			this.form.get("usuario").setValue(recentUser.email);
		} else if (this.loginType === LoginType.CELULAR) {
			this.form.get("usuario").setValue(recentUser.celular);
		}
		this.document.getElementById("form-login-psw-field").focus();
	}

	voltar() {
		this._location.back();
	}
}
