export class ContratoOperacao {
	codigo: number;
	clienteTransfereDias: number;
	clienteRecebeDias: number;
	descricaoCalculo: string;
	observacao: string;
	dataOperacao: any;
	dataInicioEfetivacaoOperacao: any;
	dataFimEfetivacaoOperacao: any;
	operacaoPaga: boolean;
	tipoOperacao: string;
	valor: number;
	nrDiasOperacao: number;
	informacoes: string;
	chaveArquivo: string;
	origemSistema: number;
	informacoesDesfazer: string;
	contrato: any;
	tipoJustificativa: any;
	responsavel: any;
	responsavelLiberacao: any;
	nomeArquivo: string;
	formatoArquivo: string;
}
