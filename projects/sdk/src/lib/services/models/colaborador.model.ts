import { Perfil } from "./perfil-acesso.model";

export enum SituacoesEnum {
	ATIVO = "Ativo",
	INATIVO = "Inativo",
}

export enum ColaboradorSexo {
	MASCULINO = "M",
	FEMININO = "F",
}

export enum ColaboradorTipoUsuario {
	CONSULTOR = "Consultor",
	COORDENADOR = "Coordenador",
	PROFESSOR = "Professor",
}

export interface ColaboradorEmail {
	id: string;
	email: string;
}

export interface ColaboradorTelefone {
	id: string;
	phone: string;
}

export interface Colaborador {
	id?: string;
	nome?: string;
	situacao?: SituacoesEnum;
	dataNascimento?: string;
	sexo?: ColaboradorSexo;
	emails?: Array<ColaboradorEmail>;
	fones?: Array<ColaboradorTelefone>;
	usarApp?: boolean;
	appUserName?: string;
	tipoUsuario?: ColaboradorTipoUsuario;
	perfilUsuario?: Perfil;
	uriImagem?: string;
}

export interface ColaboradorBase {
	id: string;
	nome: string;
	situacao: SituacoesEnum;
	dataNascimento: string;
	sexo: ColaboradorSexo;
	usarApp: boolean;
	appUserName: string;
}

export interface ColaboradorCreateEdit {
	nome: string;
	situacao: SituacoesEnum;
	dataNascimento: string;
	sexo: ColaboradorSexo;
	emails: Array<ColaboradorEmail>;
	fones: Array<ColaboradorTelefone>;
	usarApp: boolean;
	appUserName?: string;
	appPassword?: string;
	tipoUsuario?: ColaboradorTipoUsuario;
	perfilUsuario?: Perfil;
	imagemData?: string;
	tipoImagem?: string;
}

export interface ColaboradorSelect {
	id: number;
	nome: string;
	imageUri: string;
}
