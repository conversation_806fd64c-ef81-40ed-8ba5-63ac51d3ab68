import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { IntegracaoService } from "midia-social-api";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-integracao-gupshup-modal",
	templateUrl: "./integracao-gupshup-modal.component.html",
	styleUrls: ["./integracao-gupshup-modal.component.scss"],
})
export class IntegracaoGupshupModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	integracaoData;
	formGroup: FormGroup = new FormGroup({
		appName: new FormControl("", Validators.required),
		apiKey: new FormControl("", Validators.required),
		sourceId: new FormControl("", Validators.required),
		appId: new FormControl("", Validators.required),
		usuarioParceiro: new FormControl("", Validators.required),
		senhaParceiro: new FormControl("", Validators.required),
	});

	constructor(
		public integracaoService: IntegracaoService,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.integracaoService
			.getInfointegration()
			.subscribe((response: { message: any[]; result: any[] }) => {
				const result = response.result[0];
				this.integracaoData = result;
				const wahtsAppConfig = result ? result.configuracaoWhatsApp : null;
				if (wahtsAppConfig !== null) {
					this.formGroup.patchValue(wahtsAppConfig);
				}
			});
	}

	salvar() {
		if (this.formGroup.invalid) {
			this.snotifyService.error(this.traducao.getLabel("campos-invalidos"));
			return;
		}
		const params = this.formGroup.getRawValue();
		this.integracaoService
			.updateIntegration({ configuracaoWhatsApp: params })
			.subscribe(() =>
				this.snotifyService.success(this.traducao.getLabel("dados-salvos"))
			);
	}

	inverterStatus() {
		// this.integracaoData.status = !this.integracaoData.status;
	}
}
