<adm-layout
	i18n-pageTitle="@@operadora-cartao:title"
	pageTitle="Operadora de cartão"
	i18n-modulo="@@peradora-cartao:modulo"
	modulo="Administrativo"
	(goBack)="voltarListagem()">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<h3 i18n="@@operadora-cartao:insira-os-dados" class="titulo">
				Insira os dados
			</h3>

			<div class="row" style="margin-bottom: 20px">
				<div class="col-md-7">
					<pacto-cat-form-input
						*ngIf="!exibirSelectDescricaoPadrao"
						i18n-label="@@operadora-cartao:label-descricao"
						label="Descrição"
						id="descricao"
						readonly="true"
						placeholder="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
						[control]="form.get('descricao')"
						[maxlength]="50"></pacto-cat-form-input>

					<pacto-cat-form-select
						*ngIf="exibirSelectDescricaoPadrao"
						i18n-label="@@operadora-cartao:label-descricao"
						label="Descrição"
						id="descricaoNovo"
						title="Escolha uma descrição padrão."
						class="input-box"
						[control]="form.get('descricao')"
						[items]="descricaoPadraoArray"></pacto-cat-form-select>
				</div>
				<div class="col-md-3">
					<div class="col-md-4" class="situacao-checkbox">
						<span i18n="@@operadora-cartao:filtros">Situação</span>
					</div>
					<pacto-cat-checkbox
						i18n-label="@@operadora-cartao:label-ativo"
						label="Ativo"
						id="checkbox-ativo"
						[control]="form.get('ativo')"></pacto-cat-checkbox>
				</div>
			</div>

			<div class="row" id="checkbox-field">
				<pacto-cat-checkbox
					i18n-label="@@operadora-cartao:padrao-para-recebimento"
					label="Padrão para recebimento em cartão de crédito"
					id="checkbox-padrao"
					*ngIf="form.get('credito').value === true"
					[control]="form.get('padraoRecebimento')"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					i18n-label="@@operadora-cartao:padrao-para-recebimento-debito"
					label="Padrão para recebimento em cartão de débito"
					id="checkbox-padrao-debito"
					*ngIf="form.get('credito').value === false"
					[control]="form.get('padraoRecebimento')"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					id="e-cartao-checkbox"
					i18n-label="@@operadora-cartao:e-cartao-credito"
					label="É Cartão de Credito?"
					[control]="form.get('credito')"></pacto-cat-checkbox>
			</div>

			<div class="main-div">
				<pacto-cat-form-input-number
					*ngIf="this.form.get('credito').value == true"
					i18n-label="@@operadora-cartao:label-descricao"
					label="Quantidade Máxima de Parcelas"
					[formControl]="form.get('qtdeMaxParcelas')"
					placeholder="Insira a quantidade de parcelas"
					id="input-parcelas"
					class="input-box"></pacto-cat-form-input-number>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-codigo-pagamento"
					label="Código pagamento digital"
					[items]="codPgtoDigitalArray"
					[control]="form.get('codigoIntegracao')"
					id="input-codigo-pgto"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-codigo-aprova-facil"
					label="Código aprova fácil"
					[items]="codAprovaFacilArray"
					[control]="form.get('codigoIntegracaoPagoLivre')"
					id="input-aprova-facil"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-vindi"
					label="Bandeira Vindi"
					[items]="bandeiraVindiArray"
					[control]="form.get('codigoIntegracaoVindi')"
					id="input-bandeira-vinci"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-erede"
					label="Bandeira e- Rede"
					[items]="bandeiraERedeArray"
					[control]="form.get('codigoIntegracaoERede')"
					id="input-bandeira-e-rede"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-maxipago"
					label="Bandeira MaxiPago"
					[items]="bandeiraMaxiPagoArray"
					[control]="form.get('codigoIntegracaoMaxiPago')"
					id="input-bandeira-maxipago"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-fitnesscard"
					label="Bandeira Fitness Card"
					[items]="bandeiraFitnessCardArray"
					[control]="form.get('codigoIntegracaoFitnessCard')"
					id="input-bandeira-fitnesscard"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-getnetonline"
					label="Bandeira GetNet online"
					[items]="bandeiraGetNetOnlineArray"
					[control]="form.get('codigoIntegracaoGetNet')"
					id="input-bandeira-getnetonline"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-cielo"
					label="Bandeira Cielo"
					idKey="id"
					[items]="bandeiraCieloArray"
					[control]="form.get('codigoIntegracaoCielo')"
					id="input-bandeira-cielo"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-tipo-debitoonline"
					label="Tipo débito online"
					[items]="tipoDebitoOnlineArray"
					[control]="form.get('tipoDebitoOnline')"
					id="input-tipo-debito-online"
					class="input-box"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-operadora-debitooline"
					label="Operadora débito online"
					[items]="operadoraDebitoOnlineArray"
					[control]="form.get('codigoIntegracaoDebito')"
					id="input-operadora-debito-online"
					class="input-box"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-cappta"
					label="Bandeira Capta"
					[items]="bandeiraCaptaArray"
					[control]="form.get('bandeiraCappta')"
					id="input-bandeira-cappta"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-stoneonline"
					label="Bandeira Stone online"
					[items]="bandeiraStoneOnlineArray"
					[control]="form.get('codigoIntegracaoValorIBank')"
					id="input-bandeira-stone-online"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-mundipagg"
					label="Bandeira Mundipagg"
					[items]="bandeiraMundipaggArray"
					[control]="form.get('codigoIntegracaoMundiPagg')"
					id="input-bandeira-mundipagg"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-pagarme"
					label="Bandeira Pagar.me"
					[items]="bandeiraPagarMeArray"
					[control]="form.get('codigoIntegracaoPagarme')"
					id="input-bandeira-pagar-me"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-Stripe"
					label="Bandeira Stripe"
					[items]="bandeiraStripeArray"
					[control]="form.get('codigoIntegracaoStripe')"
					id="input-bandeira-strippe"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-DccCaixaOnline"
					label="Bandeira DCC Caixa Online"
					[items]="bandeiraDCCCaixaOnlineArray"
					[control]="form.get('codigointegracaodcccaixaonline')"
					id="input-bandeira-dcc-caixa-online"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>

				<pacto-cat-form-select
					i18n-label="@@operadora-cartao:label-bandeira-Pagolivre"
					label="Bandeira Pagolivre"
					[items]="bandeiraPagolivreArray"
					[control]="form.get('codigoIntegracaoPagoLivre')"
					id="input-bandeira-pagolivre"
					class="input-box"
					title="Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo."
					disabled="true"></pacto-cat-form-select>
			</div>
		</div>

		<div class="row">
			<div id="informe-os-dados">
				<h1 i18n="@@operadora-cartao:informe-os-dados">Informe os dados</h1>
				<p i18n="@@operadora-cartao:informe-os-paramentros">
					Informe os parâmetros acima para cadastrar uma nova justificativa de
					operação.
				</p>
			</div>
		</div>

		<div class="row justify-content-end">
			<pacto-cat-button
				id="btn-voltar"
				type="OUTLINE"
				i18n-label="@@operadora-cartao:btn-voltar"
				label="Voltar"
				size="LARGE"
				width="80px"
				style="margin-right: 10px"
				(click)="voltarListagem()"></pacto-cat-button>

			<pacto-cat-button
				(click)="salvar()"
				[iconPosition]="'after'"
				[icon]="'pct pct-save'"
				i18n-label="@@operadora-cartao:btn-salvar"
				id="btn-salvar"
				label="Salvar"
				size="LARGE"
				type="PRIMARY"
				width="110px"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@operadora-cartao:saveMsg" xingling="SAVE_MSG">
		Operadora cadastrada com sucesso!
	</span>
</pacto-traducoes-xingling>
