import { ConfigItemType } from "./configuration-builder.model";
export interface ConfiguracoesGerais {
	validar_agenda_aulacheia?: boolean;
	desativar_tela_aluno_treino?: boolean;
	troca_nomenclatura_crossfit?: string; // nomenclatura para treinamento funcional
	configuracoes_treino_replicar_rede_empresa?: boolean;
	escolher_tipo_lista_rapida_acessos?: boolean;
	duracao_aluno_na_academia?: string; // Permanencia aluno academina
	minutos_cancelar_com_antecedencia?: string;
}

export interface ConfiguracoesAulas {
	nr_aula_experimental_aluno?: string;
	minutos_agendar_com_antecedencia?: string;
	minutos_desmarcar_com_antecedencia?: string;
	validar_modalidade?: string;
	controlar_por_freepass?: string;
	validar_horario_contrato?: string;
	permitir_aluno_marcar_aula_por_tipo_modalidade?: string;
	limite_dias_reposicao_aula_coletiva?: string;
	manter_reposicao_aula_coletiva?: boolean;
	bloquear_mesmo_ambiente?: boolean;
	desmarcar_aulas_futuras_parcela_atrasada?: string;
	bloquear_aula_coletiva_nao_pertence_modalidade?: string;
	nr_validar_vezes_modalidade?: any;
	qtd_faltas_bloqueio_aluno?: any;
	qtd_tempo_bloqueio_aluno?: any;
	descontar_credito_ao_marcar_aula_sem_confirmar_presenca?: boolean;
	bloquear_gerar_reposicao_aula_ja_reposta?: boolean;
	utilizar_numeracao_sequencial_identificador_equipamento?: boolean;
	proibir_marcar_aula_antes_pagamento_primeira_parcela?: boolean;
}

export interface ConfiguracoesAplicativos {
	mobile_sempre_atualizar_carga_ficha?: boolean;
	permitir_reagendamento_por_aluno?: boolean;
	modulo_aulas?: boolean;
	modulo_aulas_aba_turmas?: boolean;
	modulo_aulas_aba_saldo?: boolean;
	modulo_treinar?: boolean;
	aluno_marcar_propria_aula?: boolean;
	habilitar_crossfit?: boolean;
	nao_exibir_numero_de_vezes_no_app?: boolean;
	proibir_marcar_aula_parcela_vencida?: boolean;
	proibir_buscar_programa_parcela_vencida?: boolean;
	nome_aplicativo_para_envio_email?: string;
	link_app_email?: string;
	dias_mostrar_totem?: string;
	habilitar_fila_espera?: boolean;
	habilitar_ver_wod_todas_empresas_app?: boolean;
}

export interface ConfiguracoesGestao {
	periodo_usado_bi?: string;
	inativos_a_x_dias?: string;
	somente_aluno_contrato_desistente?: string;
}

export interface ConfiguracoesNotificacao {
	iniciou_treino?: boolean;
	aumentou_carga?: boolean;
	diminuiu_carga?: boolean;
	aluno_chegou?: boolean;
	agenda?: boolean;
	agendamento_confirmado?: boolean;
	solicitar_renovacao?: boolean;
	lembrar_aluno_compromisso?: boolean;
	agendamento_alterado?: boolean;
	aluno_em_risco?: boolean;
	aluno_agendou?: boolean;
	numero_dias_notificar_treino_vencido?: boolean;
	sms_notificacao?: boolean;
}

export class ConfiguracoesIntegracoes {
	usar_integracao_bioimpedancia?: boolean;
	integracao_sistema_olympia?: boolean;
	url_sistema_olympia?: any;
	token_sistema_olympia?: any;
	usuario_sistema_olympia?: any;
	senha_sistema_olympia?: any;
	usar_gympass_booking?: any;
	codigo_gympass_booking?: any;
	usar_totalpass?: any;
	codigo_totalpass?: any;
}

export class ConfiguracoesIntegracoesEmpresasAdm {
	configuracoesIntegracaoMyWellness?: any;
	configuracoesIntegracaoMentorWeb?: any;
	configuracoesIntegracaoEstacionamento?: any;
	configuracoesIntegracaoParceiroFidelidade?: any;
	configuracoesIntegracaoGympass?: any;
	configuracoesIntegracaoSpivi?: any;
	configuracoesIntegracaoTotalpass?: any;
}

export class ConfiguracoesIntegracoesLista {
	codigo?: any;
	codigoGymPass?: any;
	empresa?: any;
	nome?: string;
	usarGymPassBooking?: boolean;
	limiteDeAcessosPorDia?: number;
	limiteDeAulasPorDia?: number;
}

export class ConfiguracoesIntegracoesListaGoGood {
	tokenAcademyGoGood?: string;
	empresa?: any;
	nome?: string;
}

export class ConfiguracoesIntegracoesListaTotalPass {
	codigo?: any;
	codigoTotalPass?: any;
	empresa?: any;
	nome?: string;
	apikey?: any;
	permitirWod?: boolean;
	ativo?: boolean;
	usarTotalPass?: boolean;
	limiteDeAcessosPorDia?: number;
	limiteDeAulasPorDia?: number;
}

export class ConfiguracoesIntegracoesListaSelfloops {
	pactoClientId?: string;
	empresa?: number;
	nome?: string;
	code?: string;
	empresaSelfloops?: string;
	teams?: any[];
}

export class ConfiguracoesIntegracoesListaMGB {
	empresa?: any;
	token?: any;
	nome?: any;
}

export class ConfiguracoesIntegracoesListaMQV {
	empresa?: any; /*codigo empresa treino*/
	token?: any;
	nome?: any;
}

export interface ConfiguracoesAvaliacao {
	cfg_objetivos_anamnese?: boolean;
	cfg_peso_altura_pa_fc?: boolean;
	cfg_parq?: boolean;
	cfg_dobras_cutaneas?: boolean;
	cfg_perimetria?: boolean;
	cfg_composicao_corporal?: boolean;
	cfg_flexibilidade?: boolean;
	cfg_postura?: boolean;
	cfg_rml?: boolean;
	cfg_rml_opcoes?: boolean;
	cfg_rml_separado?: boolean;
	cfg_tipo_rml?: string;
	cfg_vo2max?: boolean;
	cfg_recomendacoes?: boolean;
	cfg_ventilometria?: boolean;
	cfg_testes_campo?: boolean;
	cfg_teste_bike?: boolean;
	cfg_somatotipia?: boolean;
	cfg_teste_queens?: boolean;
	cfg_importacao_biosanny?: boolean;
	lancar_agendamento_proxima_avaliacao?: boolean;
	lancar_produto_avaliacao?: boolean;
	validar_produto_avaliacao?: any;
	produto_avaliacao?: any;
	lancar_produto_avaliacao_data_vencimento?: boolean;
	usar_pressao_sistolica_diastolica?: any;
	usar_ordem_dobras_cutaneas?: boolean;
	ordens_dobras?: any;
	modelo_evolucao_fisica?: string;
	produtos?: any;
	obrigar_campos_dobras_bioimpedancia?: boolean;
}
export class ConfiguracoesEmpresa {
	codigo?: any;
	nome?: any;
	timeZoneDefault?: any;
	keyImgEmpresa?: any;
}

export interface ProdutoAvaliacao {
	description?: string;
	disabled?: boolean;
	label?: string;
	value?: any;
	escape?: boolean;
	noSelectionOption?: boolean;
}

export interface ConfiguracoesDobras {
	codigo?: number;
	dobrasEnum?: string;
	ordem?: number;
}

export interface ConfiguracoesTreino {
	emitir_ficha_apos_vencimento_treino?: string;
	numero_impressao_ficha?: string;
	bloquear_impressao_ficha_apos_todas_execucoes?: string;
	dias_antes_vencimento?: string;
	permitir_apenas_alunos_ativos?: string;
	visualizar_mensagem_aviso?: string;
	permitir_visualizar_wod?: string;
	permitir_visualizar_cref?: string;
	permitir_visualizar_par_q_10_perguntas?: string;
	permitir_visualizar_lei_parq?: string;
	agrupamento_series_set?: boolean;
	permitir_visualizar_aviso_de_pendencias?: string;
	forcar_criar_novo_programa?: string;
}

export interface IntegracaoTreino {
	name?: string;
	titleKey?: string;
	descriptionKey?: string;
	type?: ConfigItemType;
	translator?: any;
	titleIntegration?: string;
	titleModalIntegration?: string;
	urlPathImg?: string;
	activeIntegration: boolean;
	config?: boolean;
	configMGB?: boolean;
	configMQV?: boolean;
	configSelfloops?: boolean;
	typeIntegration?: TipoIntegracao;
}

export enum TipoIntegracao {
	BIOIMPEDANCIA,
	GYMPASS,
	GUSTAVO_BORGES,
	MQV,
	SPIVI,
	TOTALPASS,
	SELFLOOPS,
}

export interface ConfiguracoesManutencao {
	aplicativo_personalizado?: boolean;
	aplicativo_personalizado_nome?: string;
	aplicativo_personalizado_url?: string;
}

export interface ConfiguracoesIa {
	permitir_criar_treino_automatizado_ia?: boolean;
	tempo_aprovacao_automatica?: number;
	tempo_maximo_revisao?: number;
	habilitar_obrigatoriedade_aprovacao_professor?: boolean;
	permitir_aluno_criar_treino_ia_app?: boolean;
}
