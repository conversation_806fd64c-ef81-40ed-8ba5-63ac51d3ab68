import { Inject, Injectable, LOCALE_ID } from "@angular/core";

import { AdmCoreApiConfig, AdmCoreApiConfigProviderBase } from "adm-core-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable()
export class AdmCoreApiConfigProviderService extends AdmCoreApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<AdmCoreApiConfig> {
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.admCoreUrl,
			authToken: this.sessionService.token,
			empresaId: this.sessionService.empresaId,
			acceptLanguage: this.locale,
		});
	}
}
