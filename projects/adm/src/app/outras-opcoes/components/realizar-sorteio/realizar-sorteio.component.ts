import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";

@Component({
	selector: "adm-realizar-sorteio",
	templateUrl: "./realizar-sorteio.component.html",
	styleUrls: ["./realizar-sorteio.component.scss"],
})
export class RealizarSorteioComponent implements OnInit {
	constructor(private router: Router) {}

	ngOnInit() {}

	voltarHome(): void {
		this.router.navigate(["adm", "outras-opcoes", "sorteios"]);
	}

	revealWinner(): void {
		this.router.navigate(["adm", "outras-opcoes", "revelar-ganhador"]);
	}
}
