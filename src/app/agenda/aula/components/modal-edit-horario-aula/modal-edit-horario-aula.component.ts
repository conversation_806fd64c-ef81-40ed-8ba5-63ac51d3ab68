import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { Ambiente, UsuarioBase } from "treino-api";
import {
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

@Component({
	selector: "pacto-modal-edit-horario-aula",
	templateUrl: "./modal-edit-horario-aula.component.html",
	styleUrls: ["./modal-edit-horario-aula.component.scss"],
})
export class ModalEditHorarioAulaComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	// horaFinalObrigatoria: boolean = true;
	edicao: boolean = false;
	podeSelecionarMaisDeUmDia: boolean = true;
	professores: Array<UsuarioBase> = [];
	ambientes: Array<Ambiente>;
	timerMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	addHorario: boolean = true;
	selectProfessores: any = [];
	selectAmbientes: any = [];

	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		responsavel: new FormControl(null, [Validators.required]),
		ambiente: new FormControl(null, [Validators.required]),
		horaInicio: new FormControl(null, [Validators.required]),
		horaFim: new FormControl(null, [Validators.required]),
		capacidade: new FormControl(null, [Validators.required]),
		limiteVagasAgregados: new FormControl(null, [Validators.required]),
		qtdeMaximaAlunoExperimental: new FormControl(null, [Validators.required]),
		diaSemana: new FormControl(null),
		dom: new FormControl(false),
		seg: new FormControl(false),
		ter: new FormControl(false),
		qua: new FormControl(false),
		qui: new FormControl(false),
		sex: new FormControl(false),
		sab: new FormControl(false),
		todosDias: new FormControl(false),
	});

	horariosData: {
		content: Array<{
			dias: string;
			horarioInicial: string;
			horarioFinal: string;
			horarios: Array<any>;
			ambientes: any[];
			professor: any;
		}>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<{
			dias: string;
			horarioInicial: string;
			horarioFinal: string;
			horarios: Array<any>;
			ambientes: any[];
			professor: any;
		}>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	constructor(
		private activeModal: NgbActiveModal,
		private notificationService: SnotifyService,
		private rest: RestService,
		private openModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initSelects();
		this.configTable();
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => this.horariosData,
			pagination: false,
			columns: [
				{
					nome: "dias",
					titulo: "Dias",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "horarioInicial",
					titulo: "Horário Inicial",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "horarioFinal",
					titulo: "Horário Final",
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					showIconFn: (row) => true,
				},
			],
		});
	}

	initSelects() {
		this.professores.forEach((item) => {
			this.selectProfessores.push({ value: item.id, name: item.nome });
		});

		this.ambientes.forEach((item) => {
			this.selectAmbientes.push({ value: item.id, name: item.nome });
		});
	}

	validaProfInativo() {
		return false;
	}

	// get _rest() {
	// 	return this.rest;
	// }

	// paramBuilder = (filter) => {
	// 	return {
	// 		filters: JSON.stringify({
	// 			quicksearchFields: ["nome"],
	// 			quicksearchValue: filter,
	// 		}),
	// 	};
	// };

	validarTodosDiasSemanaMarcados(dia: string) {
		if (
			("seg" === dia
				? !this.formGroup.get("seg").value
				: this.formGroup.get("seg").value) &&
			("ter" === dia
				? !this.formGroup.get("ter").value
				: this.formGroup.get("ter").value) &&
			("qua" === dia
				? !this.formGroup.get("qua").value
				: this.formGroup.get("qua").value) &&
			("qui" === dia
				? !this.formGroup.get("qui").value
				: this.formGroup.get("qui").value) &&
			("sex" === dia
				? !this.formGroup.get("sex").value
				: this.formGroup.get("sex").value) &&
			("sab" === dia
				? !this.formGroup.get("sab").value
				: this.formGroup.get("sab").value) &&
			("dom" === dia
				? !this.formGroup.get("dom").value
				: this.formGroup.get("dom").value)
		) {
			this.formGroup.get("todosDias").setValue(true);
		} else {
			this.formGroup.get("todosDias").setValue(false);
		}
	}

	marcarTodosDiasSemana() {
		setTimeout(() => {
			this.formGroup.get("dom").setValue(this.formGroup.get("todosDias").value);
			this.formGroup.get("seg").setValue(this.formGroup.get("todosDias").value);
			this.formGroup.get("ter").setValue(this.formGroup.get("todosDias").value);
			this.formGroup.get("qua").setValue(this.formGroup.get("todosDias").value);
			this.formGroup.get("qui").setValue(this.formGroup.get("todosDias").value);
			this.formGroup.get("sex").setValue(this.formGroup.get("todosDias").value);
			this.formGroup.get("sab").setValue(this.formGroup.get("todosDias").value);
			this.cd.detectChanges();
		}, 300);
	}

	adicionarListaHorarios() {
		if (
			(this.formGroup.get("responsavel") &&
				(this.formGroup.get("responsavel").value === undefined ||
					this.formGroup.get("responsavel").value === null)) ||
			(this.formGroup.get("ambiente") &&
				(this.formGroup.get("ambiente").value === undefined ||
					this.formGroup.get("ambiente").value === null ||
					this.formGroup.get("ambiente").value.length === 0))
		) {
			this.notificationService.error(
				"Nenhum dos itens de seleção pode estar vazio! (Responsável ou Ambiente)."
			);
		} else if (this.isHorarioNaoPreenchido()) {
			this.notificationService.error(
				"A hora inicial ou hora final não foram preenchidas corretamente."
			);
		} else if (this.isDiaSemanaNaoPreenchido()) {
			this.notificationService.error(
				"É necessário selecionar pelo menos um dia da semana para prosseguir."
			);
		} else if (!this.isHoraInicialMenorQueHoraFinal()) {
			this.notificationService.error(
				"Por favor, defina uma hora inicial anterior à hora final."
			);
		} else if (
			this.formGroup.get("capacidade") &&
			(this.formGroup.get("capacidade").value === undefined ||
				this.formGroup.get("capacidade").value === null ||
				this.formGroup.get("capacidade").value.length === 0)
		) {
			this.notificationService.error(
				"É necessário informar o número máximo de alunos para prosseguir."
			);
		} else {
			let diasString = "";
			const dto = this.dto();
			if (
				dto.codigo &&
				dto.codigo > 0 &&
				this.horariosData.content.length > 0
			) {
				this.notificationService.warning(
					"Para edição de horario existente é inválido adicionar mais de um horário, clique em salvar para continuar."
				);
			} else {
				const horarios = Array<any>();
				if (
					this.formGroup.get("todosDias") &&
					this.formGroup.get("todosDias").value
				) {
					if (dto.codigo && dto.codigo > 0) {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'todos os dias'"
						);
						return;
					} else {
						let diaSemanaNumero = 1;
						["DM", "SG", "TR", "QA", "QI", "SX", "SB"].forEach((dia) => {
							const horario = this.dto();

							horario.diaSemanaNumero = diaSemanaNumero;
							horario.diaSemana = dia;
							horarios.push(horario);
							diaSemanaNumero = diaSemanaNumero++;
						});
					}
					diasString = "Dom, Seg, Ter, Qua, Qui, Sex, Sab";
				} else {
					if (this.formGroup.get("dom") && this.formGroup.get("dom").value) {
						const horarioD = this.dto();
						if (this.validoAdicionarHorario(horarioD, horarios)) {
							diasString += ", Dom";
							horarioD.diaSemanaNumero = 1;
							horarioD.diaSemana = "DM";
							horarios.push(horarioD);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
					if (this.formGroup.get("seg") && this.formGroup.get("seg").value) {
						const horarioS = this.dto();
						if (this.validoAdicionarHorario(horarioS, horarios)) {
							diasString += ", Seg";
							horarioS.diaSemanaNumero = 2;
							horarioS.diaSemana = "SG";
							horarios.push(horarioS);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
					if (this.formGroup.get("ter") && this.formGroup.get("ter").value) {
						const horarioT = this.dto();
						if (this.validoAdicionarHorario(horarioT, horarios)) {
							diasString += ", Ter";
							horarioT.diaSemanaNumero = 3;
							horarioT.diaSemana = "TR";
							horarios.push(horarioT);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
					if (this.formGroup.get("qua") && this.formGroup.get("qua").value) {
						diasString += ", Qua";
						const horarioQA = this.dto();
						if (this.validoAdicionarHorario(horarioQA, horarios)) {
							horarioQA.diaSemanaNumero = 4;
							horarioQA.diaSemana = "QA";
							horarios.push(horarioQA);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
					if (this.formGroup.get("qui") && this.formGroup.get("qui").value) {
						const horarioQI = this.dto();
						if (this.validoAdicionarHorario(horarioQI, horarios)) {
							diasString += ", Qui";
							horarioQI.diaSemanaNumero = 5;
							horarioQI.diaSemana = "QI";
							horarios.push(horarioQI);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
					if (this.formGroup.get("sex") && this.formGroup.get("sex").value) {
						const horarioSE = this.dto();
						if (this.validoAdicionarHorario(horarioSE, horarios)) {
							diasString += ", Sex";
							horarioSE.diaSemanaNumero = 6;
							horarioSE.diaSemana = "SX";
							horarios.push(horarioSE);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
					if (this.formGroup.get("sab") && this.formGroup.get("sab").value) {
						const horarioSA = this.dto();
						if (this.validoAdicionarHorario(horarioSA, horarios)) {
							diasString += ", Sab";
							horarioSA.diaSemanaNumero = 7;
							horarioSA.diaSemana = "SB";
							horarios.push(horarioSA);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
							);
							return;
						}
					}
				}
				if (!this.horariosData.content) {
					this.horariosData.content = [];
				}
				let hrs = Array<any>();
				hrs = horarios;
				if (diasString.startsWith(",")) {
					diasString = diasString.slice(1);
					diasString = diasString.trim();
				}
				this.horariosData.content.forEach((h) => {
					if (
						diasString.includes(h.dias.trim()) &&
						((h.horarioInicial === dto.horaInicio &&
							h.horarioFinal === dto.horaFim) ||
							(dto.horaInicio >= h.horarioInicial &&
								dto.horaInicio < h.horarioFinal) ||
							(dto.horaFim > h.horarioInicial &&
								dto.horaFim <= h.horarioFinal) ||
							(dto.horaInicio <= h.horarioInicial &&
								dto.horaFim >= h.horarioFinal)) &&
						h.ambientes.filter((item1) =>
							dto.ambientes.some(
								(item2) => item1.ambiente.codigo === item2.ambiente.codigo
							)
						).length > 0 &&
						h.professor.codigo === dto.responsavel.codigo
					) {
						diasString = "";
						this.notificationService.error(
							"Existem horários que já foram adicionados!"
						);
						return;
					}
				});
				if (diasString) {
					this.horariosData.content.push({
						dias: diasString,
						horarioInicial: dto.horaInicio,
						horarioFinal: dto.horaFim,
						horarios: hrs,
						ambientes: dto.ambientes,
						professor: dto.responsavel,
					});
				}
				this.tableData.reloadData();
				if (this.horariosData.content.length > 0) {
					this.addHorario = false;
				}
				this.cd.detectChanges();
			}
		}
	}

	isHorarioNaoPreenchido(): boolean {
		return (
			this.formGroup.get("horaInicio").value == null ||
			this.formGroup.get("horaInicio").value === "" ||
			this.formGroup.get("horaFim").value == null ||
			this.formGroup.get("horaFim").value === "" ||
			this.formGroup.get("horaInicio").value.includes("_") ||
			this.formGroup.get("horaFim").value.includes("_")
		);
	}

	isDiaSemanaNaoPreenchido(): boolean {
		return !(
			this.formGroup.get("dom").value ||
			this.formGroup.get("seg").value ||
			this.formGroup.get("ter").value ||
			this.formGroup.get("qua").value ||
			this.formGroup.get("qui").value ||
			this.formGroup.get("sex").value ||
			this.formGroup.get("sab").value
		);
	}

	isHoraInicialMenorQueHoraFinal(): boolean {
		const horaInicio: string = this.formGroup.get("horaInicio").value;
		const horaFim: string = this.formGroup.get("horaFim").value;

		// separar hora e minuto
		const [horaInicioH, horaInicioM]: number[] = horaInicio
			.split(":")
			.map(Number);
		const [horaFimH, horaFimM]: number[] = horaFim.split(":").map(Number);

		// convertendo para minutos
		const inicioEmMinutos: number = horaInicioH * 60 + horaInicioM;
		const fimEmMinutos: number = horaFimH * 60 + horaFimM;

		// comparar as horas
		return inicioEmMinutos < fimEmMinutos;
	}

	dto(): any {
		const ambienteMatch = this.ambientes.find(
			(a) => a.id === this.formGroup.get("ambiente").value
		);
		const professorMatch = this.professores.find(
			(a) => a.id === this.formGroup.get("responsavel").value
		);
		const dto = {
			codigo:
				this.formGroup.get("codigo") && this.formGroup.get("codigo").value
					? this.formGroup.get("codigo").value
					: null,
			responsavel: {
				codigo: professorMatch.codigoColaborador,
				nome: professorMatch.nome,
			},
			ambiente: {
				codigo: ambienteMatch.id,
				nome: ambienteMatch.nome,
			},
			horaInicio: this.formGroup.get("horaInicio").value,
			horaFim: this.formGroup.get("horaFim").value,
			capacidade: this.formGroup.get("capacidade").value,
			limiteVagasAgregados: this.formGroup.get("limiteVagasAgregados").value,
			qtdeMaximaAlunoExperimental: this.formGroup.get(
				"qtdeMaximaAlunoExperimental"
			).value,
		};
		return dto;
	}

	validoAdicionarHorario(horario: any, horarios: any[]): boolean {
		if (horario.codigo && horario.codigo > 0 && horarios.length > 0) {
			return false;
		}
		return true;
	}

	actionClickHandler($event: any) {
		if ($event.iconName === "remove") {
			this.horariosData.content = this.horariosData.content.filter(
				(h) => h !== $event.row
			);
			this.tableData.reloadData();
			if (this.horariosData.content.length === 0) {
				this.addHorario = true;
			}
			this.cd.detectChanges();
		}
	}

	concluirEdicaoHorariosAula() {
		const horarios = Array<any>();
		this.horariosData.content.forEach((h) => {
			h.horarios.forEach((horario) => {
				horarios.push(horario);
			});
		});
		if (horarios.length > 0) {
			this.openModal.close(this.edicao ? horarios[0] : horarios);
			if (!this.edicao) {
			}
		} else {
			this.openModal.close();
		}
	}

	validarHoraFim(horaFim: string): string {
		const horaInicio = this.formGroup.get("horaInicio").value;

		// Verifica se ambos horaInicio e horaFim são válidos
		if (horaInicio && horaFim) {
			// Converte as horas no formato HH:mm para objetos Date (apenas para comparação)
			const [horaInicioHoras, horaInicioMinutos] = horaInicio
				.split(":")
				.map(Number);
			const [horaFimHoras, horaFimMinutos] = horaFim.split(":").map(Number);

			const inicio = new Date();
			inicio.setHours(horaInicioHoras, horaInicioMinutos, 0, 0);

			const fim = new Date();
			fim.setHours(horaFimHoras, horaFimMinutos, 0, 0);

			// Se horaFim for menor que horaInicio, retorna horaInicio
			if (fim < inicio) {
				return horaInicio;
			}

			// Verifica se horaFim é maior que 23:29 e ajusta para 23:59
			if (horaFimHoras > 23 || (horaFimHoras === 23 && horaFimMinutos > 29)) {
				this.formGroup.get("horaFim").setValue("23:59");
				return "23:59";
			}
		}

		// Caso contrário, retorna horaFim (sem alterações)
		return horaFim;
	}

	validarValorDigitadoVagasAgregados() {
		let limiteVagas = this.formGroup.get("limiteVagasAgregados").value;
		if (
			limiteVagas !== null &&
			limiteVagas !== undefined &&
			Number(limiteVagas) > 0
		) {
			limiteVagas = limiteVagas.toString().replace(".", "");
			limiteVagas = limiteVagas.replace(",", "");
			limiteVagas = Number(limiteVagas);
		} else {
			limiteVagas = 0;
		}

		let capacidade = this.formGroup.get("capacidade").value;
		if (
			capacidade !== null &&
			capacidade !== undefined &&
			Number(capacidade) > 0
		) {
			capacidade = capacidade.toString().replace(".", "");
			capacidade = capacidade.replace(",", "");
			capacidade = Number(capacidade);
		} else {
			capacidade = 0;
		}

		if (limiteVagas > capacidade) {
			this.formGroup.get("limiteVagasAgregados").setValue(capacidade);
		}

		let qtdeMaximaAlunoExperimental = this.formGroup.get(
			"qtdeMaximaAlunoExperimental"
		).value;
		if (
			qtdeMaximaAlunoExperimental !== null &&
			qtdeMaximaAlunoExperimental !== undefined &&
			Number(qtdeMaximaAlunoExperimental) > 0
		) {
			qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental
				.toString()
				.replace(".", "");
			qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental.replace(
				",",
				""
			);
			qtdeMaximaAlunoExperimental = Number(qtdeMaximaAlunoExperimental);
		} else {
			this.formGroup.get("qtdeMaximaAlunoExperimental").setValue(0);
		}
		if (qtdeMaximaAlunoExperimental >= capacidade) {
			this.formGroup.get("qtdeMaximaAlunoExperimental").setValue(capacidade);
		}
	}

	isBtnAddHorarioDisabled() {
		const codigo =
			this.formGroup.get("codigo") && this.formGroup.get("codigo").value
				? this.formGroup.get("codigo").value
				: null;
		return codigo && codigo > 0 && this.horariosData.content.length > 0;
	}
}
