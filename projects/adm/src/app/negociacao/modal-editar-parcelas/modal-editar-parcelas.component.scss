::ng-deep .modal-editar-parcelas .modal-dialog {
	width: 1000px !important;
	max-width: 1000px !important;
	height: 570px !important;
}

.edicao-parcelas {
	width: 100%;
	padding: 16px;

	table {
		width: 100%;

		td {
			padding: 10px 16px;
			color: #797d86;
			font-size: 12px;
			line-height: 16px;

			.parcela {
				position: relative;

				.prefix {
					position: absolute;
					left: 16px;
					color: #92959b;
					top: 1px;
					height: 100%;
					line-height: 35px;
					font-weight: 400;
					font-size: 14px;
				}

				&.dig2 {
					::ng-deep pacto-cat-form-input-number {
						input {
							padding: 0 30px 0 70px;
						}
					}
				}

				::ng-deep pacto-cat-form-input-number {
					margin: 0;

					input {
						text-align: left !important;
						padding: 0 30px 0 62px;
						line-height: 35px;
					}

					i.pct-x {
						display: none;
					}

					.nome,
					.pct-error-msg {
						height: 0;
						min-height: 0;
						margin: 0;
					}
				}
			}
		}

		.zebra-row {
			background-color: #f2f2f2;
		}
	}

	.atencao {
		background-color: #fdfdb4;
		color: #7d7d03;
		display: flex;
		align-items: center;
		padding: 10px;
		margin: 24px 0;

		i {
			margin-right: 8px;
		}
	}
}

.rodape-edicao-parcela {
	margin-top: 16px;
	width: 100%;
	display: flex;

	.inner-div {
		flex: 1;
		align-items: center;
	}

	.left {
		text-align: left;
	}

	.right {
		text-align: right;
	}

	pacto-cat-button {
		margin-left: 15px;
	}

	.total-residuo {
		margin-right: 10px;
		display: inline-flex;
		align-items: center;

		&.total {
			.verde {
				color: #04af04;
			}
		}

		.valor {
			font-weight: 500;
			margin-right: 0px;

			&.residuo {
				font-size: 22px;
			}
		}

		span {
			margin-right: 5px;
			line-height: 40px;
			height: 40px;
		}

		padding: 0 16px;
		text-align: center;
		border: 1px solid #c9cbcf;
		border-radius: 8px;
		color: #80858c;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
	}
}
