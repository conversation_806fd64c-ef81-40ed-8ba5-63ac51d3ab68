<div class="nav-aux">
	<a
		[routerLink]="['/pessoas', 'perfil-v2', matricula]"
		class="top-navigation"
		id="voltar-alunos">
		<i class="pct pct-arrow-left"></i>
		<span i18n="@@tela-do-cliente:title:gympass">TotalPass</span>
	</a>
</div>

<div>
	<pacto-cat-card-plain
		*ngIf="isCarregou() && !empresaTemConfiguracao && !alunoTemDados">
		<img class="icone-historico" src="assets/images/historico_totalpass.svg" />
		<div class="text">A academia não possui integração com o TotalPass</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain
		*ngIf="isCarregou() && empresaTemConfiguracao && !alunoTemDados">
		<img class="icone-historico" src="assets/images/historico_totalpass.svg" />
		<div class="text">
			O cliente ainda não possui integração com o TotalPass
		</div>
		<div class="botaoAutorizar">
			<pacto-cat-button
				(click)="autorizar()"
				id="pch-btn-autorizar"
				label="Autorizar"
				size="LARGE"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain *ngIf="isCarregou() && alunoTemDados">
		<pacto-relatorio
			#historicoRef
			(iconClick)="detalharLog($event)"
			[customActions]="checkinTotalPass"
			[enableZebraStyle]="true"
			[showShare]="false"
			[table]="historico"
			actionTitulo="Ação"
			i18n-tableTitle="@@tela-cliente-totalpass:table-title"
			tableTitle="Histórico de registro"></pacto-relatorio>
	</pacto-cat-card-plain>
</div>
<ng-template #columnTipo>
	<span i18n="@@tela-cliente-totalpass:tipo">Tipo</span>
</ng-template>

<ng-template #columnDate>
	<span i18n="@@tela-cliente-totalpass:date">Data registro</span>
</ng-template>

<ng-template #columnUsuario>
	<span i18n="@@tela-cliente-totalpass:usuario">Usuário</span>
</ng-template>

<ng-template #columnTempoResposta>
	<span i18n="@@tela-cliente-totalpass:tempoResposta">Tempo de resposta</span>
</ng-template>

<ng-template #columnStatus>
	<span i18n="@@tela-cliente-totalpass:status">Status</span>
</ng-template>

<ng-template #celulaSituacao let-item="item">
	<div [ngClass]="['situacao', item.status.toLowerCase()]">
		{{ item.status }}
	</div>
</ng-template>
<ng-template #checkinTotalPass>
	<pacto-cat-button
		(click)="autorizarTotalpass()"
		*ngIf="empresaTemConfiguracao"
		id="pch-btn-novo-autorizar-lista"
		label="Validar check-in"
		size="LARGE"></pacto-cat-button>
</ng-template>
