import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Cliente } from "../../../../microservices/personagem/cliente/cliente.model";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-ficha-nivel-aluno-edit-modal",
	templateUrl: "./ficha-nivel-aluno-edit-modal.component.html",
	styleUrls: ["./ficha-nivel-aluno-edit-modal.component.scss"],
})
export class FichaNivelAlunoEditModalComponent implements OnInit {
	nivelId: number;
	aluno: Cliente;
	numberMask = [/[0-9]/, /[0-9]/, /[0-9]/];

	fc = new FormGroup({
		lancamento: new FormControl(null),
		aulasRealizadas: new FormControl(0),
	});

	constructor(
		private nivelService: NivelService,
		private notify: SnotifyService,
		private modal: NgbActiveModal
	) {}

	ngOnInit() {}

	loadData(aluno, nivelId) {
		this.fc.get("lancamento").setValue(aluno.lancamento);
		this.fc.get("aulasRealizadas").setValue(aluno.aulasRealizadas);
		this.aluno = aluno;
		this.nivelId = nivelId;
	}

	salvar() {
		this.nivelService
			.alterarNivelAluno(this.nivelId, this.getDto())
			.subscribe((result) => {
				if (result === "sucesso") {
					this.modal.close({ nivelAluno: result, novo: true });
					this.notify.success("Dados do nível do aluno editados com sucesso.");
				}
			});
	}

	private getDto() {
		const dto: any = this.fc.getRawValue();
		dto.aulasRealizadas =
			dto.aulasRealizadas === undefined || dto.aulasRealizadas === ""
				? 0
				: dto.aulasRealizadas;
		dto.id = this.aluno.id;
		dto.nome = this.aluno.nome;
		dto.matricula = this.aluno.matricula;
		dto.nivelId = this.aluno.nivelId;
		return dto;
	}
}
