<table>
	<tr>
		<th>Quantidade</th>

		<th>Valor pré-pago</th>

		<th>Valor pós-pago</th>

		<th>Ações</th>
	</tr>

	<tr
		(click)="selecionar(pacote)"
		*ngFor="
			let pacote of pacotes;
			let even = even;
			let lastRow = last;
			let rowIndex = index
		"
		[ngClass]="{ 'zebra-row': even }">
		<td>
			{{ pacote.quantidade }}
		</td>
		<td>
			{{ transformMoeda(pacote.valorPrePago) }}
		</td>

		<td>
			{{ transformMoeda(pacote.valorPosPago) }}
		</td>

		<td>
			<span class="coluna-link">Selecionar pacote</span>
		</td>
	</tr>
</table>
