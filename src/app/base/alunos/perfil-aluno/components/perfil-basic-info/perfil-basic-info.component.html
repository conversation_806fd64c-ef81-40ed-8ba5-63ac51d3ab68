<div *ngIf="apresentarTela" style="padding-top: 20px; width: 100%">
	<div class="toast-beta">
		<div class="toast-text">
			<span>
				<i class="pct pct-info icone"></i>
				Nos próximos meses essa tela será descontinuada. Experimente a nova
				versão e teste as melhor<PERSON>. Você poderá nos enviar feedbacks e ajudar a
				construir um sistema melhor para o seu dia a dia.
			</span>
		</div>
	</div>
</div>

<div *ngIf="apresentarTela" class="nav-aux">
	<a
		[routerLink]="['/pessoas', 'lista-v2']"
		class="top-navigation"
		id="voltar-alunos">
		<i class="pct pct-arrow-left"></i>
		<span i18n="@@perfil-aluno-dados-pessoal:title:perfil-aluno">
			Perfil do aluno
		</span>
	</a>
	<pacto-recurso-padrao
		(acao)="novaTelaAluno()"
		*ngIf="integradoZW"
		[recurso]="recurso"
		class="recurso"></pacto-recurso-padrao>
	<div *ngIf="!integradoZW"></div>
</div>

<div *ngIf="apresentarTela" class="large-avatar">
	<pacto-cat-person-avatar
		[diameter]="118"
		[parqPositivo]="parqPositivo"
		[uri]="avatarUrl"></pacto-cat-person-avatar>

	<div
		(click)="openTimeLine()"
		class="d-flex justify-content-center btn-time-line">
		<div
			[autoClose]="'outside'"
			[ngbPopover]="popoverBody"
			class="inside-btn-time-line"
			placement="bottom"
			triggers="mouseenter:mouseleave">
			<i class="pct pct-clock"></i>
		</div>
	</div>
</div>

<div *ngIf="aluno && apresentarTela" class="right-aux">
	<div class="upper">
		<pacto-cat-person-avatar
			[diameter]="60"
			[parqPositivo]="parqPositivo"
			[uri]="aluno?.imageUri"></pacto-cat-person-avatar>

		<div
			(click)="openTimeLine()"
			class="d-flex justify-content-center btn-time-line btn-small-screen">
			<div
				[autoClose]="'outside'"
				[ngbPopover]="popoverBody"
				class="inside-btn-time-line"
				id="btn-linha-do-tempo-aluno"
				placement="bottom"
				triggers="mouseenter:mouseleave">
				<i class="pct pct-clock"></i>
			</div>
		</div>

		<div class="nome-matricula">
			<div class="nome">{{ aluno?.nome | lowercase }}</div>
			<div class="matricula" i18n="@@perfil-aluno-dados-pessoal:info:mat">
				MAT: {{ matricula }}
			</div>
		</div>
		<pacto-cat-situacao-aluno
			[situacaoAluno]="aluno?.situacaoAluno"></pacto-cat-situacao-aluno>
		<pacto-cat-situacao-contrato
			[situacaoContrato]="
				aluno?.situacaoContratoZW
			"></pacto-cat-situacao-contrato>

		<div class="actions">
			<button
				(click)="edit()"
				*ngIf="integradoZW === false"
				class="btn btn-primary btn-icon"
				title="Editar dados">
				<i class="pct pct-edit"></i>
			</button>

			<button
				(click)="resendValidationEmailHandler()"
				class="btn btn-primary btn-icon"
				title="Reenviar e-email">
				<i class="pct pct-send"></i>
			</button>

			<button
				(click)="removeHandler()"
				*ngIf="integradoZW === false"
				class="btn btn-primary btn-icon"
				title="Remover aluno">
				<i class="pct pct-trash"></i>
			</button>

			<pacto-log [url]="urlLog" id="log-tela-aluno"></pacto-log>
		</div>
	</div>

	<div class="column">
		<div class="info-bit">
			<div class="bit-title" i18n="@@perfil-aluno-dados-pessoal:info:empresa">
				Empresa
			</div>
			<div class="bit-value">{{ empresa }}</div>
		</div>
		<div class="info-bit">
			<div
				*ngIf="integradoZW"
				class="bit-title"
				i18n="@@perfil-aluno-dados-pessoal:info:professor">
				Professor ( TreinoWeb )
			</div>
			<div
				*ngIf="!integradoZW"
				class="bit-title"
				i18n="@@perfil-aluno-dados-pessoal:info:professor">
				Professor
			</div>
			<div class="bit-value">
				<pacto-cat-select-filter
					(click)="alterarProfessor()"
					[control]="professorFC"
					[endpointUrl]="getUrlProfessorAluno"
					[id]="'professor-select'"
					[labelKey]="'nome'"
					[paramBuilder]="professorSelectBuilder"
					[resposeParser]="responseParser"></pacto-cat-select-filter>
			</div>
		</div>
		<div *ngIf="!integradoZW" class="info-bit">
			<div
				class="bit-title"
				i18n="@@perfil-aluno-dados-pessoal:info:data-inclusao">
				Data inclusão
			</div>
			<div class="bit-value">
				<i class="pct pct-calendar"></i>
				{{ aluno.dataInclusao | date : "shortDate" }}
			</div>
		</div>
		<div *ngIf="integradoZW" class="info-bit precisao">
			<div class="bit-title" i18n="@@perfil-aluno-dados-pessoal:info:plano">
				Plano Atual
			</div>
			<div class="bit-value">
				<i class="pct pct-package"></i>
				{{ aluno?.planoZW?.nome ? aluno?.planoZW.nome : "-" }}
			</div>
		</div>

		<div *ngIf="apresentarBtnMQV">
			<button
				(click)="visualizarRelatorioMQV()"
				class="btn-mqv"
				i18n="@@perfil-aluno-dados-pessoal:info:mqv">
				<img src="./assets/images/mqv_icon.svg" />
				Visualizar relatório MQV
			</button>
		</div>
	</div>

	<div class="column">
		<div *ngIf="integradoZW" class="info-bit">
			<div class="bit-title" i18n="@@perfil-aluno-dados-pessoal:info:contrato">
				Vencimento do contrato
			</div>
			<div class="bit-value vencimento-contrato">
				<pacto-cat-tipo-contrato
					[tipoContrato]="aluno.contratoZW?.tipo"></pacto-cat-tipo-contrato>
				<span class="date">
					{{
						aluno.contratoZW?.vencimento
							? (aluno.contratoZW.vencimento | date : "shortDate")
							: "-"
					}}
				</span>
			</div>
		</div>
		<div class="info-bit">
			<div class="bit-title" i18n="@@perfil-aluno-dados-pessoal:info:idade">
				Idade
			</div>
			<div class="bit-value">
				<span
					*ngIf="idade >= 12"
					i18n="@@perfil-aluno-dados-pessoal:info:idade-ano">
					{ idadeAnos, plural, =1 { {{ idadeAnos }} ano } other {
					{{ idadeAnos }} anos } }
				</span>
				<span
					*ngIf="12 > idade"
					i18n="@@perfil-aluno-dados-pessoal:info:idade-mes">
					{ idade, plural, =0 { 0 meses } =1 { 1 mês } other { {{ idade }} meses
					} }
				</span>
			</div>
		</div>
		<div class="info-bit">
			<div
				class="bit-title"
				i18n="@@perfil-aluno-dados-pessoal:info:aniversario">
				Aniversário
			</div>
			<div class="bit-value">
				<span *ngIf="aluno.dataNascimento">
					{{ aluno.dataNascimento | date : "shortDate" : "UTC" }}
				</span>
				<span *ngIf="!aluno.dataNascimento">-</span>
			</div>
		</div>
		<div class="info-bit">
			<div class="bit-title" i18n="@@perfil-aluno-dados-pessoal:info:sexo">
				Sexo
			</div>
			<div class="bit-value">
				<span
					*ngIf="aluno.sexo === 'M'"
					i18n="@@perfil-aluno-dados-pessoal:info:sexo-masc">
					Masculino
				</span>
				<span
					*ngIf="aluno.sexo === 'F'"
					i18n="@@perfil-aluno-dados-pessoal:info:sexo-fem">
					Feminino
				</span>
				<span *ngIf="!aluno.sexo">-</span>
			</div>
		</div>
	</div>

	<div class="column">
		<div *ngIf="permissaoVisualizarContatoAluno" class="info-bit">
			<div class="bit-title" i18n="@@perfil-aluno-dados-pessoal:info:contatos">
				Contatos
			</div>
			<div *ngIf="!hasContact" class="bit-value">-</div>
			<div *ngIf="hasContact" class="bit-value">
				<div *ngIf="firstEmail">{{ firstEmail }}</div>
				<div *ngIf="firstCelular">
					<div>
						{{ firstCelular }}
						<i (click)="whatsappHandler()" class="pct pct-whatsapp"></i>
					</div>
					<!--          <div class="form-check">-->
					<!--            <input class="form-check-input" [formControl]="formGroup.get('utilizarNonoDigitoWp')" type="checkbox" value="" id="nono-digito-wp" (click)="utilizarNonoDigitoWpp(firstCelular)">-->
					<!--            <label class="form-check-label" for="nono-digito-wp">-->
					<!--              Utilizar 9º dígito no WhatsApp-->
					<!--            </label>-->
					<!--          </div>-->
				</div>
				<div
					(click)="verMaisHandler()"
					*ngIf="verMais"
					class="ver-mais"
					i18n="@@perfil-aluno-dados-pessoal:ver-mais">
					Ver Mais
				</div>
			</div>
		</div>
		<div class="info-bit">
			<div
				class="bit-title"
				i18n="@@perfil-aluno-dados-pessoal:info:usuario-app">
				Usuário do App
			</div>
			<div *ngIf="aluno && aluno.appUsername" class="bit-value">
				{{ aluno.appUsername }}
			</div>
			<pacto-cat-button
				(click)="createUserApp()"
				*ngIf="!aluno.appUsername"
				[icon]="'pct pct-plus-circle'"
				[id]="'btn-create-user'"
				[label]="'adicionar'"></pacto-cat-button>
		</div>
		<div class="info-bit">
			<div
				class="bit-title"
				i18n="@@perfil-aluno-dados-pessoal:info:nivel-aluno">
				Nível do Aluno
			</div>
			<div class="bit-value">
				<pacto-cat-select-filter
					(click)="alterarNivel()"
					[control]="nivelFormControl"
					[endpointUrl]="_rest.buildFullUrl('niveis')"
					[id]="'nivel-select'"
					[labelKey]="'nome'"
					[paramBuilder]="nivelSelectBuilder"
					[resposeParser]="responseParser"></pacto-cat-select-filter>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span xingling="createUserSuccess">
		Usuário criado com sucesso, as informações foram enviadas para o e-mail.
	</span>
	<span xingling="createUserError">
		Já existe um usuário cadastrado com o mesmo email.
	</span>
	<span i18n="@@perfil-aluno-dados-pessoal:action:whatsapp" xingling="whatsapp">
		Conversar por Whatsapp
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:action:reenviar-usuario"
		xingling="email">
		Reenviar e-mail
	</span>
	<span i18n="@@perfil-aluno-dados-pessoal:action:editar" xingling="editar">
		Editar
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:action:remover-aluno"
		xingling="remover">
		Remover
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:title"
		xingling="confimUserTitle">
		Reenviar e-mail de confirmação do usuário no aplicativo
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:body"
		xingling="confimUserBody">
		Deseja reenviar email de confirmação para o usuário
		{{ aluno?.appUsername }} do aplicativo ?
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:button"
		xingling="confimUserButton">
		Enviar
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:mensagem-success"
		xingling="confimUserSuccess">
		Email enviado com sucesso.
	</span>

	<span
		i18n="@@perfil-aluno-dados-pessoal:remover-title"
		xingling="removeAlunoTitle">
		Remover Aluno
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:remover-body"
		xingling="removeAlunoBody">
		Tem certeza que deseja remover esse aluno {{ aluno?.nome }} ?
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:remover-button"
		xingling="removeAlunobutton">
		Remover
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:remover-success"
		xingling="removeAlunoSuccess">
		Aluno removido com sucesso.
	</span>

	<span xingling="observacaoSucesso">Observação criada com sucesso.</span>
	<span xingling="anexoRemovidoSucesso">Anexo removido com sucesso.</span>
	<span xingling="removerAnexoTitulo">Remover Anexo.</span>
	<span xingling="removerAnexoBody">
		Tem certeza que deseja remover o anexo ?
	</span>
	<span xingling="anexoCriadoSucesso">Anexo criado com sucesso.</span>
</pacto-traducoes-xingling>

<div
	#edicaoSucesso
	[hidden]="true"
	i18n="@@perfil-aluno-dados-pessoal:info:aluno-atualizado">
	Aluno atualizado com sucesso.
</div>
<div
	#edicaoErro
	[hidden]="true"
	i18n="@@perfil-aluno-dados-pessoal:info:aluno-erro-atualizar">
	Não foi possível atualizar o aluno.
</div>

<ng-template #popoverBody>
	<div class="popover-time-line" i18n="@@perfil-aluno:linha-do-tempo">
		Linha do tempo
	</div>
</ng-template>
