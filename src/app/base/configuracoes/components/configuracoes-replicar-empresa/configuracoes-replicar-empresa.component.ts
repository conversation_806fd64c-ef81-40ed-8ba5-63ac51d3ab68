import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";

import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { OamdService, SessionService } from "sdk";
import { ConfiguracaoRedeEmpresa } from "@base-core/configuracao-replicar-empresa/configuracaoredeempresa.model";
import { TreinoApiConfiguracoesTreinoService } from "treino-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-configuracoes-replicar-empresa",
	templateUrl: "./configuracoes-replicar-empresa.component.html",
	styleUrls: ["./configuracoes-replicar-empresa.component.scss"],
})
export class ConfiguracoesReplicarEmpresaComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	dataGridConfig: PactoDataGridConfig;
	gridFilterConfig: GridFilterConfig;
	@ViewChild("celulaSituacao", { static: true }) celulaSituacao;
	@ViewChild("grid", { static: false }) grid: RelatorioComponent;
	loading: Array<string> = [];
	qtdeConfigsReplicadas = 0;
	qtdeConfigsNaoReplicados = 0;
	qtdeEmpresasRede = 0;
	empresasSelecionadas: Array<ConfiguracaoRedeEmpresa> = [];
	ConfigRedeEmpresaReplicar: ConfiguracaoRedeEmpresa[] = [];
	marcouSelecionarTodos = false;
	@Output() update: EventEmitter<any> = new EventEmitter();

	constructor(
		private oamdService: OamdService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private snotify: SnotifyService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService
	) {}

	ngOnInit() {
		this.initConfigsReplicadas();
	}

	initConfigsReplicadas() {
		this.obterListaEmpresasReplicar();
		this.initRelatorioConfig();
		this.cd.detectChanges();
	}

	async obterListaEmpresasReplicar(): Promise<ConfiguracaoRedeEmpresa[]> {
		this.ConfigRedeEmpresaReplicar = [];
		await this.oamdService
			.findEmpresasRedeReplicarConfiguracaoTreino(this.sessionService.chave)
			.subscribe((dados: ConfiguracaoRedeEmpresa[]) => {
				this.ConfigRedeEmpresaReplicar = dados;
				this.qtdeEmpresasRede = dados[0].qtdeEmpresasRede;
				this.qtdeConfigsReplicadas = dados[0].qtdeConfigsReplicadas;
				this.qtdeConfigsNaoReplicados = dados[0].qtdeConfigsNaoReplicadas;
			});
		return this.ConfigRedeEmpresaReplicar;
	}

	rowCheckHandler(itemCheck: {
		row?: ConfiguracaoRedeEmpresa;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}) {
		if (itemCheck.selectedAll) {
			this.marcouSelecionarTodos = true;
			this.grid.selectedItems = [];
			this.ConfigRedeEmpresaReplicar.map((f) => {
				this.grid.selectedItems.push(f);
			});
		} else if (itemCheck.clearAll) {
			this.marcouSelecionarTodos = false;
			this.grid.selectedItems = [];
			this.empresasSelecionadas = [];
			this.grid.reloadData();
		} else if (this.marcouSelecionarTodos) {
			let entrou = false;
			this.grid.selectedItems.forEach((item) => {
				if (
					this.grid.selectedItems.length === 2 &&
					item.chave === itemCheck.row.chaveDestino &&
					item.empresaZw === itemCheck.row.empresaZw
				) {
					entrou = true;
					this.grid.selectedItems = [];
				}
			});
			this.grid.selectedItems.forEach((item, index) => {
				if (
					item.chave === itemCheck.row.chaveDestino &&
					item.empresaZw === itemCheck.row.empresaZw
				) {
					entrou = true;
					this.grid.selectedItems.splice(index, 1);
				}
			});
			if (!entrou) {
				this.grid.selectedItems.push(itemCheck.row);
			}
		}
		this.empresasSelecionadas =
			this.grid.selectedItems.length > 0 ? this.grid.selectedItems : [];
		if (itemCheck.selectedAll) {
			this.grid.reloadData();
		}
	}

	initRelatorioConfig() {
		this.gridFilterConfig = {
			filters: [
				{
					name: "situacao",
					label: "Situação",
					type: GridFilterType.MANY,
					options: [
						{ value: "REPLICADO", label: "Replicado" },
						{ value: "NAO_REPLICADO", label: "Não replicado" },
					],
				},
			],
		};
		this.dataGridConfig = new PactoDataGridConfig({
			endpointUrl: this.oamdService.urlEmpresasRedeReplicarConfiguracaoTreino(
				this.sessionService.chave
			),
			quickSearch: true,
			exportButton: true,
			showFilters: true,
			pagination: false,
			totalRow: false,
			valueRowCheck: "nomeUnidade",
			columns: [
				{
					nome: "nomeUnidade",
					titulo: "Nome da unidade",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "chaveDestino",
					titulo: "Chave",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "mensagemSituacao",
					titulo: "Mensagem",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: false,
					celula: this.celulaSituacao,
				},
			],
		});
	}

	statusClass(planoRedeEmpresa: ConfiguracaoRedeEmpresa) {
		return this.status(planoRedeEmpresa)
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(" ", "_");
	}

	status(planoRedeEmpresa: ConfiguracaoRedeEmpresa) {
		if (this.loadingButton(planoRedeEmpresa.chaveDestino)) {
			return "REPLICANDO";
		}

		if (
			planoRedeEmpresa.mensagemSituacao === undefined ||
			planoRedeEmpresa.mensagemSituacao === null
		) {
			return "NAO REPLICADA";
		}

		if (
			planoRedeEmpresa.mensagemSituacao &&
			planoRedeEmpresa.mensagemSituacao
				.toUpperCase()
				.indexOf("REPLICADO EM") !== -1
		) {
			return "REPLICADA";
		}

		return "NAO_REPLICADA";
	}

	textoBotaoReplicar(planoRedeEmpresa: ConfiguracaoRedeEmpresa) {
		if (this.loadingButton(planoRedeEmpresa.chaveDestino)) {
			return "Replicando plano...";
		}
		if (
			this.exibirBotaoReplicar(planoRedeEmpresa) &&
			this.exibirMensagemSituacao(planoRedeEmpresa)
		) {
			return "Tentar novamente";
		} else {
			return "Replicar";
		}
	}

	exibirBotaoReplicar(planoRedeEmpresa: ConfiguracaoRedeEmpresa) {
		return (
			planoRedeEmpresa.mensagemSituacao === null ||
			planoRedeEmpresa.mensagemSituacao === undefined ||
			(planoRedeEmpresa &&
				planoRedeEmpresa.mensagemSituacao.indexOf("REPLICADO") === -1)
		);
	}

	exibirMensagemSituacao(planoRedeEmpresa: ConfiguracaoRedeEmpresa) {
		return (
			planoRedeEmpresa &&
			planoRedeEmpresa.mensagemSituacao &&
			planoRedeEmpresa.mensagemSituacao.length > 0 &&
			planoRedeEmpresa.mensagemSituacao.indexOf("Não replicado") === -1
		);
	}

	loadingButton(chave: string) {
		return this.loading.indexOf(chave) > -1;
	}

	replicarPlanosSelecionados() {
		if (this.empresasSelecionadas.length > 0) {
			this.empresasSelecionadas.forEach(
				async (emp: ConfiguracaoRedeEmpresa) => {
					await this.replicarPlano(emp.chaveDestino);
				}
			);
		} else {
			this.snotify.error("Selecione ao menos uma empresa da rede.");
			this.update.emit(true);
			this.cd.detectChanges();
		}
	}

	getChaveZW() {
		return this.sessionService.decodedApiToken.chave
			? this.sessionService.decodedApiToken.chave
			: JSON.parse(
					JSON.parse(JSON.stringify(this.sessionService.decodedApiToken))
						.content
			  ).k;
	}

	async replicarPlano(chaveDestino: string) {
		const token = this.sessionService.token;
		this.configTreinoService
			.replicarConfiguracaoRedeEmpresa(this.getChaveZW(), chaveDestino, token)
			.subscribe((r) => {
				if (r === "sucesso") {
					this.snotify.success(this.traducoes.getLabel("MENSSAGEM_SUCESSO"));
					console.log(r);
				} else {
					this.snotify.error(r);
				}
				this.obterListaEmpresasReplicar();
				this.grid.reloadData();
				this.update.emit(true);
				this.cd.detectChanges();
			});
	}
}
