import { browser, by, element } from "protractor";

export class LoginInternoPage {
	navigateTo() {
		return browser.get("/spa-login/login") as Promise<any>;
	}

	preencherCredenciais() {
		element(by.id("login-chave-input")).sendKeys(
			"2a4924bff634c9a90a1565f20c82b36f"
		);
		element(by.id("login-usuario-input")).sendKeys("pactobr");
		element(by.id("login-senha-input")).sendKeys("P4ZW20PMG");
		element(by.id("login-enter-botao")).click();
	}
}
