import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { PlataformModuleConfig, PlatformMenuItem } from "../../models";
import { LayoutNavigationService } from "../../layout-navigation.service";
import { MenuConfigService } from "../../menu/menu-config/menu-config.service";
import { PermissaoService } from "../../permissao/permissao.service";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";

@Injectable({
	providedIn: "root",
})
export class TaskbarConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([
			this.admMenuItem,
			this.crmMenuItem,
			this.finMenuItem,
			this.integracoesMenuItem,
			this.treinoMenuItem,
		]);
	}

	get admMenuItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "config-adm",
			permissaoAdm: "2.08 - Configuração do Sistema",
			permitido: this.permissaoService.temRecursoAdm("2.08"),
			route: {
				queryParams: {
					funcionalidadeNome: "CONFIG_ZW",
					windowTitle: "Configurações do Sistema",
					windowHeight: 768,
					windowWidth: 1024,
					openAsPopup: true,
				},
			},
		};

		menu.module = PlataformModuleConfig.ADM_LEGADO;
		menu.route.externalLink = this.layoutNavigationService.redirectToModule(
			PlataformModuleConfig.ADM_LEGADO,
			menu
		);

		return menu;
	}

	get treinoMenuItem(): PlatformMenuItem {
		const permissaoAcesso = new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.CONFIGURACOES_EMPRESA,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
			]
		);
		const menu: PlatformMenuItem = {
			id: "config-treino",
			permissaoTreino: permissaoAcesso,
			permitido: this.permissaoService.temPermissaoTreino(permissaoAcesso),
			route: {
				internalLink: "/config",
			},
		};

		menu.module = PlataformModuleConfig.TREINO;
		menu.route.externalLink = this.layoutNavigationService.redirectToModule(
			PlataformModuleConfig.TREINO,
			menu
		);

		return menu;
	}

	get crmMenuItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "config-crm",
			permissaoAdm: "7.06 - Configuração Sistema CRM",
			permitido: this.permissaoService.temRecursoAdm("7.06"),
			route: {
				queryParams: {
					funcionalidadeNome: "CONFIG_CRM",
					windowTitle: "Configurações do Sistema",
					windowHeight: 768,
					windowWidth: 1024,
					openAsPopup: true,
				},
			},
		};

		menu.module = PlataformModuleConfig.ADM_LEGADO;
		menu.route.externalLink = this.layoutNavigationService.redirectToModule(
			PlataformModuleConfig.ADM_LEGADO,
			menu
		);

		return menu;
	}

	get finMenuItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "config-fin",
			favoriteIdentifier: "CONFIG_FIN",
			permissaoAdm: "9.08 - Configurações do Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.08"),
			route: {
				queryParams: {
					funcionalidadeNome: "CONFIG_FIN",
					windowTitle: "Configurações do Sistema",
					windowHeight: 768,
					windowWidth: 1024,
					openAsPopup: true,
				},
			},
		};

		menu.module = PlataformModuleConfig.ADM_LEGADO;
		menu.route.externalLink = this.layoutNavigationService.redirectToModule(
			PlataformModuleConfig.ADM_LEGADO,
			menu
		);

		return menu;
	}

	get integracoesMenuItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "config-integracoes",
			permitido: true,
			route: {
				internalLink: "/config/integracoes-v2",
			},
		};

		menu.module = PlataformModuleConfig.TREINO;
		menu.route.externalLink = this.layoutNavigationService.redirectToModule(
			PlataformModuleConfig.TREINO,
			menu
		);

		return menu;
	}
}
