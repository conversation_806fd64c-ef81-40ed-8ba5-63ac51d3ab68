import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-notificacoes-aluno",
	templateUrl: "./notificacoes-aluno.component.html",
	styleUrls: ["./notificacoes-aluno.component.scss"],
})
export class NotificacoesAlunoComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	table: PactoDataGridConfig;
	@Input() alunoId: number;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("dataColumn", { static: true }) dataColumn;

	constructor(private rest: RestService) {}

	ngOnInit() {
		this.configTable();
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				`alunos/${this.alunoId}/notificacoesAluno`
			),
			quickSearch: false,
			ghostLoad: true,
			pagination: true,
			rowClick: true,
			totalRow: false,
			columns: [
				{
					nome: "gravidade",
					titulo: "",
					buscaRapida: false,
					visible: true,
					defaultVisible: true,
					celula: this.statusColumn,
				},
				{
					nome: "tipo",
					titulo: "",
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					campo: "type",
					defaultVisible: true,
				},
				{
					nome: "dataRegistro",
					titulo: "",
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					celula: this.dataColumn,
					defaultVisible: true,
				},
			],
		});
	}
}
