import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentação | Creating Components" />

# Creating Components

Antes de começar a escrever código, recomendamos que você leia nosso [guia de contribuição](./?path=/docs/documentação-contributing--page) para te ajudar a iniciar.

## Como escrever novos componentes

Crie seus componentes seguindo nossas [convenções para arquivos e pastas](./?path=/docs/documentação-project-structure--page).

- Confira o nome do componente. Os nomes dos componentes são decididos no Design System pelo time de UI/UX e eles são os mesmos para todas as plataformas. É importante seguir o nome do componente no ticket do Jira.
- Caso seu componente precise ser dividido em outros menores, é necessário criar um módulo que declara e exporta todos os componentes.
  - os sub componentes e diretivas devem ser separados por pastas. Ex: `ds3-table/component/ds3-table-cell.component` e `ds3-table/directives/ds3-table-primary.directive` 

### Passo 1: Crie o componente usando o AngularCLI

Acesse a pasta `projects/ui/src/lib`
```
cd projects/ui/src/lib
```
Crie o componente utilizando o AngularCLI
```
ng g c ds3/ds3-nome-do-componente
```

### Passo 2: Crie a documentação dos seus componentes

Todo componente deve ter um arquivo `.stories.ts` para a criação de histórias e documentação do componente no storybook.


Crie um arquivo arquivo `.notes.md` para a criação de notas em Markdown para a documentação do componente.

Uma história deve seguir o padrão abaixo:

```
import Notes from './ds3-componente.notes.md';
import { moduleMetadata } from '@storybook/angular';
import { boolean, withKnobs } from '@storybook/addon-knobs';
import { UiModule } from '../../ui.module';

const description = () => Notes;

export default {
	title: 'Design System 3 | Grupo/Componente',
	decorators: [
		moduleMetadata({
			imports: [
				UiModule
			]
		}),
		withKnobs
	],
	parameters: {
		docs: {
      extractComponentDescription: description
    }
	}
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-componente [isDisabled]="isDisabled"></ds3-componente>
		</div>
    `,
	props: {
		isDisabled: boolean('Is disabled', false),
	},
});

```
### Passo 3: Utilizando o UI Sandbox


- A método *recomendado* para [organizar pastas](./?path=/docs/documentação-project-structure--page#convencoes-de-pastas)
- O método *requerido* para [nomear arquivos](./?path=/docs/documentação-project-structure--page#convenções-de-nomes-de-arquivo)
- O método *requerido* para [criar commits](./?path=/docs/documentação-commiting--page)
- O método *requerido* para [criar componentes](./?path=/docs/documentação-creating-components--page)
- O método *recomendado* de escrever código

Leia cada uma das documentações acima para uma documentação detalhada sobre cada tópico.