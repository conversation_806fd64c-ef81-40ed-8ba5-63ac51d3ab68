import { Component, Input, OnInit, TemplateRef } from "@angular/core";

@Component({
	selector: "adm-modal-notificacao",
	templateUrl: "./modal-notificacao.component.html",
	styleUrls: ["./modal-notificacao.component.scss"],
})
export class ModalNotificacaoComponent implements OnInit {
	@Input() public type?: string;
	@Input() public title?: string;
	@Input() public subtitle?: string;
	@Input() public body?: string | TemplateRef<any>;
	@Input() public actions?: [];

	constructor() {}

	ngOnInit() {}

	public isTemplate(body): boolean {
		return typeof body !== "string" && typeof body !== undefined;
	}
}
