import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	HostListener,
	OnInit,
	Renderer2,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ApiResponseList, ClientDiscoveryService, SessionService } from "sdk";
import { CatTableEditableComponent } from "../../../../ui/src/lib/components/cat-editable-table/cat-table-editable.component";
import { AdmRestService } from "../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";
import {
	DialogService,
	LoaderService,
	PactoModalSize,
	PactoDataGridConfig,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	DialogAutorizacaoAcessoComponent,
} from "ui-kit";
import {
	AdmCoreApiNegociacaoService,
	AdmCoreApiVendaAvulsaService,
	VendaAvulsa,
} from "adm-core-api";
import { VendaAvulsaService } from "./venda-avulsa.service";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { DecimalPipe } from "@angular/common";
import { Observable } from "rxjs";
import { VendaAvulsaInfoComponent } from "./venda-avulsa-info/venda-avulsa-info.component";
import { ModalNumeroParcelasComponent } from "./modal-numero-parcelas/modal-numero-parcelas.component";
import { ModalEscolhaConsumidorVendaAvulsaComponent } from "./modal-escolha-consumidor-venda-avulsa/modal-escolha-consumidor-venda-avulsa.component";
import { ModalPacotePersonalComponent } from "./modal-pacote-personal/modal-pacote-personal.component";
import { ModalEnviarLinkPagamentoComponent } from "../negociacao/modal-enviar-link-pagamento/modal-enviar-link-pagamento.component";
import { EmailRecorrencia } from "adm-ms-api";
import { map } from "rxjs/operators";
import { MatDialog } from "@angular/material";

declare var moment;
@Component({
	selector: "adm-venda-avulsa",
	templateUrl: "./venda-avulsa.component.html",
	styleUrls: ["./venda-avulsa.component.scss"],
})
export class VendaAvulsaComponent implements OnInit, AfterViewInit {
	@ViewChild("tableProdutoComponent", { static: false })
	tableItensComponent: CatTableEditableComponent;
	moeda = "R$";
	tableProduto: PactoDataGridConfig;
	barcodeData: string = "";

	@ViewChild("valorTitle", { static: true }) valorTitle;
	@ViewChild("valorColumn", { static: true }) valorColumn;
	@ViewChild("opcoesColumn", { static: true }) opcoesColumn;
	@ViewChild("qtdColumn", { static: true }) qtdColumn;
	@ViewChild("opcoesTitle", { static: true }) opcoesTitle;
	@ViewChild("valorFinalColumn", { static: true }) valorFinalColumn;
	@ViewChild("descontoColumn", { static: true }) descontoColumn;
	@ViewChild("pontosColumn", { static: true }) pontosColumn;
	@ViewChild("fixo", { static: false }) fixo: ElementRef;
	@ViewChild("fluxo", { static: false }) fluxo: ElementRef;
	@ViewChild("info1", { static: false }) info1: VendaAvulsaInfoComponent;
	@ViewChild("info2", { static: false }) info2: VendaAvulsaInfoComponent;
	recurso = "VENDA_AVULSA";
	recursoPadraoEmpresa: boolean = true;
	wait = false;
	clienteColaborador = true;
	descontosGerais;
	descontoSelecionado;
	linkPagamentoDesabilitado = false;
	descontoGeralForm = new FormControl();
	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			quickSearch: term,
		};
	};

	constructor(
		private router: Router,
		private modal: DialogService,
		private service: VendaAvulsaService,
		private notificationService: SnotifyService,
		private renderer: Renderer2,
		private sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		private admRestService: AdmRestService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef,
		private telaClienteService: AdmLegadoTelaClienteService,
		protected layoutNavigationService: LayoutNavigationService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private vendaAvulsaService: AdmCoreApiVendaAvulsaService,
		private loaderService: LoaderService,
		private route: ActivatedRoute,
		private matDialog: MatDialog
	) {}

	get _rest() {
		return this.admRestService;
	}

	public descontos() {
		this.vendaAvulsaService.descontosGerais().subscribe((desconto) => {
			if (desconto && desconto.length > 0) {
				this.descontosGerais = [{ codigo: 0, descricao: "-" }, ...desconto];
			} else {
				this.descontosGerais = [];
			}
		});
	}

	ngOnInit() {
		this.carregarRecursoPadraoEmpresa();
		this.validarLinkPagamento();
		setTimeout(() => {
			this.init();
		}, 200);
	}

	abrirEscolherCliente() {
		this.service.updateCliente({});
		const modal = this.modal.open(
			"Clientes / Colaboradores",
			ModalEscolhaConsumidorVendaAvulsaComponent,
			PactoModalSize.LARGE
		);
		modal.result.then(
			(cli) => {
				this.clienteColaborador = cli.clienteColaborador;
				this.service.getConfig().aluno = cli;
				this.service.updateCliente(cli);
				this.validarLinkPagamento();
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	ngAfterViewInit() {}

	carregarRecursoPadraoEmpresa() {
		this.admCoreApiNegociacaoService
			.recursoPadraoEmpresa(this.recurso)
			.subscribe(
				(response) => {
					this.recursoPadraoEmpresa = response;
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
					this.recursoPadraoEmpresa = false;
				}
			);
	}

	validarLinkPagamento() {
		if (
			this.service.getConfig() &&
			this.service.getConfig().aluno &&
			this.service.getConfig().aluno.codigo
		) {
			this.telaClienteService
				.validarLinkPagamento(
					this.sessionService.chave,
					this.service.getConfig().aluno.codCliente
				)
				.subscribe(
					(response) => {
						if (
							response.meta &&
							response.meta.error &&
							response.meta.error === "Falta de convênio"
						) {
							this.linkPagamentoDesabilitado = true;
						} else {
							this.linkPagamentoDesabilitado = false;
						}
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						const error = httpErrorResponse.error;
						if (
							error &&
							error.meta &&
							error.meta.error === "Falta de convênio"
						) {
							this.linkPagamentoDesabilitado = true;
						} else {
							this.linkPagamentoDesabilitado = false;
						}
						this.cd.detectChanges();
					}
				);
		}
	}

	limpar() {
		this.service.resetConfig();
		this.service.updateCliente(null);
		this.tableItensComponent.rawData = [];
		this.calcularValor();
		this.cd.detectChanges();
		this.init();
	}

	init() {
		this.route.params.subscribe((params) => {
			if (params.id) {
				this.vendaAvulsaService.cliente(params.id).subscribe((result) => {
					if (result && result.length > 0) {
						this.service.getConfig().aluno = result[0];
						this.service.updateCliente(result[0]);
						this.validarLinkPagamento();
						this.cd.detectChanges();
					}
				});
			} else {
				this.abrirEscolherCliente();
			}
		});
		this.initTable();
		this.descontos();
		setTimeout(() => {
			this.addNew();
			this.onScroll(null);
		}, 500);
	}

	subLabelFn = (item: any) => {
		return item.tipo;
	};

	avatar = (item: any) => {
		return item.urlFoto;
	};

	voltarHome(): void {
		window.history.back();
	}

	onScroll(event): void {
		if (this.fluxo) {
			const div1Visible = this.isElementVisible(this.fluxo.nativeElement);
			if (div1Visible) {
				this.renderer.addClass(this.fixo.nativeElement, "hidden");
			} else {
				this.renderer.removeClass(this.fixo.nativeElement, "hidden");
			}
		}
	}

	isElementVisible(element: HTMLElement): boolean {
		const rect = element.getBoundingClientRect();
		const windowHeight =
			window.innerHeight || document.documentElement.clientHeight;
		const windowWidth =
			window.innerWidth || document.documentElement.clientWidth;

		return (
			rect.top >= 0 &&
			rect.left >= 0 &&
			rect.bottom <= windowHeight &&
			rect.right <= windowWidth
		);
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	aplicarDescontoGeral() {
		this.descontosGerais.forEach((desconto) => {
			if (desconto.codigo === parseInt(this.descontoGeralForm.value, 10)) {
				const valorFinal = this.service.getConfig().valorFinal
					? this.service.getConfig().valorFinal
					: 0.0;
				if (desconto.tipo === "VA" && desconto.valor > valorFinal) {
					this.notificationService.warning(
						"O valor do desconto não pode ser maior que o valor total dos produtos."
					);
					return;
				}
				this.descontoSelecionado = desconto;
				this.limparDescontosIndividuais();
			}
		});
		this.calcularValor();
		this.atualizarColunasMostrar();
		this.tableItensComponent.detectChanges();
	}

	limparDescontosIndividuais() {
		this.tableItensComponent.rawData.forEach((item) => {
			item.descontoFc = new FormControl(0.0);
			item.descontoPadrao = { codigo: 0, descricao: "-" };
			item.desconto = 0.0;
		});
	}

	limparDescontoGeral() {
		this.descontoSelecionado = null;
		this.descontoGeralForm = new FormControl(0);
		this.calcularValor();
		this.atualizarColunasMostrar();
		this.tableItensComponent.detectChanges();
	}

	get descontoNaoSelecionado() {
		return (
			!this.descontoGeralForm ||
			!this.descontoGeralForm.value ||
			this.descontoGeralForm.value === "0" ||
			this.descontoGeralForm.value === 0
		);
	}

	public initTable() {
		this.tableProduto = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				descricao: new FormControl(""),
				descontoPadrao: new FormControl(),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "descricao",
					titulo: "Produto",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					width: "370px",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					objectAttrLabelName: "descricao",
					endpointUrl: this.admRestService.buildFullUrlAdmCore("produtos"),
					selectParamBuilder: (term) => {
						return {
							quickSearch: term,
							produtosAdicionados: this.produtosAdicionados,
						};
					},
					selectOptionChange: (option, form, row) => {
						if (option.tipoProduto === "CP") {
							if (
								!this.clienteColaborador ||
								this.service.getConfig().aluno.tipo === "cliente"
							) {
								this.notificationService.warning(
									"Produto Crédito de personal só deve ser vendido para personais."
								);
								form.get("descricao").setValue({ codigo: 0, descricao: "-" });
								return;
							}
							if (
								option.descricao.toLowerCase().includes("interno") &&
								!this.service.getConfig().aluno.tipoColaborador.includes("PI,")
							) {
								this.notificationService.warning(
									"Produto CRÉDITO DE PERSONAL INTERNO só pode ser vendido para personal interno."
								);
								form.get("descricao").setValue({ codigo: 0, descricao: "-" });
								return;
							}
							if (
								option.descricao.toLowerCase().includes("externo") &&
								!this.service.getConfig().aluno.tipoColaborador.includes("PE,")
							) {
								this.notificationService.warning(
									"Produto CRÉDITO DE PERSONAL EXTERNO só pode ser vendido para personal externo."
								);
								form.get("descricao").setValue({ codigo: 0, descricao: "-" });
								return;
							}
						}
						form.get("descontoPadrao").setValue({ codigo: 0, descricao: "-" });
						row.desconto = 0.0;
						row.descontoFc = new FormControl(0.0);
						row.qtdFC = new FormControl(1);
						row.qtdFC.valueChanges.subscribe((value) => {
							const qtd = parseInt(value, 10);
							row.valorFinal = row.valor * qtd - row.desconto;
							row.quantidade = { id: qtd, l: "" + qtd };
							this.aplicarDesconto(row);
							this.calcularValor();
							this.cd.detectChanges();
						});
						row.quantidade = { id: 1, l: "1" };
						row.valor = option.valorFinal;
						row.valorFinal = option.valorFinal;
						row.pontos = option.pontos;
						row.tipoProduto = option.tipoProduto;
						row.descricao = option;
						this.onProdutoChange(option, row, form);
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
				},
				{
					nome: "opcao",
					titulo: this.opcoesTitle,
					celula: this.opcoesColumn,
					visible: this.algumCreditoPersonal,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "opcao",
					titulo: this.opcoesTitle,
					celula: this.opcoesColumn,
					visible: this.algumCreditoPersonal,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "quantidade",
					width: "185px",
					titulo: "Qtd.",
					celula: this.qtdColumn,
					visible: true,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "valor",
					titulo: this.valorTitle,
					celula: this.valorColumn,
					visible: true,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "descontoPadrao",
					width: "180px",
					titulo: "Desconto padrão",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					classes: ["coluna-centro"],
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					objectAttrLabelName: "descricao",
					isDisabled: (row) => this.isDescontoDisabled(row),
					inputSelectData: [],
					showSelectFilter: false,
					showAddSelectBtn: false,
					selectOptionChange: (option, form, row) => {
						const valorSemDesconto = row.valor * row.quantidade.id;
						if (!option.codigo || option.codigo === 0) {
							row.descontoPadrao = null;
							row.valorFinal = valorSemDesconto;
							this.aplicarDesconto(row);
						} else {
							row.desconto = 0.0;
							row.descontoFc = new FormControl(0.0);
							row.descontoPadrao = option;
							this.calcularValor();
							this.cd.detectChanges();
						}
					},
				},
				{
					nome: "desconto",
					titulo: "Desconto extra",
					visible: true,
					celula: this.descontoColumn,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "valorFinal",
					titulo: "Valor total",
					celula: this.valorFinalColumn,
					visible: true,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
			],
		});
		this.cd.detectChanges();
	}

	isDescontoDisabled(row) {
		return (
			row.descricao &&
			row.descricao.codigo &&
			row.descricao.codigo > 0 &&
			(!row.descontos || row.descontos.length === 0)
		);
	}

	atualizarColunasMostrar() {
		const opcaoColumn = this.tableProduto.columns.find(
			(column) => column.nome === "opcao"
		);
		opcaoColumn.visible = this.algumCreditoPersonal;

		const descontoPadrao = this.tableProduto.columns.find(
			(column) => column.nome === "descontoPadrao"
		);
		descontoPadrao.visible = !this.descontoSelecionado;

		const desconto = this.tableProduto.columns.find(
			(column) => column.nome === "desconto"
		);
		desconto.visible = !this.descontoSelecionado;
		this.cd.detectChanges();
	}

	get algumCreditoPersonal(): boolean {
		let creditoPersonal = false;
		this.tableItensComponent.rawData.forEach((produto) => {
			if (produto.tipoProduto === "CP") {
				creditoPersonal = true;
			}
		});
		return creditoPersonal;
	}

	transformMoeda(v) {
		v = v ? v : 0.0;
		return this.moeda + " " + this.decimalPipe.transform(v, "1.2-2");
	}

	clearListDesconto(): void {
		const colunaDesconto = this.tableProduto.columns.find(
			(v) => v.nome === "descontoPadrao"
		);
		colunaDesconto.inputSelectData = [{ codigo: 0, descricao: "-" }];
	}

	private onProdutoChange(option, row, form: FormGroup) {
		const colunaDesconto = this.tableProduto.columns.find(
			(v) => v.nome === "descontoPadrao"
		);
		this.vendaAvulsaService
			.descontos(option.tipoProduto)
			.subscribe((descontos) => {
				row.descontos = descontos;
				if (descontos && descontos.length > 0) {
					colunaDesconto.inputSelectData = [
						{ codigo: 0, descricao: "-" },
						...descontos,
					];
					form.get("descontoPadrao").enable();
					this.cd.detectChanges();
				} else {
					form.get("descontoPadrao").disable();
				}
			});
		if (option.tipoProduto === "CP") {
			this.vendaAvulsaService
				.pacotes(row.descricao.codigo)
				.subscribe((pacotes) => {
					if (pacotes && pacotes.length > 0) {
						row.pacotes = pacotes;
						this.selecionarPacote(row);
					}
				});
		}
		this.atualizarColunasMostrar();
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		const pode =
			row.descricao && row.descricao.codigo && row.descricao.codigo > 0;
		if (pode) {
			this.clearListDesconto();
		}
		return pode;
	}

	showDelete(row, isAdd): boolean {
		return true;
	}

	editProduto(event) {
		this.cd.detectChanges();
	}

	confirmProduto(event) {
		this.calcularValor();
	}

	calcularValor() {
		let valorProdutos = 0.0;
		let descontos = 0.0;
		let valor = 0.0;
		this.tableItensComponent.rawData.forEach((item) => {
			if (item.valor) {
				const valorSemDesconto = item.valor * item.quantidade.id;
				valorProdutos = valorProdutos + valorSemDesconto;
				const descontoPadrao = item.descontoPadrao;
				if (descontoPadrao && descontoPadrao.tipo === "PE") {
					const percentualDecimal = descontoPadrao.valor / 100.0;
					const valorDescontado =
						Math.round(valorSemDesconto * percentualDecimal * 100) / 100;
					item.valorFinal = valorSemDesconto - valorDescontado;
					descontos = descontos + valorDescontado;
				} else if (descontoPadrao && descontoPadrao.codigo > 0) {
					item.valorFinal = valorSemDesconto - descontoPadrao.valor;
					descontos = descontos + descontoPadrao.valor;
				} else {
					item.valorFinal = valorSemDesconto - item.desconto;
					descontos = descontos + item.desconto;
				}
				valor = valor + item.valorFinal;
			}
		});
		if (this.descontoSelecionado && this.descontoSelecionado.tipo === "PE") {
			const percentualDecimal = this.descontoSelecionado.valor / 100.0;
			const valorDescontado =
				Math.round(valorProdutos * percentualDecimal * 100) / 100;
			descontos = descontos + valorDescontado;
			valor = valor - valorDescontado;
		} else if (
			this.descontoSelecionado &&
			this.descontoSelecionado.tipo === "VA" &&
			this.descontoSelecionado.valor > valor
		) {
			this.descontoSelecionado = null;
			this.cd.detectChanges();
		} else if (this.descontoSelecionado) {
			descontos = descontos + this.descontoSelecionado.valor;
			valor = valor - this.descontoSelecionado.valor;
			this.cd.detectChanges();
		}
		this.service.getConfig().valorFinal = valor;
		this.service.getConfig().descontoTotal = descontos;
		this.service.getConfig().valorProduto = valorProdutos;
	}

	removeProduto(event) {
		if (this.tableItensComponent.rawData) {
			this.tableItensComponent.rawData.splice(event.index, 1);
		}
		this.calcularValor();
	}

	isEditingOrAdding($event: boolean) {}

	aplicarDesconto(item): void {
		const desconto = item.descontoFc.value;
		const valorSemDesconto =
			Math.round(item.valor * item.quantidade.id * 100) / 100;
		if (desconto > valorSemDesconto) {
			this.notificationService.warning(
				"O valor do desconto não pode ser maior do que o valor do produto"
			);
		} else {
			item.descontoAutorizado = false;
			item.desconto = desconto;
			item.valorFinal = valorSemDesconto - item.desconto;
			this.calcularValor();
			this.cd.detectChanges();
		}
	}

	selecionarPacote(item): void {
		const modalPacote = this.modal.open(
			"Pacotes de crédito personal",
			ModalPacotePersonalComponent
		);
		modalPacote.componentInstance.produto = item.descricao.codigo;
		modalPacote.componentInstance.pacotes = item.pacotes;
		modalPacote.result.then(
			(pacote) => {
				item.pacoteEscolhido = pacote;
				item.quantidade = { id: pacote.quantidade, l: "" + pacote.quantidade };
				if (
					this.service.getConfig().aluno.tipoCompraCredito &&
					this.service.getConfig().aluno.tipoCompraCredito === "pos"
				) {
					item.valor = pacote.valorPosPago / pacote.quantidade;
					item.valorFinal = pacote.valorPosPago - item.desconto;
				} else {
					item.valor = pacote.valorPrePago / pacote.quantidade;
					item.valorFinal = pacote.valorPrePago - item.desconto;
				}
				this.aplicarDesconto(item);
				this.calcularValor();
			},
			() => {}
		);
	}

	autorizarDesconto(item): void {
		if (
			!item.descricao ||
			!item.descricao.codigo ||
			item.descricao.codigo === 0
		) {
			this.notificationService.warning(
				"Selecione um produto antes de aplicar o desconto."
			);
			return;
		}
		if (
			this.sessionService.loggedUser.pedirSenhaFuncionalidade &&
			this.sessionService.loggedUser.pedirSenhaFuncionalidade === true
		) {
			const modalConfirmacao: any = this.matDialog.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);
			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				result.modal.close();
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						"DescontoVendaAvulsa",
						"2.38 - Desconto em produto de Venda Avulsa",
						this.sessionService.empresaId
					)
					.subscribe(
						(response: any) => {
							item.descontoAutorizado = true;
							this.cd.detectChanges();
						},
						(error) => {
							this.notificationService.error(error.error.meta.message);
						}
					);
			});
		} else {
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.sessionService.chave,
					this.sessionService.loggedUser.id,
					this.sessionService.empresaId,
					"DescontoVendaAvulsa",
					"2.38 - Desconto em produto de Venda Avulsa"
				)
				.subscribe(
					(response: any) => {
						item.descontoAutorizado = true;
						this.cd.detectChanges();
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		}
	}

	montarDto(): VendaAvulsa {
		return {
			tipo: 0,
			colaborador:
				this.service.getConfig().aluno.tipo &&
				this.service.getConfig().aluno.tipo === "colaborador",
			nomeComprador: this.clienteColaborador
				? ""
				: this.service.getConfig().aluno.nome,
			pessoa: this.clienteColaborador
				? this.service.getConfig().aluno.codigo
				: 0,
			lancamento: this.service.getConfig().dataLancamento,
			parcelas: this.service.getConfig().nrParcelas,
			primeiraParcela: this.service.getConfig().dataParcela,
			descontoGeral: this.descontoSelecionado
				? this.descontoSelecionado.codigo
				: 0,
			itens: this.itens,
		};
	}

	get tipoCliente(): boolean {
		return (
			this.service.getConfig() &&
			this.service.getConfig().aluno &&
			this.service.getConfig().aluno.tipo &&
			this.service.getConfig().aluno.tipo === "cliente"
		);
	}

	get valorFinal(): number {
		return Math.round(this.service.getConfig().valorFinal * 100) / 100;
	}

	vender() {
		if (this.wait === false) {
			this.acao("vender");
		}
	}

	receber() {
		if (this.wait === false) {
			this.acao("receber");
		}
	}

	concluir() {
		if (this.wait === false) {
			this.acao("concluir");
		}
	}

	enviar() {
		if (this.wait === false) {
			this.acao("enviar");
		}
	}

	acao(acao_) {
		this.autoClickSave();
		if (
			this.clienteColaborador &&
			(!this.service.getConfig().aluno ||
				!this.service.getConfig().aluno.codigo ||
				this.service.getConfig().aluno.codigo === 0)
		) {
			this.notificationService.warning("Informe o comprador");
			this.abrirEscolherCliente();
			return;
		}
		if (
			!this.clienteColaborador &&
			(!this.service.getConfig().aluno ||
				!this.service.getConfig().aluno.nome ||
				this.service.getConfig().aluno.nome === "")
		) {
			this.notificationService.warning("Informe o comprador");
			this.abrirEscolherCliente();
			return;
		}
		const itens = this.itens;
		if (!itens || itens.length === 0) {
			this.notificationService.warning("Adicione um produto");
			return;
		}
		if (
			this.sessionService.loggedUser.pedirSenhaFuncionalidade &&
			this.sessionService.loggedUser.pedirSenhaFuncionalidade === true
		) {
			this.autorizarVenda(acao_);
		} else {
			this.validarFuncionalidade(acao_);
		}
	}

	autorizarVenda(receber): void {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			modalConfirmacao.componentInstance.disable();
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"CaixaEmAberto",
					"2.44 - Operação - Caixa em Aberto",
					this.sessionService.empresaId
				)
				.subscribe(
					(response: any) => {
						this.gravarVenda(receber);
						result.modal.close();
					},
					(error) => {
						modalConfirmacao.componentInstance.enable();
						this.cd.detectChanges();
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	validarFuncionalidade(acao): void {
		this.wait = true;
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.id,
				this.sessionService.empresaId,
				"CaixaEmAberto",
				"2.44 - Operação - Caixa em Aberto"
			)
			.subscribe(
				(response: any) => {
					this.gravarVenda(acao);
				},
				(error) => {
					this.notificationService.error(error.error.meta.message);
					this.wait = false;
				}
			);
	}

	gravarVenda(acao) {
		this.vendaAvulsaService.save(this.montarDto()).subscribe(
			(venda) => {
				if (venda.error) {
					this.notificationService.error(venda.error);
					this.wait = false;
					return;
				}
				const id = venda.venda;
				this.notificationService.success("Venda efetuada com sucesso!");
				if (acao === "enviar") {
					this.compartilharLink(venda.whatsapp);
					return;
				}
				if (acao === "concluir") {
					this.limpar();
					return;
				}
				this.loaderService.initForce();
				this.cd.detectChanges();
				this.clientDiscoveryService
					.linkZw(
						this.sessionService.usuarioOamd,
						this.sessionService.empresaId
					)
					.subscribe((result) => {
						if (acao === "receber") {
							window.open(
								result + "&urlRedirect=pagamentoVenda_" + id,
								"_self"
							);
						} else {
							const nome = this.service.getConfig().aluno.nome;
							window.open(
								result + "&urlRedirect=caixaEmAberto_" + nome,
								"_self"
							);
						}
					});
			},
			(httpErrorResponse) => {
				this.wait = false;
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				} else {
					this.notificationService.error(
						"Ocorreu um erro inesperado, tente novamente."
					);
				}
			}
		);
	}

	resetAll() {
		this.wait = false;
		this.loaderService.stopForce();
		this.limpar();
	}

	compartilharLink(telefone) {
		this.telaClienteService
			.linkCartao(
				this.sessionService.chave,
				this.service.getConfig().aluno.codCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.id,
				"pagamento",
				null,
				null,
				1
			)
			.subscribe(
				(response) => {
					let text = `*Seu link para cadastrar o cartão de crédito. ${this.sessionService.currentEmpresa.nome}.*\n`;
					text += response.content;
					const target =
						"https://api.whatsapp.com/send?phone=" +
						"55" +
						(telefone ? telefone.replace(/[()]/g, "") : "") +
						"&text=" +
						encodeURI(text);
					const modal = this.modal.open(
						"Compartilhar link de pagamento",
						ModalEnviarLinkPagamentoComponent,
						PactoModalSize.MEDIUM,
						"modal-opcoes-avancadas"
					);
					modal.componentInstance.config = {
						whatsapp: target,
						link: response.content,
					};
					modal.result.then(
						(obs) => {
							this.resetAll();
						},
						() => {
							this.resetAll();
						}
					);
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message, {
							timeout: 4000,
						});
					} else {
						this.notificationService.error(
							`Desculpe, ocorreu um erro ao carregar link de cadastro de cartão.`,
							{ timeout: 4000 }
						);
					}
				}
			);
	}

	get itens() {
		const itens = [];
		this.tableItensComponent.rawData.forEach((item) => {
			if (
				item.descricao &&
				item.descricao.codigo &&
				item.descricao.codigo > 0
			) {
				itens.push({
					qtd: item.quantidade.id,
					pontos: item.pontos,
					valorParcial: item.valorFinal,
					descontoManual: item.desconto,
					descontoPadrao: item.descontoPadrao.codigo,
					precoProduto: item.valor,
					pacoteEscolhido: item.pacoteEscolhido,
					codigoProduto: item.descricao.codigo,
					descricaoProduto: item.descricao.descricao,
				});
			}
		});
		return itens;
	}

	get produtosAdicionados() {
		let itens = "";
		this.tableItensComponent.rawData.forEach((item) => {
			if (item.descricao && item.descricao.codigo) {
				itens = itens + item.descricao.codigo + ",";
			}
		});
		return itens;
	}

	addNew() {
		this.tableItensComponent.addNewLine();
	}

	novoProduto() {
		let algumSemProduto: boolean = false;
		this.tableItensComponent.rawData.forEach((item) => {
			if (
				!item.descricao ||
				!item.descricao.codigo ||
				item.descricao.codigo === 0
			) {
				algumSemProduto = true;
			}
		});
		if (algumSemProduto) {
			this.notificationService.warning(
				"Selecione um produto antes de adicionar outros."
			);
		} else {
			this.autoClickSave();
			this.addNew();
		}
	}

	autoClickSave() {
		const elementsByClassName = document.getElementsByClassName("pct-save");
		const array = Array.from(elementsByClassName);
		for (const save of array) {
			if (save.id && save.id.includes("confirm-table-produto")) {
				save.dispatchEvent(
					new MouseEvent("click", {
						bubbles: true,
						cancelable: true,
						view: window,
					})
				);
			}
		}
	}

	acessarVersaoAntiga() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe(
				(result) => {
					result += `&urlRedirect=uriVendaAvulsa`;
					window.open(result, "_self");
				},
				(httpResponseError) => {
					this.cd.detectChanges();
					if (
						httpResponseError.error.meta.message ===
						"Erro ao recuperar usuário!"
					) {
						const chave = this.sessionService.chave;
						const loginUrl =
							this.clientDiscoveryService.getUrlMap().loginAppUrl;
						location.replace(`${loginUrl}/${chave}`);
					} else {
						this.notificationService.error(
							"Ocorreu um erro ao tentar acessar a versão antiga! Tente novamente em isntantes!"
						);
					}
				}
			);
	}

	openFeedbackForm() {
		window.open("https://forms.gle/HcyhjDcMTCpLaiBw7", "_blank");
	}

	get dataLancamento(): string {
		return moment(this.service.getConfig().dataLancamento).format("DD/MM/yyyy");
	}

	get dataPrimeiraParcela(): string {
		return moment(this.service.getConfig().dataParcela).format("DD/MM/yyyy");
	}

	get nrParcelas(): number {
		return this.service.getConfig().nrParcelas
			? this.service.getConfig().nrParcelas
			: 1;
	}

	abrirDataLancamento() {
		const modal = this.modal.open(
			"Configurações de venda",
			ModalNumeroParcelasComponent,
			PactoModalSize.MEDIUM,
			"modal-opcoes-avancadas"
		);
		modal.componentInstance.contrato = false;
		modal.componentInstance.lancamento.setValue(
			this.service.getConfig().dataLancamento
		);
		modal.componentInstance.dataParcela.setValue(
			this.service.getConfig().dataParcela
		);
		modal.componentInstance.nrFC.setValue(this.service.getConfig().nrParcelas);
		modal.result.then((configs) => {
			this.service.getConfig().dataLancamento = configs.dataLancamento;
			this.service.getConfig().nrParcelas = configs.nr;
			this.service.getConfig().dataParcela = configs.dataParcela;
			this.cd.detectChanges();
		});
	}

	abrirAutorizacaoEditarData(): void {
		this.abrirAutorizacao("editar-data");
	}

	abrirAutorizacao(acao): void {
		if (
			this.sessionService.loggedUser.pedirSenhaFuncionalidade &&
			this.sessionService.loggedUser.pedirSenhaFuncionalidade === true
		) {
			const modalConfirmacao: any = this.matDialog.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);
			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						"DataVendaAvulsa",
						"2.39 - Alterar data de Venda Avulsa",
						this.sessionService.empresaId
					)
					.subscribe(
						(response: any) => {
							result.modal.close();
							switch (acao) {
								case "editar-data":
									this.service.getConfig().usuarioDataLancamento =
										response.content;
									this.abrirDataLancamento();
									break;
							}
						},
						(error) => {
							this.notificationService.error(error.error.meta.message);
						}
					);
			});
		} else {
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.sessionService.chave,
					this.sessionService.loggedUser.id,
					this.sessionService.empresaId,
					"DataVendaAvulsa",
					"2.39 - Alterar data de Venda Avulsa"
				)
				.subscribe(
					(response: any) => {
						switch (acao) {
							case "editar-data":
								this.service.getConfig().usuarioDataLancamento =
									response.content;
								this.abrirDataLancamento();
								break;
						}
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		}
	}

	@HostListener("window:keydown", ["$event"])
	handleKeyboardEvent(event: KeyboardEvent) {
		if (event.key === "Enter") {
			if (this.isBarCode(this.barcodeData)) {
				this.addProdutoBarCode();
			}
			this.barcodeData = "";
		} else {
			this.barcodeData += event.key;
		}
	}

	removerItemTabelaSemProduto() {
		this.tableItensComponent.rawData = this.tableItensComponent.rawData.filter(
			(item) =>
				item.descricao && item.descricao.codigo && item.descricao.codigo > 0
		);
		this.calcularValor();
		this.tableItensComponent.detectChanges();
	}

	filtrarProdutoNaTabelaEAumentarQuantidade(produto) {
		const item = this.tableItensComponent.rawData.find(
			(i) => i.descricao.codigo === produto.codigo
		);
		if (item) {
			item.qtdFC.setValue(item.qtdFC.value + 1);
			item.quantidade = { id: item.qtdFC.value, l: "" + item.qtdFC.value };
			item.valorFinal = item.valor * item.qtdFC.value - item.desconto;
			this.aplicarDesconto(item);
			this.calcularValor();
			this.cd.detectChanges();
		} else {
			this.tableItensComponent.rawData.push({
				descricao: {
					codigo: produto.codigo,
					descricao: produto.descricao,
					valorFinal: produto.valorFinal,
					valor: produto.valorFinal,
					tipoProduto: produto.tipoProduto,
				},
				qtdFC: new FormControl(1),
				quantidade: { id: 1, l: "1" },
				valor: produto.valorFinal,
				valorFinal: produto.valorFinal,
				pontos: produto.pontos,
				tipoProduto: produto.tipoProduto,
				desconto: 0.0,
				descontoFc: new FormControl(0.0),
				descontoPadrao: { codigo: 0, descricao: "-" },
				descontos: [],
				descontoAutorizado: false,
			});
			this.removerItemTabelaSemProduto();
			this.calcularValor();
			this.tableItensComponent.detectChanges();
			this.cd.detectChanges();
		}
	}

	addProdutoBarCode() {
		this.vendaAvulsaService.produtos(this.barcodeData).subscribe((produtos) => {
			if (produtos && produtos.length > 0) {
				this.filtrarProdutoNaTabelaEAumentarQuantidade(produtos[0]);
			} else {
				this.notificationService.warning(
					"Produto não encontrado com o código de barras informado."
				);
			}
		});
	}

	private isBarCode(input: string): boolean {
		// Regex para verificar se a string contém exatamente 13 dígitos
		const regex = /^\d{13}$/;
		return regex.test(input);
	}
}
