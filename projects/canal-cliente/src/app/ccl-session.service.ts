import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Observable, of, zip } from "rxjs";
import { catchError, map, tap } from "rxjs/operators";

import { ApiOamdResponseSingle } from "./ccl-rest.service";
import { EmpresaFinanceiro } from "canal-cliente/app/shared/empresa-financeiro.model";
import { UsuarioBase } from "canal-cliente/app/shared/usuario-base.model";
import { OperacaoPactoStore } from "canal-cliente/app/marketplace/marketplace.model";
import { CryptoService } from "sdk";

export interface SetupParams {
	chave: string;
	usuarioZwId: string;
	usuarioOamd: string;
	empresaId: string;
	zwUrl: string;
	oamdUrl: string;
	codigoFinanceiro: string;
	paginaDirecionar: string;
	marketplace: string;
	treinoIndependente: boolean;
	treinoUrl: string;
	zwBootUrl: string;
}

@Injectable({
	providedIn: "root",
})
export class CclSessionService {
	apresentarMenu = true;
	apresentarPactoStore = false;

	constructor(private http: HttpClient,
							private criptoService: CryptoService) {
	}

	load(params: SetupParams): Observable<boolean> {
		this.apresentarMenu = true;
		this.apresentarPactoStore = true;
		this.params = params;

		if (this.paramsValid()) {
			this.obterApresentarPactoStore();
			return zip(
				this.obterEmpresaFinanceiro(params),
				this.obterUsuarioLogadoZw(params),
			).pipe(
				map((result) => {
					this.empresaFinanceiro = result[0];
					this.loggedUser = result[1];
					return result[0] && (result[1] as boolean);
				}),
			);
		} else {
			return of(false);
		}
	}

	private paramsValid() {
		const valid = this.chave && this.empresaId && this.usuarioZwId;
		if (valid) {
			return true;
		} else {
			console.info(
				"O parametro \"chave\" deve ser informado para includes em aplicação externa.",
			);
			console.info(
				"O parametro \"moduleId\" deve ser informado com código do modulo.",
			);
			console.info(
				"O parametro \"empresaId\" deve ser informado e somente com 1 modulo para includes em aplicação externa.",
			);
			console.info(
				"O parametro \"usuarioZwId\" deve ser informado para includes em aplicação externa.",
			);
			return false;
		}
	}

	private obterEmpresaFinanceiro(
		params: SetupParams,
	): Observable<EmpresaFinanceiro> {
		const oamdUrl = params.oamdUrl;
		let url;
		if (this.codigoFinanceiro != null) {
			url = `${oamdUrl}/prest/empresaFinanceiro/empresas/${this.codigoFinanceiro}`;
		} else {
			url = `${oamdUrl}/prest/empresaFinanceiro/${this.chave}/empresas/${this.empresaId}`;
		}
		return this.http.get(url).pipe(
			map((res: ApiOamdResponseSingle<EmpresaFinanceiro>) => {
				return res.return;
			}),
		);
	}

	private obterUsuarioLogadoZw(params: SetupParams): Observable<UsuarioBase> {
		let url;
		if (this.treinoIndependente) {
			const zwUrl = params.treinoUrl;
			url = `${zwUrl}/prest/usuario/${this.chave}/find?id=${this.usuarioZwId}`;
			return this.http.get(url).pipe(
				map((usuario: UsuarioBase) => {
					return usuario;
				}),
			);
		} else {
			const opt = {
				headers: {
					empresaId: this.empresaId,
				},
			};
			const zwBootUrl = params.zwBootUrl;
			return this.http.get(`${zwBootUrl}/adm/obter-usuario-cc`, opt).pipe(
				map((response: any) => {
					const resp = this.criptoService.decrypt(response.content);
					return JSON.parse(resp);
				}),
			);
		}
	}

	private obterApresentarPactoStore() {
		if (!this.treinoIndependente) {
			this.consultaApresentarPactoStore()
				.pipe(
					tap((result) => {
						if (result.retorno) {
							this.apresentarPactoStore = result.retorno;
						} else {
							this.apresentarPactoStore = false;
						}
					}),
				)
				.subscribe();
		}
	}

	consultaApresentarPactoStore(): Observable<{ erro?: string; retorno?: any }> {
		const dto = new OperacaoPactoStore("apresentarPactoStore");
		dto.empresa = this.params.empresaId;
		dto.usuario = this.params.usuarioZwId;
		const zwUrl = this.params.zwUrl;
		const url = `${zwUrl}/prest/pactostore`;
		const params: any = {
			chave: this.params.chave,
		};
		return this.http.post(url, JSON.stringify(dto), { params }).pipe(
			map((response: any) => {
				return { retorno: response.dados };
			}),
			catchError((error) => {
				return of({ erro: error.dados });
			}),
		);
	}

	set params(params: SetupParams) {
		sessionStorage.setItem("params", JSON.stringify(params));
	}

	get params(): SetupParams {
		return JSON.parse(sessionStorage.getItem("params"));
	}

	get empresaFinanceiro(): EmpresaFinanceiro {
		return JSON.parse(sessionStorage.getItem("empresaFinanceiro"));
	}

	set empresaFinanceiro(empresaFinanceiro: EmpresaFinanceiro) {
		sessionStorage.setItem(
			"empresaFinanceiro",
			JSON.stringify(empresaFinanceiro),
		);
	}

	get loggedUser(): UsuarioBase {
		return JSON.parse(sessionStorage.getItem("loggedUser"));
	}

	set loggedUser(usuario: UsuarioBase) {
		sessionStorage.setItem("loggedUser", JSON.stringify(usuario));
	}

	get chave() {
		return this.params.chave;
	}

	get usuarioZwId() {
		return this.params.usuarioZwId;
	}

	get usuarioOamd() {
		return this.params.usuarioOamd;
	}

	get empresaId() {
		return this.params.empresaId;
	}

	get cclZwUrl() {
		return this.params.zwUrl;
	}

	get cclOamdUrl() {
		return this.params.oamdUrl;
	}

	get codigoFinanceiro() {
		return this.params.codigoFinanceiro;
	}

	get treinoIndependente() {
		return this.params.treinoIndependente;
	}

	get isUsuarioPacto(): boolean {
		return this.loggedUser.codigo === 2;
	}

	get isUsuarioAdminCliente(): boolean {
		return (
			this.loggedUser.tipoPerfis.find(
				(tipo) => tipo !== undefined && tipo.toUpperCase() === "ADMINISTRADOR",
			) !== undefined
		);
	}

	get isAcessarFaturas(): boolean {
		return (
			this.isUsuarioPacto ||
			this.isUsuarioAdminCliente ||
			this.loggedUser.permissaoFaturas
		);
	}
}
