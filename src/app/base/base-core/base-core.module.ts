import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";

import {
	NgbDateAdapter,
	NgbDateParserFormatter,
} from "@ng-bootstrap/ng-bootstrap";

import { ConfirmModalComponent } from "@base-shared/modal/confirm-modal/confirm-modal.component";
import { NgbPactoDateAdapter } from "./utils/NgDatepickerAdapter";
import { NgbDateParserFormatterAdapter } from "./utils/NgbDateParserFormatterAdapter";

import { ModalWrapperComponent } from "@pacto-commom/modal-wrapper/modal-wrapper.component";
import { ModalFullscreenComponent } from "@pacto-commom/modal-fullscreen/modal-fullscreen.component";
import { PactoCommonModule } from "@pacto-commom/pacto-common.module";

@NgModule({
	imports: [CommonModule, PactoCommonModule, FormsModule],
	declarations: [ConfirmModalComponent],
	providers: [
		{ provide: NgbDateAdapter, useClass: NgbPactoDateAdapter },
		{
			provide: NgbDateParserFormatter,
			useClass: NgbDateParserFormatterAdapter,
		},
	],
	entryComponents: [
		ModalFullscreenComponent,
		ModalWrapperComponent,
		ConfirmModalComponent,
	],
	exports: [ConfirmModalComponent],
})
export class BaseCoreModule {}
