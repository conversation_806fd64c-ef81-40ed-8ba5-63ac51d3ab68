import { Injectable } from "@angular/core";
import {
	ActivatedRouteSnapshot,
	CanActivate,
	Router,
	RouterStateSnapshot,
} from "@angular/router";

import { Observable } from "rxjs";

import { PerfilAcessoFuncionalidade, PerfilAcessoRecurso } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";

@Injectable()
export class AparelhoAcessoGuard implements CanActivate {
	public constructor(
		private session: SessionService,
		private snotifyService: SnotifyService,
		private router: Router
	) {}

	/**
	 * Get the configurations from de server and set at a local instance of each service.
	 */
	canActivate(
		route: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> | Promise<boolean> | boolean {
		const recursoNecessario: PerfilAcessoRecurso = route.data.recurso;
		const crossfit = route.data.crossfit;
		const funcionalidadeNecessario: PerfilAcessoFuncionalidade =
			route.data.funcionalidade;

		const perfilAcesso = this.session.perfilUsuario;
		const possuiRecurso = perfilAcesso.possuiRecurso(recursoNecessario);
		const possuiFuncionalidade = perfilAcesso.possuiFuncionalidade(
			funcionalidadeNecessario
		);

		const menssagem =
			"Seu usuário não possui permissão, procure seu administrador";
		if (crossfit) {
			if (!possuiFuncionalidade && funcionalidadeNecessario) {
				this.snotifyService.warning(menssagem);
				return this.router.navigate(["/treino/bi"]);
			} else {
				return true;
			}
		} else if (!possuiRecurso && recursoNecessario) {
			this.snotifyService.warning(menssagem);
			return this.router.navigate(["/treino/bi"]);
		} else {
			return true;
		}
	}
}
