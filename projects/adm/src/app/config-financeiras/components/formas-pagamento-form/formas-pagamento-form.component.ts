import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnD<PERSON>roy,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	CadastroAuxApiFormasPagamentoService,
	FormasPagamento,
} from "cadastro-aux-api";
import moment from "moment";
import { SnotifyService } from "ng-snotify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { ApiResponseList, EmpresaFinanceiro, SessionService } from "sdk";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

@Component({
	selector: "adm-formas-pagamento-form",
	templateUrl: "./formas-pagamento-form.component.html",
	styleUrls: ["./formas-pagamento-form.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormasPagamentoFormComponent
	implements OnInit, OnDestroy, AfterViewInit
{
	subs = new Subscription();

	hideCompensacaoCartaoCredito = true;
	hideTaxaPix = true;
	hideCompensarDiasUteis = true;

	isEditingOrAddingTableTaxaDebito = false;

	selectedPaymentOptView: ElementRef<HTMLElement>;

	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;

	@ViewChild("cartaoCredito", { static: true })
	cartaoCredito: ElementRef<HTMLElement>;

	@ViewChild("cartaoDebito", { static: true })
	cartaoDebito: ElementRef<HTMLElement>;

	@ViewChild("creditoContaCorrente", { static: true })
	creditoContaCorrente: ElementRef<HTMLElement>;

	@ViewChild("aVista", { static: true }) aVista: ElementRef<HTMLElement>;

	@ViewChild("default", { static: true })
	defaultView: ElementRef<HTMLElement>;

	@ViewChild("empresaColumn", { static: true })
	empresaColumn: TemplateRef<any>;

	@ViewChild("contaDestinoColumn", { static: true })
	contaDestinoColumn: TemplateRef<any>;

	@ViewChild("tableEmpresasRef", { static: true })
	tableEmpresasRef: RelatorioComponent;

	@ViewChild("perfilAcessoColumn", { static: true })
	perfilAcessoColumn: TemplateRef<any>;

	@ViewChild("tablePerfilRef", { static: true })
	tablePerfilRef: RelatorioComponent;

	@ViewChild("tableTaxaDebitoEditable", { static: false })
	tableTaxaDebitoEditable: CatTableEditableComponent;
	@ViewChild("columnTaxaDebitoTaxa", { static: true })
	columnTaxaDebitoTaxa: TemplateRef<any>;
	@ViewChild("columnTaxaDebitoAdquirente", { static: true })
	columnTaxaDebitoAdquirente: TemplateRef<any>;
	@ViewChild("columnTaxaDebitoBandeira", { static: true })
	columnTaxaDebitoBandeira: TemplateRef<any>;
	@ViewChild("columnTaxaDebitoInicial", { static: true })
	columnTaxaDebitoInicial: TemplateRef<any>;
	@ViewChild("columnTaxaDebitoFinal", { static: true })
	columnTaxaDebitoFinal: TemplateRef<any>;

	@ViewChild("tableTaxaCreditoEditable", { static: false })
	tableTaxaCreditoEditable: CatTableEditableComponent;
	@ViewChild("columnTaxaCreditoNParcelas", { static: true })
	columnTaxaCreditoNParcelas: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoAntecipAuto", { static: true })
	columnTaxaCreditoAntecipAuto: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoDiasComp", { static: true })
	columnTaxaCreditoDiasComp: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoTaxaAtual", { static: true })
	columnTaxaCreditoTaxaAtual: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoAdquirente", { static: true })
	columnTaxaCreditoAdquirente: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoBandeira", { static: true })
	columnTaxaCreditoBandeira: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoInicial", { static: true })
	columnTaxaCreditoInicial: TemplateRef<any>;
	@ViewChild("columnTaxaCreditoFinal", { static: true })
	columnTaxaCreditoFinal: TemplateRef<any>;

	@ViewChild("tableTaxaBoletoEditable", { static: false })
	tableTaxaBoletoEditable: CatTableEditableComponent;
	@ViewChild("columnTaxaBoletoTaxaAtual", { static: true })
	columnTaxaBoletoTaxaAtual: TemplateRef<any>;
	@ViewChild("columnTaxaBoletoTipo", { static: true })
	columnTaxaBoletoTipo: TemplateRef<any>;
	@ViewChild("columnTaxaBoletoVigenteDesde", { static: true })
	columnTaxaBoletoVigenteDesde: TemplateRef<any>;
	@ViewChild("columnTaxaBoletoVigenteAte", { static: true })
	columnTaxaBoletoVigenteAte: TemplateRef<any>;

	tableTaxaDebitoPage = 1;
	tableTaxaCreditoPage = 1;
	tableTaxaBoletoPage = 1;
	tablePerfilPage = 1;
	tableEmpresaPage = 1;
	tableTaxaDebitoSize = 10;
	tableTaxaCreditoSize = 10;
	tableTaxaBoletoSize = 10;
	tablePerfilSize = 10;
	tableEmpresaSize = 10;

	tableEmpresas: PactoDataGridConfig;
	empresasTableState: ApiResponseList<any> = {
		content: [],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	empresasResponse: ApiResponseList<any> = {
		content: Array.from(Array(5).keys()).map((i) => ({
			empresa: { id: i, nome: "Pacto academia" },
			contaDestino: "B: 237 / AG: 2241-0/ CC: 113531-7",
		})),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	tablePerfis: PactoDataGridConfig;
	tablePerfisResponse: ApiResponseList<any> = {
		content: [],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	perfisResponse: ApiResponseList<any> = {
		content: Array.from(Array(2).keys()).map((i) => ({
			nomePerfil: "NOME PERFIL",
		})),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	tableTaxaDebito: PactoDataGridConfig;
	taxaDebitoTableState = {
		content: [],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	taxaDebitoResponse: ApiResponseList<any> = {
		content: [
			{
				Taxa: 0.2,
				Adquirente: "Cielo",
				Bandeira: "Mastercard",
				DtInicial: new Date(),
				DtFinal: new Date(),
			},
			{
				Taxa: 0.45,
				Adquirente: "Visa",
				Bandeira: "Platinum",
				DtInicial: new Date(),
				DtFinal: new Date(),
			},
		],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	tableTaxaCredito: PactoDataGridConfig;
	taxaCreditoTableState = {
		content: [],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	taxaCreditoResponse: ApiResponseList<any> = {
		content: [
			{
				NParcelas: 4,
				AntecipAuto: "Sim",
				DiasComp: 10,
				TaxaAtual: 0.2,
				Adquirente: "Cielo",
				Bandeira: "Mastercard - Cred",
				Inicial: new Date(),
				Final: new Date(),
			},
			{
				NParcelas: 4,
				AntecipAuto: "Sim",
				DiasComp: 10,
				TaxaAtual: 0.2,
				Adquirente: "Cielo",
				Bandeira: "Mastercard - Cred",
				Inicial: new Date(),
				Final: new Date(),
			},
			{
				NParcelas: 8,
				AntecipAuto: "Não",
				DiasComp: 10,
				TaxaAtual: 0.2,
				Adquirente: "Cielo",
				Bandeira: "Mastercard - Cred",
				Inicial: new Date(),
				Final: new Date(),
			},
			{
				NParcelas: 8,
				AntecipAuto: "Não",
				DiasComp: 10,
				TaxaAtual: 0.2,
				Adquirente: "Cielo",
				Bandeira: "Mastercard - Cred",
				Inicial: new Date(),
				Final: new Date(),
			},
		],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	tableTaxaBoleto: PactoDataGridConfig;
	taxaBoletoTableState = {
		content: [],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	taxaBoletoResponse: ApiResponseList<any> = {
		content: [
			{
				taxaAtual: 0.2,
				tipo: "Percentual",
				Inicial: new Date(),
				Final: new Date(),
			},
		],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	id;

	form = new FormGroup({
		codigo: new FormControl({ value: "0", disabled: true }),
		descricao: new FormControl(),
		tipoFormaPagamento: new FormControl(),
		convenio: new FormControl(),
		ativa: new FormControl(),
		checkboxOptions: new FormGroup({
			usarSomenteFinanceiro: new FormControl(),
			defaultRecorrencia: new FormControl(),
			cartaoDebitoOnline: new FormControl(),
			aprensentarCampoNSU: new FormControl(),
			dcoPadrao: new FormControl(),
			gerarPontosFidelidade: new FormControl(),
			compensarApenasDiasUteis: new FormControl(),
			obrigatorioInformarCodigoAutorizacao: new FormControl(),
		}),
		empresa: new FormGroup({
			codigo: new FormControl(),
			contaDestino: new FormControl(),
		}),
		taxaBoleto: new FormGroup({
			tipoPagamento: new FormControl(),
			taxaBoleto: new FormControl(),
			iniciaEm: new FormControl(new Date()),
			finalizaEm: new FormControl(new Date()),
		}),
		taxaCredito: new FormGroup({
			antecipacaoAuto: new FormControl(),
			nrParcelas: new FormControl(),
			adquirente: new FormControl(),
			operadora: new FormControl(),
			taxaCartao: new FormControl(),
			iniciaEm: new FormControl(new Date()),
			finalizaEm: new FormControl(new Date()),
		}),
		perfis: new FormGroup({
			nomePerfil: new FormControl(),
		}),
		tipoParceiroFidelidade: new FormControl(),
		diasCompensacaoCartaoCredito: new FormControl(),
		tipoCartaoDebito: new FormControl(),
		taxaPix: new FormControl(),
		cor: new FormControl(),
		empresas: new FormControl(),
	});

	empresaFormGroup = this.form.get("empresa") as FormGroup;
	perfisFormGroup = this.form.get("perfis") as FormGroup;
	checkboxOptions = this.form.get("checkboxOptions") as FormGroup;
	taxaBoletoFormGroup = this.form.get("taxaBoleto") as FormGroup;
	taxaCreditoFormGroup = this.form.get("taxaCredito") as FormGroup;

	formasPagamento$ = this.formasPagamentoService.getTipoFormasPagamento().pipe(
		// define o primeiro item da lista de formas de pagamento como padrão
		tap((options) => {
			this.form.patchValue({ tipoFormaPagamento: options[1].id });
		})
	);

	empresas = this.sessionService.empresas;

	perfisArray = this.getPerfis();

	TESTEArray = [
		{ nome: "teste1", id: 1 },
		{ nome: "teste2", id: 2 },
	];

	testeSelectsInputs: Array<{ label: string; codigo: string }> = new Array<{
		label: string;
		codigo: string;
	}>();
	testeSelectInputs = [
		{ label: "teste1", codigo: "1" },
		{ label: "teste2", codigo: "2" },
	];

	constructor(
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private formasPagamentoService: CadastroAuxApiFormasPagamentoService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService, // private dialog: NgbActiveModal
		private sessionService: SessionService
	) {}

	ngOnInit() {
		console.log(this.empresas);
		this.initTableEmpresas();
		this.initTablePerfis();
		this.initTableTaxaBoleto();
		this.initTableTaxaCredito();
		this.initTableTaxaDebito();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");

		this.subs.add(
			this.form
				.get("tipoFormaPagamento")
				.valueChanges.subscribe((value) => this.handleFieldChanges(value))
		);
	}

	ngAfterViewInit(): void {
		// Assim que a tela é preenchida, essa é a visão dos checkboxes
		this.selectedPaymentOptView = this.cartaoCredito;
		this.cd.detectChanges();

		if (this.id) {
			// get the info for the id selected
		} else {
			// this.sortEmpresas();
			this.createEmpresaPageObject();
			this.createPerfisPageObject();
		}
	}

	ngOnDestroy(): void {
		this.subs.unsubscribe();
	}

	voltarHome(): void {
		this.router.navigate(["adm", "config-financeiras", "formas-pagamento"]);
	}

	cancel() {
		// this.dialog.dismiss();
	}

	handleFieldChanges(selectedOpt): void {
		// defaults the value
		this.hideCompensacaoCartaoCredito = true;
		this.hideTaxaPix = true;
		this.hideCompensarDiasUteis = true;

		// change the fields displayed on the screen
		switch (FormasPagamento[selectedOpt]) {
			case FormasPagamento.BB:
				this.hideCompensarDiasUteis = false;
				break;
			case FormasPagamento.TB:
			case FormasPagamento.CO:
			case FormasPagamento.AV:
				this.selectedPaymentOptView = this.aVista;
				break;

			case FormasPagamento.CA:
				this.selectedPaymentOptView = this.cartaoCredito;
				this.hideCompensarDiasUteis = false;
				break;

			case FormasPagamento.CD:
				this.selectedPaymentOptView = this.cartaoDebito;
				this.hideCompensarDiasUteis = false;
				break;

			case FormasPagamento.CC:
				this.selectedPaymentOptView = this.creditoContaCorrente;
				this.hideCompensacaoCartaoCredito = false;
				break;

			case FormasPagamento.PX:
				this.selectedPaymentOptView = this.creditoContaCorrente;
				this.hideTaxaPix = false;
				break;

			case FormasPagamento.PF:
				this.selectedPaymentOptView = null;
				break;

			default:
				this.selectedPaymentOptView = this.defaultView;
				break;
		}

		this.checkboxOptions.reset();
	}

	// INICIO TABELA EMPRESAS
	initTableEmpresas() {
		this.tableEmpresas = new PactoDataGridConfig({
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return this.empresasTableState;
			},
			columns: [
				{
					nome: "empresa",
					titulo: this.empresaColumn,
					visible: true,
					ordenavel: true,
					valueTransform: (v: any) => (typeof v == "string" ? v : v.nome),
				},
				{
					nome: "contaDestino",
					titulo: this.contaDestinoColumn,
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("ACTION_DELETE"),
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "",
				},
			],
		});
	}

	createEmpresaPageObject(page = 1, size = 10) {
		this.empresasTableState.totalElements = this.empresasResponse.totalElements;
		this.empresasTableState.size = size;
		this.empresasTableState.totalPages = +(
			this.empresasResponse.totalElements / size
		);
		this.empresasTableState.first = page === 0 || page === 1;
		this.empresasTableState.last = page === this.empresasResponse.totalPages;
		this.empresasTableState.content = this.empresasResponse.content;
		this.tableEmpresasRef.reloadData();
	}

	changePageEmpresa(page) {
		this.tableEmpresaPage = page;
		this.createEmpresaPageObject(this.tableEmpresaPage, this.tableEmpresaSize);
	}

	changePageSizeEmpresa(size) {
		this.tableEmpresaSize = size;
		this.createEmpresaPageObject(this.tableEmpresaPage, this.tableEmpresaSize);
	}

	sortEmpresas() {
		this.empresas = this.empresas.sort((a, b) => {
			if (a.nome > b.nome) {
				return 1;
			} else if (a.nome < b.nome) {
				return -1;
			} else {
				return 0;
			}
		});

		this.createEmpresaPageObject();
	}

	addEmpresa() {
		console.log(this.empresaFormGroup.getRawValue());
		const { codigo, contaDestino } = this.empresaFormGroup.getRawValue();
		const empresa: EmpresaFinanceiro = this.empresas.find(
			(e) => e.codigo == codigo
		);
		this.empresasResponse.content.push({
			empresa,
			contaDestino,
		});

		this.createEmpresaPageObject();
	}

	deleteEmpresa(rowInfo) {
		const index = rowInfo.rowIndex;
		if (index > -1) {
			// only splice array when item is found
			this.empresasResponse.content.splice(index, 1); // 2nd parameter means remove one item only
		}
		this.createEmpresaPageObject();
	}

	// FIM TABELA EMPRESAS
	// INICIO TABELA PERFIS

	getPerfis() {
		let usuarios = [{ ...this.sessionService.loggedUser }];
		var response: any;
		response = [];
		for (
			let UsersForindex = 0;
			UsersForindex < usuarios.length;
			UsersForindex++
		) {
			for (
				let ProfileForIndex = 0;
				ProfileForIndex < usuarios[UsersForindex].perfis.length;
				ProfileForIndex++
			) {
				let idPerfil = usuarios[UsersForindex].id + (ProfileForIndex + 1) * 0.1;
				response.push({
					Usuario: usuarios[UsersForindex].nome,
					idUsuario: usuarios[UsersForindex].id,
					perfil: usuarios[UsersForindex].perfis[ProfileForIndex],
					idPerfil: idPerfil,
				});
			}
		}
		return response;
	}

	initTablePerfis() {
		this.tablePerfis = new PactoDataGridConfig({
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return this.tablePerfisResponse;
			},
			columns: [
				{
					nome: "nomePerfil",
					titulo: this.perfilAcessoColumn,
					visible: true,
					ordenavel: true,
					valueTransform: (v: any) => (typeof v == "string" ? v : v.nome),
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("ACTION_DELETE"),
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "",
				},
			],
		});
	}

	createPerfisPageObject(page = 1, size = 10) {
		this.tablePerfisResponse.totalElements = this.perfisResponse.totalElements;
		this.tablePerfisResponse.size = size;
		this.tablePerfisResponse.totalPages = +(
			this.perfisResponse.totalElements / size
		);
		this.tablePerfisResponse.first = page === 0 || page === 1;
		this.tablePerfisResponse.last = page === this.perfisResponse.totalPages;
		this.tablePerfisResponse.content = this.perfisResponse.content;
		this.tablePerfilRef.reloadData();
	}

	changePagePerfis(page) {
		this.tablePerfilPage = page;
		this.createPerfisPageObject(page);
	}

	changePageSizePerfis(size) {
		this.createPerfisPageObject(this.tablePerfilPage, size);
	}

	sortPerfis() {
		this.perfisArray = this.perfisArray.sort((a, b) => {
			if (a.nome > b.nome) {
				return 1;
			} else if (a.nome < b.nome) {
				return -1;
			} else {
				return 0;
			}
		});

		this.createPerfisPageObject();
	}

	addPerfil() {
		console.log(this.perfisFormGroup.getRawValue());
		const { nomePerfil } = this.perfisFormGroup.getRawValue();
		// const empresa: EmpresaFinanceiro = this.empresas.find(
		// 	(e) => e.codigo == codigo
		// );
		this.perfisResponse.content.push({
			nomePerfil,
		});

		this.createPerfisPageObject();
	}

	deletePerfis(rowInfo) {
		const index = rowInfo.rowIndex;
		if (index > -1) {
			// only splice array when item is found
			this.perfisResponse.content.splice(index, 1); // 2nd parameter means remove one item only
		}
		this.createPerfisPageObject();
	}

	// FIM TABELA PERFIS
	// INICIO TABELA TAXA BOLETO

	initTableTaxaBoleto() {
		this.tableTaxaBoleto = new PactoDataGridConfig({
			pagination: true,
			dataAdapterFn: (serverData) => {
				return this.taxaCreditoResponse;
			},
			formGroup: new FormGroup({
				taxaAtual: new FormControl(),
				tipo: new FormControl(),
				vigenteDesde: new FormControl(),
				vigenteAte: new FormControl(),
			}),
			columns: [
				{
					nome: "taxaAtual",
					titulo: this.columnTaxaBoletoTaxaAtual,
					visible: true,
					inputType: "decimal",
					labelSelectKey: "Taxa atual",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "25%",
				},
				{
					nome: "tipo",
					titulo: this.columnTaxaBoletoTipo,
					visible: true,
					inputType: "select",
					inputSelectData: this.testeSelectInputs,
					idSelectKey: "codigo",
					labelSelectKey: "label",
					showSelectFilter: true,
					addEmptyOption: false,
					width: "25%",
					showAddSelectBtn: false,
				},
				{
					nome: "vigenteDesde",
					inputType: "date",
					titulo: this.columnTaxaBoletoVigenteDesde,
					visible: true,
					labelSelectKey: "Vigente desde",
					showSelectFilter: true,
					addEmptyOption: false,
					width: "25%",
					showAddSelectBtn: false,
				},
				{
					nome: "vigenteAte",
					inputType: "date",
					titulo: this.columnTaxaBoletoVigenteAte,
					visible: true,
					labelSelectKey: "Vigente até",
					showSelectFilter: true,
					addEmptyOption: false,
					width: "25%",
					showAddSelectBtn: false,
				},
			],
		});
		this.cd.detectChanges();
	}

	createTaxaBoletoPageObject(page = 1, size = 10) {
		this.taxaBoletoTableState.totalElements =
			this.taxaBoletoResponse.totalElements;
		this.taxaBoletoTableState.size = size;
		this.taxaBoletoTableState.totalPages = +(
			this.taxaBoletoResponse.totalElements / size
		);
		this.taxaBoletoTableState.first = page === 0 || page === 1;
		this.taxaBoletoTableState.last =
			page === this.taxaBoletoResponse.totalPages;
		this.taxaBoletoTableState.content = this.taxaBoletoResponse.content;
		this.tableTaxaBoletoEditable.reloadData();
	}

	sortTaxaBoleto() {
		this.tableTaxaBoleto = this.perfisArray.sort((a, b) => {
			if (a.nome > b.nome) {
				return 1;
			} else if (a.nome < b.nome) {
				return -1;
			} else {
				return 0;
			}
		});

		this.createPerfisPageObject();
	}

	changePageTaxaBoleto(page) {
		this.tableTaxaBoletoPage = page;
		this.createTaxaBoletoPageObject(
			this.tableTaxaBoletoPage,
			this.tableTaxaBoletoSize
		);
	}

	changeSizeTaxaBoleto(size) {
		this.tableTaxaBoletoSize = size;
		this.createTaxaBoletoPageObject(
			this.tableTaxaBoletoPage,
			this.tableTaxaBoletoSize
		);
	}

	// FIM TABELA TAXA BOLETO

	// INICIO TABELA TAXA CREDITO

	initTableTaxaCredito() {
		this.tableTaxaCredito = new PactoDataGridConfig({
			pagination: true,
			dataAdapterFn: (serverData) => {
				return this.taxaCreditoResponse;
			},
			formGroup: new FormGroup({
				NParcelas: new FormControl(),
				AntecipAuto: new FormControl(),
				DiasComp: new FormControl(),
				TaxaAtual: new FormControl(),
				Adquirente: new FormControl(),
				Bandeira: new FormControl(),
				Inicial: new FormControl(),
				Final: new FormControl(),
			}),
			columns: [
				{
					nome: "NParcelas",
					titulo: this.columnTaxaCreditoNParcelas,
					inputType: "decimal",
					visible: true,
					labelSelectKey: " N.º parcelas",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
				},
				{
					nome: "AntecipAuto",
					titulo: this.columnTaxaCreditoAntecipAuto,
					inputType: "checkbox",
					valueTransform: (v) => (v ? "Sim" : "Não"),
					visible: true,
					labelSelectKey: "Antecip. auto.",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
				},
				{
					nome: "DiasComp",
					titulo: this.columnTaxaCreditoDiasComp,
					inputType: "number",
					visible: true,
					labelSelectKey: "Dias comp.",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
				},
				{
					nome: "TaxaAtual",
					titulo: this.columnTaxaCreditoTaxaAtual,
					inputType: "decimal",
					visible: true,
					labelSelectKey: "Taxa atual",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
				},
				{
					nome: "Adquirente",
					titulo: this.columnTaxaCreditoAdquirente,
					inputType: "select",
					visible: true,
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					inputSelectData: this.testeSelectInputs,
					idSelectKey: "codigo",
					labelSelectKey: "label",
					width: "12.5%",
				},
				{
					nome: "Bandeira",
					titulo: this.columnTaxaCreditoBandeira,
					inputType: "select",
					visible: true,
					inputSelectData: this.testeSelectInputs,
					idSelectKey: "codigo",
					labelSelectKey: "label",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
				},
				{
					nome: "Inicial",
					titulo: this.columnTaxaCreditoInicial,
					inputType: "date",
					visible: true,
					labelSelectKey: "Vigen. inicial",
					editable: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
					date: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "Final",
					titulo: this.columnTaxaCreditoFinal,
					inputType: "date",
					visible: true,
					labelSelectKey: "Vigen. final",
					editable: true,
					showSelectFilter: true,
					addEmptyOption: false,
					showAddSelectBtn: false,
					width: "12.5%",
					date: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
			],
			// endpointUrl: this.planoService.urlPlano + '/only-cod-name'
		});
		this.cd.detectChanges();
	}

	createCondicaoTaxaCreditoPageObject(page = 1, size = 10) {
		this.taxaCreditoTableState.totalElements =
			this.taxaCreditoResponse.totalElements;
		this.taxaCreditoTableState.size = size;
		this.taxaCreditoTableState.totalPages = +(
			this.taxaCreditoResponse.totalElements / size
		);
		this.taxaCreditoTableState.first = page === 0 || page === 1;
		this.taxaCreditoTableState.last =
			page === this.taxaCreditoResponse.totalPages;
		this.taxaCreditoTableState.content = this.taxaCreditoResponse.content;
		this.tableTaxaCreditoEditable.reloadData();
	}

	changePageTaxaCredito(page) {
		this.tableTaxaCreditoPage = page;
		this.createCondicaoTaxaCreditoPageObject(
			this.tableTaxaCreditoPage,
			this.tableTaxaCreditoSize
		);
	}

	changeSizeTaxaCredito(size) {
		this.tableTaxaCreditoSize = size;
		this.createCondicaoTaxaCreditoPageObject(
			this.tableTaxaCreditoPage,
			this.tableTaxaCreditoSize
		);
	}

	editTaxaCredito(evento) {
		console.log("editTaxaCredito");
		console.log(evento);
	}

	addTaxaCredito() {
		console.log(this.taxaCreditoFormGroup.getRawValue());
		const { taxaCredito } = this.taxaCreditoFormGroup.getRawValue();
		this.taxaCreditoResponse.content.push({
			taxaCredito,
		});

		this.createCondicaoTaxaCreditoPageObject();
	}

	deleteTaxaCredito(rowInfo) {
		console.log("deleteTaxaCredito");
		console.log(rowInfo);
	}

	// FIM TABELA TAXA CREDITO

	// INICIO TABELA TAXA DEBITO

	initTableTaxaDebito() {
		this.tableTaxaDebito = new PactoDataGridConfig({
			pagination: true,
			dataAdapterFn: (serverData) => {
				return this.taxaDebitoResponse;
			},
			formGroup: new FormGroup({
				Taxa: new FormControl(),
				Adquirente: new FormControl(),
				Bandeira: new FormControl(),
				Inicial: new FormControl(),
				Final: new FormControl(),
			}),
			// beforeConfirm: (row, form, data, rowIndex) => this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "Taxa",
					titulo: this.columnTaxaDebitoTaxa,
					visible: true,
					inputType: "decimal",
					showAddSelectBtn: false,
					addEmptyOption: false,
					ordenavel: false,
					editable: true,
					width: "11%",
				},
				{
					nome: "Adquirente",
					titulo: this.columnTaxaDebitoAdquirente,
					visible: true,
					inputType: "select",
					showAddSelectBtn: false,
					addEmptyOption: false,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					width: "21%",
					inputSelectData: this.testeSelectInputs,
					idSelectKey: "codigo",
					labelSelectKey: "label",
				},
				{
					nome: "Bandeira",
					titulo: this.columnTaxaDebitoBandeira,
					visible: true,
					inputType: "select",
					showAddSelectBtn: false,
					addEmptyOption: false,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					width: "21%",
					inputSelectData: this.testeSelectInputs,
					idSelectKey: "codigo",
					labelSelectKey: "label",
				},
				,
				{
					nome: "Inicial",
					titulo: this.columnTaxaDebitoInicial,
					visible: true,
					inputType: "date",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					showAddSelectBtn: false,
					addEmptyOption: false,
					ordenavel: false,
					editable: true,
					width: "21%",
				},
				{
					nome: "Final",
					titulo: this.columnTaxaDebitoFinal,
					visible: true,
					inputType: "date",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					showAddSelectBtn: false,
					addEmptyOption: false,
					ordenavel: false,
					editable: true,
					width: "21%",
				},
			],
			// endpointUrl: this.LINKService.URL + '/AREA'  AQUI VAI ENDPOINT QUE TRAZ CONTEUDO DE DEBITO
		});
		this.cd.detectChanges();
	}

	createCondicaoTaxaDebitoPageObject(page = 1, size = 10) {
		this.taxaDebitoTableState.totalElements =
			this.taxaDebitoResponse.content.length;
		this.taxaDebitoTableState.size = size;
		this.taxaDebitoTableState.totalPages = Math.ceil(
			+(this.taxaDebitoResponse.totalElements / this.taxaDebitoResponse.size)
		);
		this.taxaDebitoTableState.first = page === 0 || page === 1;
		this.taxaDebitoTableState.last =
			page === this.taxaDebitoResponse.totalPages;
		this.taxaDebitoTableState.content = this.taxaDebitoResponse.content.slice(
			size * page - size,
			size * page
		);
		this.tableTaxaDebitoEditable.reloadData();
	}

	changePageTaxaDebito(page) {
		if (!isNaN(page)) {
			this.tableTaxaDebitoPage = page;
		}
		this.createCondicaoTaxaDebitoPageObject(page, this.tableTaxaDebitoSize);
	}

	changeSizeTaxaDebito(size) {
		if (!isNaN(size)) {
			this.tableTaxaDebitoSize = size;
		}
		this.createCondicaoTaxaDebitoPageObject(this.tableTaxaDebitoPage, size);
	}

	confirmTaxaDebito(event) {
		if (!this.isEditingOrAddingTableTaxaDebito) {
			const rowFormData = {
				Adquirente: event.row.Adquirente,
				Bandeira: event.row.Bandeira,
				Final: event.row.Final,
				Inicial: event.row.Inicial,
				Taxa: event.row.Taxa,
			};
		}
	}

	deleteTaxaDebito(rowInfo) {
		console.log("deleteTaxaDebito", rowInfo);
	}

	editTaxaDebito(rowInfo) {
		console.log(this.isEditingOrAddingTableTaxaDebito);

		console.log("editTaxaDebito", rowInfo);
	}

	isEditingOrAddingTaxaDebito(event) {
		this.isEditingOrAddingTableTaxaDebito = event;
	}

	//FIM TABELA DEBITO

	enviarDados() {
		if (this.confereDados()) {
		}
	}

	confereDados(): any {
		console.log(this.form);

		return true;
	}
}
