import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import {
	ClubeVantagensApiConfig,
	ClubeVantagensApiConfigProviderBase,
} from "clube-vantagens-api";
import { ClientDiscoveryService, SessionService } from "sdk";
import { Observable, of } from "rxjs";

@Injectable()
export class ClubeVantagesApiProviderConfigService extends ClubeVantagensApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<ClubeVantagensApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.clientDiscoveryService.getUrlMap().clubeVantagensMsUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
