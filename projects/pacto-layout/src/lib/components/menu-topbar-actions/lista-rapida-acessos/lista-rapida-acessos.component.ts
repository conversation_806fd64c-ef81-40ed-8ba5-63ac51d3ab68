import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	Optional,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { PactoLayoutSearchService } from "../../../navigation/pacto-layout-search/pacto-layout-search.service";
import { PactoLayoutSDKWrapper } from "../../../sdk-wrapper/sdk-wrappers";
import { LayoutNavigationService } from "../../../navigation/layout-navigation.service";
import { Router } from "@angular/router";
import { PlataformaModulo } from "sdk";

@Component({
	selector: "pacto-lista-rapida-acessos",
	templateUrl: "./lista-rapida-acessos.component.html",
	styleUrls: ["./lista-rapida-acessos.component.scss"],
})
export class ListaRapidaAcessosComponent implements OnInit {
	aberto = false;
	configuracoes = false;
	acessos;
	tipoFC = new FormControl(1);
	verTodos = false;
	verPendencias = false;
	configNotificacoes;

	constructor(
		@Optional() private pactoLayoutSearchService: PactoLayoutSearchService,
		@Inject(LOCALE_ID) private locale,
		private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper,
		private layoutNavigationService: LayoutNavigationService,
		private router: Router,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit(): void {
		this.tipoFC.valueChanges.subscribe(() => {
			this.consultar();
		});
	}

	fechar() {
		this.aberto = false;
		this.cd.detectChanges();
	}

	consultar() {
		if (this.pactoLayoutSearchService) {
			this.pactoLayoutSearchService
				.acessos(this.tipoFC.value, 50)
				.subscribe((result) => {
					this.configNotificacoes = result;
					this.acessos = result.lista;
					this.verTodos = result.verTodos;
					if (!this.verTodos) {
						this.tipoFC = new FormControl(2);
						this.tipoFC.valueChanges.subscribe(() => {
							this.consultar();
						});
					}
					this.cd.detectChanges();
				});
		}
	}

	abrir() {
		this.aberto = true;
		this.configuracoes = false;
		this.consultar();
		this.cd.detectChanges();
	}

	fecharConfig() {
		this.configuracoes = false;
		this.cd.detectChanges();
	}

	abrirConfig() {
		this.configuracoes = true;
		this.cd.detectChanges();
	}

	toggleConfig(tipo: string) {
		const configNotificacoes = this.configNotificacoes.mapConfigs[tipo];
		this.configNotificacoes.mapConfigs[tipo] = !configNotificacoes;
		this.pactoLayoutSearchService
			.mudarConfigNotificacao(tipo, this.configNotificacoes.mapConfigs[tipo])
			.subscribe((result) => {
				this.cd.detectChanges();
			});
	}

	configMarcada(tipo: string) {
		return (
			this.configNotificacoes &&
			this.configNotificacoes.mapConfigs &&
			this.configNotificacoes.mapConfigs[tipo]
		);
	}

	goToAluno(matricula) {
		const plat =
			PlataformaModulo[this.layoutNavigationService.getCurrentModule().sigla];
		if (plat === PlataformaModulo.NZW) {
			const url =
				this.pactoLayoutSDKWrapper.clientDiscoveryService.getUrlMap()
					.treinoFrontUrl +
				"/" +
				this.locale +
				"/pessoas/perfil-v2/" +
				matricula;
			window.location.href = url;
		} else {
			this.router.navigate(["pessoas", "perfil-v2", matricula, "contratos"]);
			this.fechar();
		}
	}
}
