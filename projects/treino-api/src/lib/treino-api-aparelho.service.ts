import { Injectable } from "@angular/core";
import { Api<PERSON><PERSON>ponseList, ApiResponseSingle } from "./base.model";
import { Aparelho, AparelhoEdit } from "./aparelho.model";
import { AparelhoBase } from "./wod.model";
import { TreinoApiModule } from "./treino-api.module";

import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { TreinoApiBaseService } from "./treino-api-base.service";

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiAparelhoService {
	constructor(private restService: TreinoApiBaseService) {}

	obterAparelho(id): Observable<Aparelho> {
		return this.restService.get(`aparelhos/${id}`).pipe(
			map((response: ApiResponseSingle<Aparelho>) => {
				return response.content;
			})
		);
	}

	obterTodosAparelhos(crossfit: string): Observable<Array<AparelhoBase>> {
		const params = {
			crossfit: "false",
		};
		if (crossfit) {
			params.crossfit = crossfit;
		}
		return this.restService.get("aparelhos/all", { params }).pipe(
			map((response: ApiResponseList<AparelhoBase>) => {
				return response.content;
			})
		);
	}

	removerAparelho(id): Observable<any> {
		return this.restService.delete(`aparelhos/${id}`).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return true;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.error(error);
					observer.complete();
				});
			})
		);
	}

	criarAparelho(dados: AparelhoEdit): Observable<Aparelho> {
		return this.restService.post(`aparelhos`, dados).pipe(
			map((response: ApiResponseSingle<Aparelho>) => {
				return response.content;
			})
		);
	}

	atualizarAparelho(dados: AparelhoEdit): Observable<Aparelho> {
		return this.restService.put(`aparelhos/${dados.id}`, dados).pipe(
			map((response: ApiResponseSingle<Aparelho>) => {
				return response.content;
			})
		);
	}

	obterAparelhosParaReservaEquipamentos(): Observable<any> {
		return this.restService
			.get(`aparelhos/habilitados-reserva-equipamento`)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				}),
				catchError((error) => {
					return new Observable((observer) => {
						observer.error(error);
						observer.complete();
					});
				})
			);
	}

	obterTodosSensoresSelfloops(): Observable<Array<any>> {
		return this.restService
			.get(`aparelhos/integracaoSelfloop/sensores`, {})
			.pipe(
				map((response: ApiResponseList<any>) => {
					return response.content;
				})
			);
	}
}
