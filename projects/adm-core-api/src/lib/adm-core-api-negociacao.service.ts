import { Injectable } from "@angular/core";

import { Observable, of } from "rxjs";

import { AdmCoreApiModule } from "./adm-core-api.module";
import { AdmCoreApiBaseService } from "./adm-core-api-base.service";
import { catchError, map } from "rxjs/operators";
import { Plano } from "./plano.model";
import { ApiResponseList, ApiResponseSingle } from "./base.model";
import { Negociacao } from "./negociacao.model";
import { AgendaTurma } from "./agenda-turma.model";

@Injectable({
	providedIn: AdmCoreApiModule,
})
export class AdmCoreApiNegociacaoService {
	constructor(private restApi: AdmCoreApiBaseService) {}

	consultarPlanos(
		incluirBolsa,
		forcarPlano,
		codigoCliente,
		contrato,
		dataLancamentoContrato?
	): Observable<Array<Plano>> {
		const params: any = {
			codigoCliente,
			contrato,
		};
		if (dataLancamentoContrato) {
			params.filters = { dataLancamentoContrato: dataLancamentoContrato };
		}
		if (params.filters) {
			params.filters = JSON.stringify(params.filters);
		}
		return this.restApi
			.get(`negociacao/planos/${incluirBolsa}/${forcarPlano}`, {
				params,
			})
			.pipe(
				map((response: ApiResponseList<Plano>) => {
					return response.content;
				})
			);
	}

	agendaTurmas(
		modalidade: number,
		nivel: number,
		professor: number,
		periodo: string,
		disponibilidade: string,
		cliente: number,
		inicio: string
	): Observable<Array<AgendaTurma>> {
		const params: any = {};
		if (cliente) {
			params.cliente = cliente;
			params.inicio = inicio;
		}
		return this.restApi
			.get(
				`negociacao/agenda-turmas/${modalidade}/${nivel}/${professor}/${periodo}/${disponibilidade}`,
				{ params }
			)
			.pipe(
				map((response: ApiResponseList<AgendaTurma>) => {
					return response.content;
				})
			);
	}

	configAgendaTurmas(modalidade: number): Observable<any> {
		return this.restApi
			.get(`negociacao/agenda-turmas/configs/${modalidade}`)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				})
			);
	}

	dadosNegociacao(
		plano: number,
		situacao: string,
		contrato: number
	): Observable<Negociacao> {
		return this.restApi
			.get(`negociacao/dados-plano/${plano}/${situacao}/${contrato}`)
			.pipe(
				map((response: ApiResponseSingle<Negociacao>) => {
					return response.content;
				})
			);
	}

	check(
		cliente: number,
		contrato: any,
		verificarEmpresaEcontratoResponsavelRematricula?: boolean
	): Observable<any> {
		const params: any = {};
		if (verificarEmpresaEcontratoResponsavelRematricula !== undefined) {
			params.verificarEmpresaEContratoResponsavelRematricula =
				verificarEmpresaEcontratoResponsavelRematricula;
		}
		return this.restApi
			.get(`negociacao/check/${cliente}/${contrato}`, { params })
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				})
			);
	}

	simular(configs): Observable<any> {
		return this.restApi.post(`negociacao/simular`, configs).pipe(
			map((responses: ApiResponseSingle<any>) => {
				return responses.content;
			}),
			catchError((error) => {
				const realError = typeof error === "function" ? error() : error;
				return of({
					status: realError.error.meta ? true : false,
					error: realError.error.meta ? realError.error.meta.message : "",
				});
			})
		);
	}

	validarAulasFixar(cliente, duracao, horarios): Observable<any> {
		return this.restApi
			.post(`negociacao/validar-aulas-fixar/${cliente}/${duracao}`, horarios)
			.pipe(
				map((responses: ApiResponseSingle<any>) => {
					return responses.content;
				}),
				catchError((error) =>
					of({
						status: error.error.meta ? true : false,
						error: error.error.meta ? error.error.meta.message : "",
					})
				)
			);
	}

	recursoHabilitado(recurso): Observable<any> {
		return this.restApi.get(`info-migracao/habilitado/${recurso}`).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	recursoPadraoEmpresa(recurso): Observable<any> {
		return this.restApi
			.get(`info-migracao/recurso-padrao-empresa/${recurso}`)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				})
			);
	}

	negociacaoHabilitada(): Observable<any> {
		return this.restApi.get(`info-migracao/negociacao-habilitada`).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	alterarRecurso(recurso, value, origem): Observable<any> {
		const params: any = {};
		params.origem = origem;
		return this.restApi
			.post(`info-migracao/${recurso}/${value}`, {}, { params })
			.pipe(
				map((responses: ApiResponseSingle<any>) => {
					return responses.content;
				}),
				catchError((error) =>
					of({
						status: error.error.meta ? true : false,
						error: error.error.meta ? error.error.meta.message : "",
					})
				)
			);
	}

	consultarRecurso(recurso): Observable<any> {
		return this.restApi.get(`info-migracao/consultar/${recurso}`).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	gravar(configs): Observable<any> {
		return this.restApi.post(`negociacao/gravar`, configs).pipe(
			map((responses: ApiResponseSingle<any>) => {
				return responses.content;
			}),
			catchError((error) =>
				of({
					status: error.error.meta ? true : false,
					error: error.error.meta ? error.error.meta.message : "",
				})
			)
		);
	}

	clientes(filtro): Observable<Array<any>> {
		const params: any = {};
		params.nome = filtro;
		return this.restApi.get(`negociacao/clientes`, { params }).pipe(
			map((responses: ApiResponseList<any>) => {
				return responses.content;
			})
		);
	}

	public chamarServletCarteirinhaCliente(body: any): Observable<any> {
		return this.restApi
			.post("negociacao/carteirinha-servlet-zw", body, {})
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				}),
				catchError((error) =>
					of({
						error: error.error.meta ? error.error.meta.message : "",
					})
				)
			);
	}
}
