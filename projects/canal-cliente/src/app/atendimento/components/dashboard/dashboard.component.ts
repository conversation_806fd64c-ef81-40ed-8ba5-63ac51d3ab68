import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { CclModalService } from "canal-cliente/app/servicos/ccl-modal.service";
import { fadeIn } from "ui-kit";
import { trigger } from "@angular/animations";
import { PendenciaService } from "./pendencia.service";
import { EmpresaFinanceiro } from "canal-cliente/app/shared/empresa-financeiro.model";
import { ActivatedRoute, Router } from "@angular/router";

import { PlanoSucessoService } from "canal-cliente/app/planos-sucesso/components/services/plano-sucesso.service";
import { IndicadorPlanoSucesso } from "canal-cliente/app/planos-sucesso/components/models/plano-sucesso";
import { CclSessionService } from "canal-cliente/app/ccl-session.service";
import { MovideskService } from "canal-cliente/app/atendimento/components/dashboard/movidesk.service";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService } from "sdk";

@Component({
	selector: "ccl-dashboard",
	templateUrl: "./dashboard.component.html",
	styleUrls: ["./dashboard.component.scss"],
	animations: [trigger("fadeIn", fadeIn(":enter"))],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardComponent implements OnInit {
	public empresa: EmpresaFinanceiro;
	public zwBaseUrl: string;
	public indicadoresPlanoSucesso: IndicadorPlanoSucesso;

	possuiCadastroCentralAjuda: boolean;
	usernameEncontrado: string;

	@ViewChild("propositoComCliente", { static: true })
	propositoComClienteRef: TemplateRef<any>;

	constructor(
		private cclModal: CclModalService,
		private pendenciaService: PendenciaService,
		private cclSession: CclSessionService,
		private route: ActivatedRoute,
		private router: Router,
		private planoSucessoService: PlanoSucessoService,
		private movideskService: MovideskService,
		private cd: ChangeDetectorRef,
		private snotiy: SnotifyService,
		private clientDiscoveryService: ClientDiscoveryService
	) {
		this.zwBaseUrl = this.cclSession.cclZwUrl;
	}

	ngOnInit() {
		this.empresa = this.cclSession.empresaFinanceiro;

		this.planoSucessoService.obterIndicadores(6).subscribe((indicador) => {
			this.indicadoresPlanoSucesso = indicador;
		});

		const usuarioBase = this.cclSession.loggedUser;
		if (usuarioBase.username === "PACTOBR") {
			this.possuiCadastroCentralAjuda = true;
			this.usernameEncontrado =
				"pacto_" + this.cclSession.empresaFinanceiro.codigoFinanceiro;
			console.log("Username encontrado: " + this.usernameEncontrado);
			this.cd.detectChanges();
		} else {
			this.movideskService
				.obterUsuariosPeloIdFavorecido(this.cclSession.empresaFinanceiro)
				.subscribe((persons) => {
					persons.forEach((person) => {
						if (
							person.userName.toLowerCase() ===
								usuarioBase.username.toLowerCase() ||
							person.userName.toLowerCase() === usuarioBase.email.toLowerCase()
						) {
							this.possuiCadastroCentralAjuda = true;
							this.usernameEncontrado = person.userName;
						}
					});
					this.cd.detectChanges();
				});
		}
	}

	get linkFormulario() {
		return "https://forms.gle/oazsUHonn2To2Mtd8";
	}

	get nomeAcademia() {
		return this.cclSession.empresaFinanceiro.nomeFantasia;
	}

	get linkCentralAjuda() {
		return "https://pactosolucoes.com.br/ajuda/";
	}

	acessarMovidesk() {
		const usuarioBase = this.cclSession.loggedUser;
		const ehPacto = usuarioBase.username === "PACTOBR";
		this.movideskService
			.obterUrlAcessoExterno(
				this.usernameEncontrado,
				this.cclSession.empresaFinanceiro,
				ehPacto
			)
			.subscribe((resposta) => {
				if (resposta === "usuario_nao_existe") {
					this.snotiy.error(
						`Usuário (${this.usernameEncontrado}) não existe no movidesk;`
					);
				} else {
					window.open(resposta.urlRedirect, "_blank");
				}
			});
	}

	get isUsarChatMovDesk(): boolean {
		return this.clientDiscoveryService.isUsarChatMovDesk();
	}
}
