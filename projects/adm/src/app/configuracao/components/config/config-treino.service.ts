import { Injectable, EventEmitter, OnInit } from "@angular/core";
import {
	TreinoApiConfiguracoesTreinoService,
	ConfigTreinoPayload,
	ConfiguracoesAvaliacao,
	ConfiguracoesAplicativos,
	ConfiguracoesTreino,
} from "treino-api";
import { ConfigItemType } from "../inputs/form-configuracoes.model";
import { SubGrupoInputs } from "./model/sub-group-inputs.model";
import { FormControl } from "@angular/forms";
import { ConfigModuloSubGroup } from "./model/config-module-subgroup.model";
import { forkJoin, from } from "rxjs";
import { SessionService } from "sdk";
import { SnotifyService } from "ng-snotify";
import { ModalService } from "@base-core/modal/modal.service";
import { PactoDataGridConfig } from "ui-kit";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
@Injectable({
	providedIn: "root",
})
export class ConfigTreinoService implements OnInit {
	detectChanges: EventEmitter<boolean> = new EventEmitter();
	loaded: EventEmitter<boolean> = new EventEmitter();
	configTreinoPayload: ConfigTreinoPayload = new ConfigTreinoPayload();
	formTipoRML: FormControl = new FormControl();
	formControlUsarOrdemDobrasCultaneas: FormControl = new FormControl();
	formGridOrdenacaoDobrasCultaneas: FormControl = new FormControl();
	formControlPermitirCriarTreinoAutomatizadoIA: FormControl = new FormControl();
	formControlTempoAprovacaoAutomatica: FormControl = new FormControl();
	formControlHabilitarObrigatoriedadeAprovacaoProfessor: FormControl =
		new FormControl();
	formControlPermitirAlunoCriarTreinoIaApp: FormControl = new FormControl();

	formControlHabilitarModuloAulasTurmas: FormControl = new FormControl();
	formControlSaldoAulasColetivas: FormControl = new FormControl();
	formControlPermitirMarcacoesRemarcacoesTurmasApp: FormControl =
		new FormControl();

	formControlEmpresaUtilizaAppPersonalizado: FormControl = new FormControl();
	formControlAppPersonalizadoNome: FormControl = new FormControl();
	formControlAppPersonalizadoUrl: FormControl = new FormControl();

	integradoZw: boolean = false;
	nr_aulas: any;
	pactobr = false;
	orderDobras: any[] = [];
	mgb: any;

	constructor(
		private treinoConfigApi: TreinoApiConfiguracoesTreinoService,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private appModal: ModalService,
		private session: SessionService
	) {
		forkJoin(
			this.treinoConfigApi.getConfiguracoesAplicativo(),
			this.treinoConfigApi.getConfiguracoesAula(),
			this.treinoConfigApi.getConfiguracoesAvaliacao(),
			this.treinoConfigApi.getConfiguracoesGerais(),
			this.treinoConfigApi.getConfiguracoesGestao(),
			this.treinoConfigApi.getConfiguracoesManutencao(),
			this.treinoConfigApi.getConfiguracoesNotificacao(),
			this.treinoConfigApi.getConfiguracoesTreino(),
			this.treinoConfigApi.getConfiguracoesIa()
		).subscribe(
			([
				configAplicativo,
				configAula,
				configAvaliacao,
				configGerais,
				configGestao,
				configManutencao,
				configNotificao,
				configTreino,
				configIa,
			]) => {
				this.configTreinoPayload.initConfiguracoesAplicativos(configAplicativo);
				this.configTreinoPayload.initConfiguracoesAulas(configAula);
				this.configTreinoPayload.initAvaliacaoFisica(configAvaliacao);
				this.configTreinoPayload.initConfigGerais(configGerais);
				this.configTreinoPayload.initConfigGestao(configGestao);
				this.configTreinoPayload.initConfigManutencao(configManutencao);
				this.configTreinoPayload.initConfigNotificacao(configNotificao);
				this.configTreinoPayload.initConfigTreino(configTreino);
				this.configTreinoPayload.initConfigIa(configIa);
				this.selectConfigRml(configAvaliacao);
				this.obterOrdensCultaneas();
			},
			(error) => {
				this.notificationService.error(
					"Falha ao consultar configurações do treino" + error.url
				);
				console.error("Falha ao consultar configurações do treino", error);
			},
			() => {
				this.loaded.emit(true);
			}
		);

		this.prepareDetectChanges();
	}

	ngOnInit(): void {
		this.integradoZw = this.sessionService.integracaoZW;
		this.nr_aulas = this.configTreinoPayload.nr_aula_experimental_aluno;
		this.pactobr = this.session.loggedUser.username.toLowerCase() === "pactobr";
	}

	private obterOrdensCultaneas() {
		if (!this.configTreinoPayload && !this.configTreinoPayload.ordens_dobras) {
			return;
		}
		this.orderDobras = JSON.parse(this.configTreinoPayload.ordens_dobras);
	}

	selectConfigRml(config) {
		if (config.cfg_rml_separado === true) {
			this.formTipoRML = new FormControl("cfg_rml_separado");
		} else if (
			config.cfg_rml_opcoes === true &&
			config.cfg_rml_separado === false
		) {
			this.formTipoRML = new FormControl("cfg_rml_opcoes");
		} else {
			this.formTipoRML = new FormControl("nenhum");
		}
	}

	prepareDetectChanges() {
		[this.formTipoRML].forEach((formControl) => {
			from(formControl.valueChanges).subscribe(() => {
				this.detectChanges.emit(true);
			});
		});
	}

	getOptionsProdutosAvaliacao() {
		if (!this.configTreinoPayload || !this.configTreinoPayload.produtos) {
			return [];
		}
		const produtosAvaliacao = JSON.parse(this.configTreinoPayload.produtos);
		return produtosAvaliacao.map((produto) => {
			return {
				id: produto.value,
				label: produto.label,
			};
		});
	}

	private isMgbAtiva() {
		this.treinoConfigApi
			.getConfiguracoesIntegracoesListaMGB()
			.subscribe((res) => (this.mgb = res));
		if (this.mgb) {
			const find = this.mgb.find(
				(x) => x.empresa === Number(this.session.empresaId)
			);
			const tokenMgb = find ? find.token : null;
			return tokenMgb !== null && tokenMgb.length > 0;
		}
		return false;
	}

	getInputs(): SubGrupoInputs[] {
		return [
			this.getInputsAplicativo(),
			this.getInputsAulas(),
			this.getInputsAvaliacaoFisica(),
			this.getInputsConfiguracoes(),
			this.getInputsGestao(),
			this.getInputsManutencao(),
			this.getInputsNotificacao(),
			this.getInputsTreino(),
			this.getInputsIa(),
		];
	}

	getInputsAplicativo(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_APLICATIVO,
			inputs: [
				{
					name: "mobile_sempre_atualizar_carga_ficha",
					title: "Mobile sempre atualizar a carga da ficha",
					description: `
            Se marcada, sempre que o aluno alterar a carga no aplicativo, a carga será automaticamente alterada também na ficha do aluno. 
            Atenção: Essa configuração é válida apenas para o aplicativo antigo
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitir_reagendamento_por_aluno",
					title: "Permitir que o aluno faça reagendamento:",
					description: `
            Com essa opção marcada, o aplicativo mobile irá permitir que o aluno realize o reagendamento de algum tipo de evento que o professor agendou o aluno.
            Ex: O aluno poderia reagendar pelo aplicativo uma Avaliação física
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "modulo_treinar",
					title: "Habilitar o módulo treinar",
					description: `
            Habilitar o módulo treinar na tela principal do aplicativo, permitindo ao aluno a execução do seu treino
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "habilitar_crossfit",
					title: "Habilitar módulo Cross",
					description: `
            Habilita o módulo Cross na barra de ferramentas do aplicativo, permitindo o aluno visualizar o seu WOD
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "habilitar_ver_wod_todas_empresas_app",
					title: "Alunos podem visualizar WOD de outras unidades da empresa",
					description: `
            Esta opção permite os alunos verem o WOD de outras unidades da sua empresa. Caso esteja desabilitada, os alunos verão apenas o WOD da unidade em que estão logados no aplicativo.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "aluno_marcar_propria_aula",
					title: "Permitir o próprio aluno marcar sua aula (aulas coletivas)",
					description: `
            Com esta opção marcada o próprio aluno poderá participar da aula desejada, marcando-a através do aplicativo ou do RetiraFicha. 
            Com a configuração desmarcada, ao clicar em uma aula disponível, o aplicativo notificar o aluno que a operação não poderá ser realizada. 
            No caso do RetiraFicha, será impresso apenas o QRCODE ao clicar na aula desejada
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "proibir_marcar_aula_parcela_vencida",
					title:
						"Proibir o aluno marcar aula, caso exista mensagem de parcela vencida",
					description: `
            Com esta opção marcada o aluno será impedido de marcar aulas coletivas pelo Aplicativo, 
            Agenda e RetiraFicha, caso exista alguma mensagem de bloqueio do tipo Parcela Vencida em Aberto.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "proibir_buscar_programa_parcela_vencida",
					title:
						"Proibir o aluno buscar programa, caso exista mensagem de parcela vencida",
					description: `
            Com esta opção marcada o aluno será impedido de marcar aulas coletivas pelo Aplicativo, 
            Agenda e RetiraFicha, caso exista alguma mensagem de bloqueio do tipo Parcela Vencida em Aberto.`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "nao_exibir_numero_de_vezes_no_app",
					title: "Não exibir o número de vezes da Modalidade no App",
					description: `
            Com esta opção marcada não deve aparecer o número de vezes da Modalidade que estiver no contrato do aluno.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "nome_aplicativo_para_envio_email",
					title: "Nome do aplicativo no envio de E-mail",
					type: ConfigItemType.TEXT,
				},
				{
					name: "link_app_email",
					title: "Link do app no e-mail",
					description: `Define qual vai ser o aplicativo que será redirecionado no e-mail enviado ao aluno ou colaborador.`,
					validators: [],
					type: ConfigItemType.SELECT,
					options: [
						{ id: "TREINO", label: "Treino" },
						{ id: "MINHA_ACADEMIA", label: "Minha Academia" },
						{ id: "MEU_BOX", label: "Meu Box" },
					],
				},
				{
					name: "modulo_aulas",
					title: "Habilitar módulo Aulas/Turmas",
					description: `Habilita o módulo aulas na tela principal do aplicativo`,
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlHabilitarModuloAulasTurmas,
					autoControlCurrentValue: true,
					children: [
						{
							name: "modulo_aulas_aba_saldo",
							title: "Saldo em aulas coletivas",
							description:
								"Informa a quantidade/saldo de créditos que o aluno possui (esta opção só se aplica para planos do tipo Crédito Treino)",
							type: ConfigItemType.CHECKBOX,
							formControl: this.formControlSaldoAulasColetivas,
							autoControlCurrentValue: true,
						},
						{
							name: "modulo_aulas_aba_turmas",
							title: "Permitir marcações e remarcações de turmas pelo APP",
							description:
								"Permite que o aluno faça marcações e remarcações de suas turmas diretamente pelo APP",
							type: ConfigItemType.CHECKBOX,
							formControl:
								this.formControlPermitirMarcacoesRemarcacoesTurmasApp,
							autoControlCurrentValue: true,
						},
					],
				},
				{
					name: "dias_mostrar_totem",
					title: "Tempo máximo para aparecer na agenda do app",
					description: `
            Configuração que determina por quanto tempo a partir da data da consulta, as aulas irão ser exibidas no Retira Fichas e nos Aplicativos. 
            Ex.: Caso configure 4 horas, apenas serão apresentadas as aulas que acontecerão em 4 horas.
            `,
					type: ConfigItemType.SELECT,
					options: [
						{ id: "15", label: "15 Minutos" },
						{ id: "60", label: "1 Hora" },
						{ id: "240", label: "4 Horas" },
						{ id: "1440", label: "Dia" },
						{ id: "10080", label: "Semana" },
						{ id: "43200", label: "Mês" },
					],
				},
				{
					name: "habilitar_fila_espera",
					title: "Habilitar fila de espera no App",
					description: `O recurso fila de espera terá a função de inserir o aluno em uma lista de espera, com a função de identificar que a aula está cheia e bloquear novos agendamentos, 
            perguntando se o aluno deseja entrar na fila de espera ou agendar em outro horário
            `,
					type: ConfigItemType.CHECKBOX,
				},
			],
		};
	}

	getInputsAulas(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_AULAS,
			inputs: [
				{
					name: "controlar_por_freepass",
					title: "Aulas experimentais por freepass",
					description: `
            Ao marcar esta opção o aluno poderá realizar aula experimental apenas se tiver um freepass lançado, 
            além disso alunos de gympass poderão ingressar nas aulas coletivas após efetuar o check-in
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "nr_aula_experimental_aluno",
					title: "Aulas experimentais por aluno",
					description: `
            Este número define a quantidade de aulas experimentais que cada aluno possuí. São consideradas aulas experimentais
            as turmas com modalidades que o aluno não adquiriu em seu contrato.
          `,
					type: ConfigItemType.TEXT,
				},
				{
					name: "minutos_agendar_com_antecedencia",
					title: "Tempo para agendar com antecedência",
					description: `
            Tempo em que o aluno pode marcar sua aula com antecedência, ou seja, em quanto tempo o aluno pode agendar antes do início da aula. 
            Caso você não deseje validar essa restrição de antecedência, preencha com 0 minutos, permitindo o aluno marcar aulas em qualquer dia.
          `,
					type: ConfigItemType.NUMBER,
					min: 0,
					max: 99999,
					maxlength: 5,
					typeDescription: "minuto(s)",
					placeholder: "minuto(s)",
				},
				{
					name: "minutos_desmarcar_com_antecedencia",
					title: "Tempo para desmarcar com antecedência",
					description: `
            Tempo em minutos que o aluno pode desmarcar sua aula com antecedência, 
            ou seja, em até quanto tempo o aluno pode desmarcar antes do início da aula.
          `,
					type: ConfigItemType.NUMBER,
					min: 0,
					max: 99999,
					maxlength: 5,
					typeDescription: "minuto(s)",
					placeholder: "minuto(s)",
				},
				{
					name: "validar_modalidade",
					title: "Validar Modalidade",
					description: `
            Validar modalidade para marcar aula: Caso esteja marcado, apenas alunos com a modalidade no contrato poderão fazer a aula da modalidade correspondente, 
            se o aluno não tiver a modalidade, será contabilizado como aula experimental.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "nr_validar_vezes_modalidade",
					title: "Quantidade máxima de agendamentos por modalidade",
					description: `Restrinja quantos agendamentos concomitantes de aula o aluno pode marcar previamente dentro da mesma modalidade. Caso você não deseje restringir, deixe em branco,
						isso permitirá ao aluno marcar quantas aulas quiser na mesma modalidade`,
					type: ConfigItemType.TEXT,
					maxlength: 6,
				},
				{
					name: "validar_horario_contrato",
					title: "Validar horário do contrato",
					description: `
            Caso esteja marcado, o aluno não conseguirá marcar uma aula fora do horário de seu contrato, a menos que seja uma aula experimental.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitir_aluno_marcar_aula_por_tipo_modalidade",
					title: "Permitir check-in em outras unidades da rede",
					description: `
            Marque esta opção para que seu aluno consiga agendar uma aula em outra unidade. Caso a opção "Validar Modalidade" esteja marcada, 
            o aluno só conseguirá realizar as mesmas aulas do contrato (uma única modalidade para várias empresas) 
            ou se as modalidades do contrato e da aula estiverem vinculadas ao mesmo "Tipo de Modalidade". 
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "bloquear_mesmo_ambiente",
					title: "Bloquear marcações conflitantes no mesmo ambiente",
					description: `
            Com esta configuração marcada, a agenda não vai permitir que sejam realizadas marcações em aulas distintas num mesmo ambiente e no mesmo horário.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "bloquear_aula_coletiva_nao_pertence_modalidade",
					title:
						"Bloquear visualização de aula coletiva que não pertence a modalidade",
					description: `
            Marque esta opção para que seja apresentadas apenas aulas coletivas pertencentes a modalidade.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "desmarcar_aulas_futuras_parcela_atrasada",
					title: "Desmarcar aulas futuras em caso de parcela atrasada",
					description: `
            Configure esse número de dias e o cliente terá até esse limite para fazer o pagamento sem interferir nos agendamentos futuros, 
            caso extrapole esse limite todas as aulas futuras serão desmarcadas, independentemente de ser coletiva ou turma.
          `,
					type: ConfigItemType.TEXT,
				},
				{
					name: "manter_reposicao_aula_coletiva",
					title: "Manter reposições e aula coletiva na renovação de contrato",
					description: `
            Caso marcado, se o aluno ainda tiver reposições dentro do prazo, ele poderá utilizar essas reposições no novo contrato. 
            No entanto, se essa opção estiver desabilitada, assim que o contrato finalizar, o aluno perderá o direito de realizar essas reposições.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "limite_dias_reposicao_aula_coletiva",
					title: "Limite de dias para realizar reposição de aula coletiva",
					description: `
            Se houver um limite de dias e a desmarcação ocorrer em um determinado dia, o aluno poderá repor a aula dentro do prazo estipulado. 
            Após esse prazo, caso ele ainda não tenha reposto a aula, perderá o direito a essa reposição.
          `,
					type: ConfigItemType.NUMBER,
					min: 0,
					max: 99999,
					maxlength: 5,
					placeholder: "dia(s)",
				},
			],
		};
	}

	getInputsAvaliacaoFisica(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_AVALIACAO_FISICA,
			inputs: [
				{
					name: "cfg_objetivos_anamnese",
					title: "Utilizar objetivos e anamnese:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_peso_altura_pa_fc",
					title:
						"Utilizar peso, altura, pressão arterial e frequência cardíaca:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_parq",
					title: "Utilizar questionário PAR-Q:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_dobras_cutaneas",
					title: "Utilizar dobras cutâneas:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_perimetria",
					title: "Utilizar perimetria:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_composicao_corporal",
					title: "Utilizar composição corporal:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_flexibilidade",
					title: "Utilizar teste de flexibilidade:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_postura",
					title: "Utilizar avaliação postural:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_tipo_rml",
					title: "Tipo de RML que deseja utilizar:",
					type: ConfigItemType.SELECT,
					options: [
						{ id: "nenhum", label: "" },
						{ id: "cfg_rml_separado", label: "Padrão" },
						{ id: "cfg_rml_opcoes", label: "Separado Homem/Mulher" },
					],
					formControl: this.formTipoRML,
				},
				{
					name: "cfg_vo2max",
					title: "Utilizar testes de VO2 máximo:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_recomendacoes",
					title: "Utilizar recomendações do profissional:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_ventilometria",
					title: "Utilizar informações de ventilometria:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_testes_campo",
					title: "Utilizar testes de VO2 máximo de campo:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_teste_bike",
					title: "Utilizar testes de VO2 máximo na Bike:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_somatotipia",
					title: "Utilizar somatotipia:",
					description: ``,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_teste_queens",
					title: "Utilizar testes de VO2 com protocolo Queens College:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cfg_importacao_biosanny",
					title: "Habilitar upload avaliação física BioSanny:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "lancar_agendamento_proxima_avaliacao",
					title: "Lançar agendamento para próxima avaliação física:",
					description: ``,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "usar_pressao_sistolica_diastolica",
					title: "Usar pressão arterial sistólica e diastólica:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "modelo_evolucao_fisica",
					title: "Qual avaliação física deseja comparar na evolução física.",
					description: `
            Você pode escolher se deseja comparar a primeira avaliação com a última avaliação ou pode escolher entre primeira, penúltima e última. 
            Lembrando que somente a opção de primeira, penúltima e última levará em consideração todos os itens da evolução.
          `,
					validators: [],
					type: ConfigItemType.SELECT,
					options: [
						{ id: "PRIMEIRA_ULTIMA", label: "Primeira e última" },
						{
							id: "PRIMEIRA_PENULTIMA_ULTIMA",
							label: "Primeira, penúltima e última",
						},
					],
				},
				{
					name: "obrigar_campos_dobras_bioimpedancia",
					title:
						"Tornar obrigatório campos de dobras do protocolo bioimpedância:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "lancar_produto_avaliacao",
					title: "Lançar produto para próxima avaliação física:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "validar_produto_avaliacao",
					title: "Validar produto ao lançar avaliação física:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "lancar_produto_avaliacao_data_vencimento",
					title:
						"Data de vencimento da parcela do produto avaliação física igual a data do agendamento:",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "produto_avaliacao",
					title: "Produto de Avaliação Física no ADM:",
					type: ConfigItemType.SELECT,
					options: this.getOptionsProdutosAvaliacao(),
				},
				{
					name: "usar_ordem_dobras_cutaneas",
					title: "Definir ordem das dobras cutâneas na avaliação física:",
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlUsarOrdemDobrasCultaneas,
					autoControlCurrentValue: true,
					children: [
						{
							formControl: this.formGridOrdenacaoDobrasCultaneas,
							type: ConfigItemType.GRID,
							dataGridConfig:
								this.getOrdenacaoDobrasCultaneasAvaliacaoFisicaGrid(),
							alternatingColors: "first",
						},
					],
				},
			],
		};
	}

	private updateGridData(): void {
		this.formGridOrdenacaoDobrasCultaneas.setValue({
			size: this.orderDobras.length,
			content: this.orderDobras.map((dobra) => {
				return {
					codigo: dobra.codigo,
					ordem: dobra.ordem,
					dobra: DobraCutanea[dobra.dobrasEnum as keyof typeof DobraCutanea],
				};
			}),
			totalElements: this.orderDobras.length,
			number: 0,
		});
		this.detectChanges.emit(true);
	}

	getOrdenacaoDobrasCultaneasAvaliacaoFisicaGrid(): PactoDataGridConfig {
		this.updateGridData();

		return new PactoDataGridConfig({
			pagination: false,
			quickSearch: false,
			exportButton: false,
			ordenable: true,
			onDragEnd: (event: CdkDragDrop<string[], string[]>, data: Array<any>) => {
				const previousIndex = event.previousIndex;
				const currentIndex = event.currentIndex;
				moveItemInArray(this.orderDobras, previousIndex, currentIndex);
				this.atualizaValorOrdem();
				this.updateGridData();
				this.formGridOrdenacaoDobrasCultaneas.markAsDirty();
				this.detectChanges.emit(true);
			},
			dataFormControl: this.formGridOrdenacaoDobrasCultaneas,
			columns: [
				{
					nome: "dobra",
					titulo: "Descrição",
					visible: true,
					ordenavel: true,
					inputType: "text",
				},
			],
		});
	}

	private atualizaValorOrdem(): void {
		this.orderDobras.forEach((dobra, index) => {
			dobra.ordem = index;
		});
	}

	getInputsConfiguracoes(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_CONFIGURACOES,
			inputs: [
				{
					name: "validar_agenda_aulacheia",
					title: "Validar se o cliente já possui compromisso no mesmo horário",
					description: `
            Quando o aluno através do aplicativo ou o consultor pelo sistema, tentar incluir um evento da agenda do treino, 
            o sistema validará se o aluno possui uma aula coletiva no mesmo horário e vice-versa. O sistema não irá permitir 
            a inclusão da operação informando que o aluno já possui compromisso naquele horário. Caso não queira esta validação, 
            basta desmarcar esta configuração
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "troca_nomenclatura_crossfit",
					title: "Usar nomenclatura para treinamento funcional",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "configuracoes_treino_replicar_rede_empresa",
					title: "Permite replicar configurações rede empresa",
					description: `
            Marcada uma nova aba irá aparecer "Replicar Empresa", onde se pode selecionar uma empresa da rede para replicar as configurações do treino.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "duracao_aluno_na_academia",
					title:
						'Tempo em que o aluno permanece no filtro "Na academia" (Em minutos)',
					type: ConfigItemType.NUMBER,
					placeholder: "Em minutos",
				},
			],
		};
	}

	getInputsGestao(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_GESTAO,
			inputs: [
				{
					name: "periodo_usado_bi",
					title: "Período abrangido no BI (em dias):",
					type: ConfigItemType.NUMBER,
				},
				{
					name: "inativos_a_x_dias",
					title: "Inativos a X dias (Deixar '0' para mostrar todos):",
					type: ConfigItemType.NUMBER,
					initOption: 0,
				},
				{
					name: "somente_aluno_contrato_desistente",
					title:
						'Considerar apenas alunos com situação "Inativo Desistente", no contador de "Alunos Inativos" do B.I. Gestão Carteira:',
					description: `
           Com a configuração marcada, apenas serão considerados alunos com a situação de Inativo Desistente no indicador, 
           caso deseje incluir no cálculo os alunos vencidos, cancelados e trancados, desmarque essa configuração.
          `,
					type: ConfigItemType.CHECKBOX,
				},
			],
		};
	}

	getInputsManutencao(): SubGrupoInputs {
		const inputsManutencao = [
			{
				title: "Excluir alunos que não existem no ZW:",
				description: `
          Exclui todos os alunos do treino, que tem um código de cliente que não existe no ZW. O processo vai excluir todo perfil, programa de treino, avaliações e vínculos. 
          Deve ser utilizado para possíveis erros de importação ou erros momentâneos.
        `,
				type: ConfigItemType.BUTTON,
				typeDescription: "Executar",
				onClick: () => this.excluirDuplicados(),
			},
			{
				title: "Atualizar aulas experimentais para todos os alunos:",
				description: `Atualizar todos os alunos com o número de aulas experimentais da configuração do treino`,
				type: ConfigItemType.BUTTON,
				typeDescription: "Executar",
				onClick: () => this.atualizarNumeroAulasExperimentais(),
			},
			{
				name: "aplicativo_personalizado",
				title: "Empresa utiliza aplicativo personalizado?",
				type: ConfigItemType.CHECKBOX,
				formControl: this.formControlEmpresaUtilizaAppPersonalizado,
				autoControlCurrentValue: true,
				children: [
					{
						title: "Nome do aplicativo",
						name: "aplicativo_personalizado_nome",
						type: ConfigItemType.TEXT,
						formControl: this.formControlAppPersonalizadoNome,
						autoControlCurrentValue: true,
					},
					{
						title: "URL do leadpage",
						name: "aplicativo_personalizado_url",
						type: ConfigItemType.TEXT,
						formControl: this.formControlAppPersonalizadoUrl,
						autoControlCurrentValue: true,
					},
				],
			},
		];

		if (this.isMgbAtiva()) {
			inputsManutencao.push({
				title: "Sincronizar alunos MGB:",
				description: `
          Este processo irá verificar todos alunos ativos que não estão sincronizados com o MGB e fará a sincronização dos mesmos.
        `,
				type: ConfigItemType.BUTTON,
				typeDescription: "Sincronizar",
				onClick: () => this.sincronizarMGB(),
			});

			if (this.integradoZw) {
				inputsManutencao.push({
					title: "Sincronizar Booking Gympass:",
					description: `
          Força a sincronização das aulas consfiguradas com a integração Booking Gympass.
        `,
					type: ConfigItemType.BUTTON,
					typeDescription: "Sincronizar",
					onClick: () => this.sincronizarGympass(),
				});
			}
		}

		return {
			subgrupo: ConfigModuloSubGroup.TRE_MANUTENCAO,
			inputs: inputsManutencao,
		};
	}

	getInputsNotificacao(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_NOTIFICACOES,
			inputs: [
				{
					name: "iniciou_treino",
					title: "Iniciou o treino",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno acaba de iniciar seu treino,
            seja pelo aplicativo do smartphone ou pela retirada de ficha.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "aumentou_carga",
					title: "Aumentou carga",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno aumenta a carga em algum dos exercícios 
            que o mesmo executou pelo aplicativo do smartphone.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "diminuiu_carga",
					title: "Diminuiu carga",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno diminui a carga em algum dos exercícios 
            que o mesmo executou pelo aplicativo do smartphone
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "aluno_chegou",
					title: "Chegou",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno acabou de chegar na academia
            (Passou na catraca) para realizar seu treino.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "agenda",
					title: "Lembrete agendamento",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno possui algum agendamento marcado no TreinoWeb.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "agendamento_confirmado",
					title: "Agendamento confirmado",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno confirma algum agendamento marcado, 
            pelo aplicativo, ou quando algum professor confirma um agendamento marcado, pelo TreinoWeb.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "solicitar_renovacao",
					title: "Solicitar renovar treino",
					description: `
            Habilita/Desabilita as notificações para o aluno quando o aluno precisa renovar seu treino.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "lembrar_aluno_compromisso",
					title: "Lembrete Compromisso",
					description: `
            Habilita/Desabilita as notificações para o aluno via SMS de quando o aluno passou muito tempo sem Treinar, para que o mesmo retome suas atividades.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "agendamento_alterado",
					title: "Compromisso Alterado",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno possui algum agendamento que fora reagendado ou remarcado.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "aluno_em_risco",
					title: "Aluno chegou e possui Índice de Risco",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno sobe de nível no grupo de risco.`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "aluno_agendou",
					title: "Aluno reagendou",
					description: `
            Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno possui algum agendamento que fora reagendado ou remarcado.
          .`,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "sms_notificacao",
					title: "Enviar SMS nas notificações",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "numero_dias_notificar_treino_vencido",
					title: "Número de dias para notificar o treino vencido:",
					description: `
            Essa configuração define ate quantos dias o cliente deve receber a notificação de treino vencido.
          `,
					type: ConfigItemType.NUMBER,
				},
			],
		};
	}

	getInputsTreino(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_TREINO,
			inputs: [
				{
					name: "emitir_ficha_apos_vencimento_treino",
					title: "Emitir ficha após vencimento do treino",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "numero_impressao_ficha",
					title: "Número máximo de impressões de fichas por dia",
					type: ConfigItemType.NUMBER,
				},
				{
					name: "bloquear_impressao_ficha_apos_todas_execucoes",
					title:
						"Bloquear impressão após o aluno atingir a quantidade de execuções previstas",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "dias_antes_vencimento",
					title:
						"Quantidade de dias para identificar um programa de treino como a renovar.",
					description: `
            Aqui você pode definir a quantidade de dias que o programa de treino entra no status de a renovar, 
            terá impacto no dashboard em Treino a Renovar e no relatório de carteira dos professores no campo Próx. Vencimento.
          `,
					type: ConfigItemType.NUMBER,
				},
				{
					name: "agrupamento_series_set",
					title:
						"Habilitar agrupamento de atividades para o método BI-Set e Tri-Set",
					description: `
            Ao marcar a configuração as atividades que são do tipo BI-set e TRI-set serão agrupadas e enviadas
            para os aplicativos/retira fichas como uma única atividade.
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitir_apenas_alunos_ativos",
					title:
						"Bloquear prescrição de treino para alunos inativos/visitantes",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "visualizar_mensagem_aviso",
					title: "Visualizar mensagem aviso",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitir_visualizar_wod",
					title: "Permitir visualizar wod",
					description: `
            Permitir que clientes Gympass ou TotalPass possam ver, mas não realizar o WOD antes de realizar o check-in
          `,
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitir_visualizar_cref",
					title: "Visualizar campo Cref",
					description: `Exibir o Cref do professor na ficha do aluno.`,
					type: ConfigItemType.CHECKBOX,
				},
			],
		};
	}

	getInputsIa(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.TRE_IA,
			inputs: [
				{
					name: "permitir_criar_treino_automatizado_ia",
					title: "Permitir criação de treino automatizado (I.A)",
					description: `Ao habilitar essa configuração, os professores poderão prescrever treinos por inteligência artificial para os alunos, com a devida permissão do perfil de usuário.`,
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlPermitirCriarTreinoAutomatizadoIA,
					autoControlCurrentValue: true,
					children: [
						{
							name: "tempo_aprovacao_automatica",
							title: "Tempo para aprovação automática",
							description:
								"Após esse período, o treino será aprovado e exibido ao aluno sem a ação do professor.",
							type: ConfigItemType.NUMBER,
							formControl: this.formControlTempoAprovacaoAutomatica,
							autoControlCurrentValue: true,
						},
						{
							name: "habilitar_obrigatoriedade_aprovacao_professor",
							title: "Obrigatório aprovação do professor",
							description:
								"Ao habilitar essa configuração, o treino só será exibido ao aluno após aprovação de um professor.",
							type: ConfigItemType.CHECKBOX,
							formControl:
								this.formControlHabilitarObrigatoriedadeAprovacaoProfessor,
							autoControlCurrentValue: true,
						},
						{
							name: "permitir_aluno_criar_treino_ia_app",
							title: "Permitir aluno Criar treino por IA - APP",
							description:
								"Ao habilitar essa configuração, o aluno poderá criar treino utilizando IA no App.",
							type: ConfigItemType.CHECKBOX,
							formControl: this.formControlPermitirAlunoCriarTreinoIaApp,
							autoControlCurrentValue: true,
						},
					],
				},
			],
		};
	}

	excluirDuplicados() {
		const titulo = "Remover alunos ?";
		const body =
			"Tem certeza que deseja remover todos os alunos que não tem vínculo com o ZW? Esse processo pode demorar alguns minutos.";
		const styles = `modal-title-sm modal-body-sm`;
		const appModal = this.appModal.confirmModal(
			titulo,
			body,
			"Remover",
			"sm",
			styles
		);

		appModal.result.then(() => {
			this.treinoConfigApi
				.executarExclusaoNaoExisteZw(this.sessionService.empresaId)
				.subscribe({
					next: (res) => {
						this.notificationService.success(
							"Manutenção executada com sucesso."
						);
					},
					error: (error) => {
						this.notificationService.error(error);
					},
				});
		});
	}

	atualizarNumeroAulasExperimentais() {
		const titulo =
			"Atualizar o número de aulas experimentais para todos os alunos ?";
		const body =
			"Tem certeza que deseja atualizar o número de aulas experimentais para todos os alunos. Esse processo pode demorar alguns minutos.";
		const styles = `modal-title-sm modal-body-sm`;
		const appModal = this.appModal.confirmModal(
			titulo,
			body,
			"Atualizar",
			"lg",
			styles
		);
		appModal.result.then(() => {
			this.treinoConfigApi
				.updateConfiguracaoAtualizarAulaExperimental(
					this.configTreinoPayload.nr_aula_experimental_aluno
				)
				.subscribe({
					next: (res) => {
						this.notificationService.success(
							"Manutenção executada com sucesso."
						);
					},
					error: (error) => {
						this.notificationService.error(error);
					},
				});
		});
	}

	sincronizarMGB(): void {
		const titulo = "Sincronizar alunos?";
		const body =
			"Tem certeza que deseja sincronizar os alunos ativos com o MGB? Esse processo pode demorar alguns minutos.";
		const styles = `modal-title-sm modal-body-sm`;
		const appModal = this.appModal.confirmModal(
			titulo,
			body,
			"Sincronizar",
			"lg",
			styles
		);
		appModal.result.then(() => {
			this.treinoConfigApi
				.sincronizarAlunosMGB(this.session.empresaId)
				.subscribe({
					next: (res) => {
						this.notificationService.success(
							"Sincronização executada com sucesso."
						);
					},
					error: (error) => {
						this.notificationService.error(
							"Não é possível sincronizar os alunos."
						);
					},
				});
		});
	}

	sincronizarGympass() {
		setTimeout(() => {
			const title = "Sincronizar Booking Gympass";
			const body =
				"Força a sincronização das aulas consfiguradas com a integração Booking Gympass.";
			const mensagemError = "Erro na sincronização";
			const sucesso = "Aulas sincronizadas com sucesso!";
			const styles = `modal-title-sm modal-body-sm`;
			const appModal = this.appModal.confirmModal(
				title,
				body,
				"Sincronizar",
				"lg",
				styles
			);
			appModal.result.then(() => {
				if (this.integradoZw !== false) {
					this.treinoConfigApi
						.sincronizarGympass(this.session.empresaId)
						.subscribe((response) => {
							if (response) {
								if (response.toString().includes("OK")) {
									this.notificationService.success(sucesso);
								} else {
									this.notificationService.error(mensagemError);
								}
							}
						});
				}
			});
		});
	}

	setModuloAula(payload: ConfiguracoesAplicativos) {
		if (payload.modulo_aulas === false) {
			payload.modulo_aulas_aba_saldo = false;
			payload.modulo_aulas_aba_turmas = false;
		}
	}

	setTipoRmlAvaliacao(payload: ConfiguracoesAvaliacao) {
		if (payload.cfg_tipo_rml === "cfg_rml_separado") {
			payload.cfg_rml = true;
			payload.cfg_rml_separado = true;
			payload.cfg_rml_opcoes = false;
		} else if (payload.cfg_tipo_rml === "cfg_rml_opcoes") {
			payload.cfg_rml = true;
			payload.cfg_rml_opcoes = true;
			payload.cfg_rml_separado = false;
		} else {
			payload.cfg_rml = false;
			payload.cfg_rml_opcoes = false;
			payload.cfg_rml_separado = false;
		}
		payload.cfg_tipo_rml = undefined;
		return payload;
	}
}

enum DobraCutanea {
	ABDOMINAL = "Abdominal",
	SUPRA_ILIACA = "Supra Iliaca",
	PEITORAL = "Peitoral",
	TRICEPS = "Triceps",
	COXA_MEDIAL = "Coxa Medial",
	BICEPS = "Biceps",
	SUBESCAPULAR = "Subescapular",
	AXILARMEDIA = "Axilar Media",
	SUPRA_ESPINHAL = "Supra Espinhal",
	PANTURRILHA = "Panturrilha",
}
