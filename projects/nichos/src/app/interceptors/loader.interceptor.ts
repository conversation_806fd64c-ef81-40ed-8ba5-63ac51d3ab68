import { Injectable } from "@angular/core";
import {
	<PERSON>ttpEvent,
	HttpHandler,
	HttpInterceptor,
	HttpRequest,
} from "@angular/common/http";

import { Observable } from "rxjs";
import { finalize } from "rxjs/operators";
import { LoaderService } from "ui-kit";

@Injectable()
export class LoaderInterceptor implements HttpInterceptor {
	constructor(private loaderService: LoaderService) {}

	intercept(
		request: HttpRequest<any>,
		next: <PERSON>ttp<PERSON>and<PERSON>
	): Observable<HttpEvent<any>> {
		this.loaderService.show();

		return next.handle(request).pipe(finalize(() => this.loaderService.hide()));
	}
}
