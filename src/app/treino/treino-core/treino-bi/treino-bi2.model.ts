import { UsuarioBase } from "treino-api";

export interface TreinoBiTreinamento {
	alunosAtivosComTreino: number;
	alunosAtivosProgramaEmDia: number;
	alunosProgramaRenovar: number;
	alunosProgramaVencidos: number;
	alunosAtivosSemTreino: number;
	porcentagemTreinosEmDia: number;
	tempoPermanenciaPrograma: {
		medio: number;
		minimo: {
			valor: number;
			aluno: UsuarioBase;
		};
		maximo: {
			valor: number;
			aluno: UsuarioBase;
		};
	};
	treinosRenovarEm30Dias: number;
	mediaExecucao: MediaExecucaoPorDia;
	acessoExecucoesZW: {
		[day: number]: {
			execucoesTreino: 0;
			alunosDoTreino: 0;
			smartphone: 0;
			acessos: 0;
		};
	};
}

export interface MediaExecucaoPorDia {
	segunda: MediaExecucaoDia;
	terca: MediaExecucaoDia;
	quarta: MediaExecucaoDia;
}

export interface MediaExecucaoDia {
	manha: number;
	tarde: number;
	noite: number;
	total?: number;
}

export interface TreinoBiCarteira {
	totalAlunos: number;
	ativos: number;
	inativos: number;
	totalAlunosSemAcompanhamento: number;
	totalAlunosEmAcompanhamento: number;
	visitantes: number;
	taxaRenovacaoZW: number;
	aVencerZW: number;
	totalRenovacoesCarteira: number;
	tempoPermanenciaCarteiraZW: {
		medio: number;
		minimo: {
			valor: number;
			aluno: UsuarioBase;
		};
		maximo: {
			valor: number;
			aluno: UsuarioBase;
		};
	};
	fluxoCarteiraZW: {
		novos: number;
		trocaram: number;
		sairam: number;
	};
	historicoCarteiraZW?: {
		[dia: number]: number;
	};
	avaliacaoAplicativo?: {
		numeroAvaliacoes: number;
		media: number;
	};
}

export interface TreinoBiAvaliacaoTreino {
	nrAvaliacoesTreino: number;
	nr1estrela: number;
	nr2estrelas: number;
	nr3estrelas: number;
	nr4estrelas: number;
	nr5estrelas: number;
}
