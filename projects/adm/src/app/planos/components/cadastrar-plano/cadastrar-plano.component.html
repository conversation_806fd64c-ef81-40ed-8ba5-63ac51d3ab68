<ng-container *ngIf="loading">
	<div class="loader">
		<img src="assets/images/loading.svg" />
		<span i18n="@@plano:loading-message">Carregando plano...</span>
	</div>
</ng-container>
<ng-container *ngIf="!loading">
	<pacto-cat-layout-v2 *ngIf="planoDTO.tipoPlano == tipoPlano.NENHUM">
		<!-- <div class="type-p-small cor-cinza05" i18n="@@adm:module-location">
			Você está em: Administrativo
		</div> -->

		<div class="content-wrapper">
			<div
				class="type-h4-bold icon-voltar"
				id="btn-come-back-plano"
				(click)="voltarListagemPlanos()">
				<i class="pct pct-arrow-left"></i>
			</div>
			<div class="content-title">
				<div class="type-h3-bold">
					<span i18n="@@plano:title">Planos</span>
				</div>
				<div style="display: flex">
					<div class="type-p-small cor-cinza05 texto-subtitulo">
						<span i18n="@@plano:subtitle">
							Configure Planos, incluindo: descontos, produtos, modalidades
							diferentes e convênios, tudo em uma mesma janela de fácil acesso e
							manuseio.
						</span>
					</div>
				</div>
			</div>
		</div>

		<div class="card-plano-row">
			<pacto-cat-card-plain
				class="card-plano margin-right-30"
				(click)="cadastrarPlanoNormal()">
				<div
					class="imagem-card"
					style="background-image: url('assets/plano/plano-normal.png')"></div>
				<div class="type-h6 cor-preto05 titulo-card">
					<span i18n="@@plano:title-normal">Plano Normal</span>
				</div>

				<div class="type-p-small cor-cinza05 descricao-card">
					<span i18n="@@plano:description-normal">
						Ideal para escolas de natação, dança, lutas, estúdios entre outros
						que trabalham com planos de várias durações e vezes na semana.
					</span>
				</div>

				<pacto-cat-button
					i18n-label="@@plano:register-btn"
					label="Cadastrar Plano"
					[id]="'plano-normal'"
					[full]="true"></pacto-cat-button>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain
				class="card-plano margin-right-30"
				[routerLink]="['recorrencia']">
				<div
					class="imagem-card"
					style="
						background-image: url('assets/plano/plano-recorrencia.png');
					"></div>
				<div class="type-h6 cor-preto05 titulo-card">
					<span i18n="@@plano:title-recorrencia">
						Plano Recorrência (Plano Autorrenovável)
					</span>
				</div>

				<div class="type-p-small cor-cinza05 descricao-card">
					<span i18n="@@plano:description-recorrencia">
						Recomendado para academias Low Cost, que trabalham apenas com
						musculação e aulas coletivas, e cujos planos têm um valor fixo
						mensal e renovam automaticamente.
					</span>
				</div>

				<pacto-cat-button
					i18n-label="@@plano:register-btn"
					label="Cadastrar Plano"
					[id]="'plano-recorrencia'"
					[full]="true"></pacto-cat-button>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain
				class="card-plano margin-right-30"
				routerLink="credito">
				<div
					class="imagem-card"
					style="background-image: url('assets/plano/plano-credito.png')"></div>
				<div class="type-h6 cor-preto05 titulo-card">
					<span i18n="@@plano:title-credito">Plano Crédito</span>
				</div>

				<div class="type-p-small cor-cinza05 descricao-card">
					<span i18n="@@plano:description-credito">
						Feito para academias em regiões turísticas ou faculdades, onde os
						alunos não irão adquirir um plano de longa duração, mas sim diárias.
					</span>
				</div>

				<pacto-cat-button
					i18n-label="@@plano:register-btn"
					label="Cadastrar Plano"
					[id]="'plano-credito'"
					[full]="true"></pacto-cat-button>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain
				class="card-plano margin-right-30"
				routerLink="personal">
				<div
					class="imagem-card"
					style="
						background-image: url('assets/plano/plano-personal.png');
					"></div>

				<div class="type-h6 cor-preto05 titulo-card">
					<span i18n="@@plano:title-personal">Plano Personal</span>
				</div>

				<div class="type-p-small cor-cinza05 descricao-card">
					<span i18n="@@plano:description-personal">
						Esse tipo de plano é recomendado para academias com muitos personais
						e se deseja cobrar dos mesmos de forma recorrente.
					</span>
				</div>

				<pacto-cat-button
					i18n-label="@@plano:register-btn"
					label="Cadastrar Plano"
					[id]="'plano-personal'"
					[full]="true"></pacto-cat-button>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain
				class="card-plano margin-right-30"
				(click)="cadastrarPlanoAvancado()">
				<div
					class="imagem-card backgorund-plano-personalizado"
					style="
						background-image: url('assets/plano/plano-normal-avancado.jpeg');
					"></div>
				<div class="type-h6 cor-preto05 titulo-card">
					<span i18n="@@plano:title-normal">Plano Normal (Avançado)</span>
				</div>

				<div class="type-p-small cor-cinza05 descricao-card">
					<span i18n="@@plano:description-normal">
						Ideal para escolas de natação, dança, lutas, estúdios entre outros
						que trabalham com planos de várias durações e vezes na semana.
					</span>
				</div>

				<pacto-cat-button
					i18n-label="@@plano:register-btn"
					label="Cadastrar Plano"
					[id]="'plano-avancado'"
					[full]="true"></pacto-cat-button>
			</pacto-cat-card-plain>
		</div>
	</pacto-cat-layout-v2>

	<ng-container *ngIf="planoDTO.tipoPlano !== tipoPlano.NENHUM">
		<pacto-cat-layout-v2>
			<ng-container *ngIf="planoDTO.tipoPlano !== tipoPlano.PLANO_CREDITO">
				<pacto-cat-stepper-simple (goBack)="voltarListagemPlanos()">
					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-basico"
						stepLabel="Dados Básicos"
						i18n-stepDescription="@@plano:stepDescription-basico"
						stepDescription="Informe os dados necessários para criar um novo plano">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimpleNext
								class="margin-left-12"
								[disabled]="!dadosBasicosComponent.formGroup?.valid"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								id="btn-plano-avancar-dados-basicos"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<adm-plano-dados-basicos
							#dadosBasicosComponent
							[planoDTO]="planoDTO"
							(nome)="planoNome = $event"></adm-plano-dados-basicos>
					</pacto-cat-step-simple>

					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-pacotes"
						stepLabel="{{ planoNome | captalize }} / Pacotes"
						i18n-stepDescription="@@plano:stepdescription-pacotes"
						stepDescription="Informe os dados necessários para criar um novo plano"
						*ngIf="exibirTabPacotes()">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-table-pactores"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-table-pacotes"
								[disabled]="disableNextProduto"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<div class="table-wrapper pacto-shadow">
								<adm-plano-pacote
									#pacoteComponent
									[planoDTO]="planoDTO"
									[codigo]="state.plano?.codigo"></adm-plano-pacote>
							</div>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>

					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-modalidade"
						stepLabel="{{ planoNome | captalize }} / Modalidade"
						i18n-stepDescription="@@plano:stepdescription-modalidade"
						stepDescription="Informe os dados necessários para criar um novo plano"
						*ngIf="exibirTabModalidade()">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-table-modalidade"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-table-modalidade"
								[disabled]="disableNextProduto"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<div class="table-wrapper pacto-shadow">
								<adm-plano-modalidade
									#modalidadeComponent
									[codigo]="state.plano?.codigo"
									[planoDTO]="planoDTO"></adm-plano-modalidade>
							</div>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>

					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-categoria"
						stepLabel="{{ planoNome | captalize }} / Categoria"
						i18n-stepDescription="@@plano:stepdescription-categoria"
						stepDescription="Informe os dados necessários para criar um novo plano"
						*ngIf="exibirTabCategoria()">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-table-categoria"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-table-categoria"
								[disabled]="disableNextProduto"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<div class="table-wrapper pacto-shadow">
								<adm-categoria-plano
									[planoDTO]="planoDTO"></adm-categoria-plano>
							</div>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>
					<pacto-cat-step-simple
						[stepLabel]="getStepDuracaoTitulo()"
						[stepDescription]="getStepDuracaoDescricao()"
						*ngIf="
							state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO &&
							state.plano.tipoPlano !== tipoPlano.PLANO_PERSONAL
						">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-table-duracao-valor"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-table-duracao-valor"
								[disabled]="!duracoes || duracoes?.length === 0"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<adm-plano-duracao
								*ngIf="state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO"
								[planoDTO]="planoDTO"
								[formDadosBasicos]="dadosBasicosComponent.formGroup"
								(isEditinOrAdding)="disableNextDuracaoValor = $event"
								(afterAdd)="
									updateCondicaoPagamento($event)
								"></adm-plano-duracao>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>

					<pacto-cat-step-simple
						stepLabel="
						{{ planoNome | captalize }} / {{
							state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO
								? 'Exceções'
								: 'Duração e Valores'
						}}
						"
						i18n-stepDescription="@@plano:stepdescription-durval"
						stepDescription="Informe as variações de tempo e valor do plano."
						*ngIf="state.plano.tipoPlano !== tipoPlano.PLANO_PERSONAL">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-table-duracao-valor"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-table-duracao-valor"
								[disabled]="!duracoes || duracoes?.length === 0"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<div class="table-wrapper pacto-shadow">
								<adm-table-duracao-valor
									[planoDTO]="planoDTO"
									[formDadosBasicos]="dadosBasicosComponent.formGroup"
									(afterAdd)="updateCondicaoPagamento($event)"
									(isEditinOrAdding)="
										disableNextDuracaoValor = $event
									"></adm-table-duracao-valor>
							</div>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>
					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-prodserv"
						stepLabel="{{ planoNome | captalize }} / Horários"
						i18n-stepDescription="@@plano:stepdescription-prod-serv"
						stepDescription="Informe os dados de horarios para o plano"
						*ngIf="state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-horarios"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-horarios"
								[disabled]="disableNextProduto"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<div class="table-wrapper pacto-shadow">
								<adm-plano-horario
									[planoDTO]="planoDTO"
									(isEditinOrAdding)="
										disableNextProduto = $event
									"></adm-plano-horario>
							</div>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>

					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-prodserv"
						stepLabel="{{ planoNome | captalize }} / Produtos e Serviços"
						i18n-stepDescription="@@plano:stepdescription-prod-serv"
						stepDescription="Informe os dados necessários para criar um novo plano"
						*ngIf="
							state.plano.tipoPlano === tipoPlano.PLANO_CREDITO ||
							state.plano.tipoPlano === tipoPlano.PLANO_NORMAL ||
							state.plano.tipoPlano === tipoPlano.PLANO_RECORRENCIA ||
							state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO
						">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-table-produtos"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-table-produtos"
								[disabled]="disableNextProduto"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"></pacto-cat-button>
						</ng-container>
						<pacto-cat-card-plain>
							<div class="table-wrapper pacto-shadow">
								<adm-table-produtos
									[planoDTO]="planoDTO"
									(isEditinOrAdding)="
										disableNextProduto = $event
									"></adm-table-produtos>
							</div>
						</pacto-cat-card-plain>
					</pacto-cat-step-simple>
					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-dadoscontratuais"
						stepLabel="{{ planoNome | captalize }} / Dados Contratuais"
						i18n-stepDescription="@@plano:stepdescription-dadoscontratuais"
						stepDescription="Informe os dados necessários para criar um novo plano"
						*ngIf="
							state.plano.tipoPlano === tipoPlano.PLANO_CREDITO ||
							state.plano.tipoPlano === tipoPlano.PLANO_NORMAL ||
							state.plano.tipoPlano === tipoPlano.PLANO_PERSONAL ||
							state.plano.tipoPlano === tipoPlano.PLANO_RECORRENCIA ||
							state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO
						">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-dados-contraturais"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								pactoCatStepSimpleNext
								id="btn-plano-avancar-dados-contratuais"
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-avancar"
								label="Avançar"
								[disabled]="
									((planoDTO.tipoPlano === 'PLANO_NORMAL' ||
										planoDTO.tipoPlano === 'PLANO_AVANCADO') &&
										!dadosContratuaisComponent?.formGroup.valid) ||
									(planoDTO.tipoPlano === 'PLANO_RECORRENCIA' &&
										!recorrenciaDadosContratuaisComponent?.formGroup.valid) ||
									(planoDTO.tipoPlano === 'PLANO_PERSONAL' &&
										!personalDadosContratuaisComponent?.formGroup.valid)
								"
								*ngIf="
									state.plano.tipoPlano === tipoPlano.PLANO_NORMAL ||
									state.plano.tipoPlano === tipoPlano.PLANO_CREDITO ||
									state.plano.tipoPlano === tipoPlano.PLANO_AVANCADO
								"></pacto-cat-button>
							<pacto-cat-button
								class="margin-left-12"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								id="btn-plano-concluir-dados-contratuais"
								i18n-label="@@adm:btn-concluir"
								label="Concluir"
								(click)="save()"
								[disabled]="
									isSaving ||
									((planoDTO.tipoPlano === 'PLANO_NORMAL' ||
										planoDTO.tipoPlano === 'PLANO_AVANCADO') &&
										!dadosContratuaisComponent?.formGroup.valid) ||
									(planoDTO.tipoPlano === 'PLANO_RECORRENCIA' &&
										!recorrenciaDadosContratuaisComponent?.formGroup.valid) ||
									(planoDTO.tipoPlano === 'PLANO_PERSONAL' &&
										!personalDadosContratuaisComponent?.formGroup.valid)
								"
								*ngIf="
									state.plano.tipoPlano === tipoPlano.PLANO_RECORRENCIA ||
									state.plano.tipoPlano === tipoPlano.PLANO_PERSONAL
								"></pacto-cat-button>
						</ng-container>

						<adm-plano-dados-contratuais
							#dadosContratuaisComponent
							[plano]="planoDTO"
							*ngIf="
								planoDTO.tipoPlano === 'PLANO_NORMAL' ||
								planoDTO.tipoPlano === 'PLANO_AVANCADO'
							"></adm-plano-dados-contratuais>
						<adm-plano-recorrencia-dados-contratuais
							#recorrenciaDadosContratuaisComponent
							[plano]="planoDTO"
							*ngIf="
								planoDTO.tipoPlano === 'PLANO_RECORRENCIA'
							"></adm-plano-recorrencia-dados-contratuais>
						<adm-plano-personal-dados-contratuais
							#personalDadosContratuaisComponent
							[plano]="planoDTO"
							*ngIf="
								planoDTO.tipoPlano === 'PLANO_PERSONAL'
							"></adm-plano-personal-dados-contratuais>
					</pacto-cat-step-simple>
					<pacto-cat-step-simple
						i18n-stepLabel="@@plano:steplabel-condpag"
						stepLabel="{{ planoNome | captalize }} / Condição de Pagamento"
						i18n-stepDescription="@@plano:stepdescription-condpag"
						stepDescription="Informe as negociações possiveis na contratação do plano, determinando como o pagamento será realizado.">
						<ng-container *pactoButtonContainer>
							<pacto-cat-button
								pactoCatStepSimplePrevious
								id="btn-plano-voltar-condicao-pagamento"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								i18n-label="@@adm:btn-voltar"
								label="Voltar"></pacto-cat-button>
							<pacto-cat-button
								class="margin-left-12"
								id="btn-plano-concluir-condicao-pagamento"
								[size]="'LARGE'"
								[type]="'PRIMARY'"
								i18n-label="@@adm:btn-concluir"
								label="Concluir"
								(click)="save()"
								[disabled]="
									isSaving || disableNextCondicaoPagamento
								"></pacto-cat-button>
						</ng-container>
						<ng-container *ngIf="duracoes && duracoes.length > 0">
							<adm-plano-condicao-pagamento
								*ngFor="let duracao of duracoes"
								[duracao]="duracao"
								[planoDTO]="planoDTO"
								(isEditinOrAdding)="
									disableNextCondicaoPagamento = $event
								"></adm-plano-condicao-pagamento>
						</ng-container>
						<ng-container *ngIf="!duracoes || duracoes?.length === 0">
							<pacto-cat-card-plain>
								<div class="table-wrapper pacto-shadow">
									<span i18n="@@plano:condpag-hint-durval">
										Para criar uma condição de pagamento é necessário acessar o
										passo "Duração e Valores" e adicionar ao menos uma duração.
									</span>
								</div>
							</pacto-cat-card-plain>
						</ng-container>
					</pacto-cat-step-simple>
				</pacto-cat-stepper-simple>
			</ng-container>
			<ng-container *ngIf="planoDTO.tipoPlano === tipoPlano.PLANO_CREDITO">
				<adm-cadastrar-plano-credito></adm-cadastrar-plano-credito>
			</ng-container>
		</pacto-cat-layout-v2>
	</ng-container>
</ng-container>

<pacto-traducoes-xingling #traducoes>
	<span xingling="steplabel-modalidade" i18n="@@plano:steplabel-modalidade">
		Modalidades
	</span>
	<span xingling="steplabel-durval" i18n="@@plano:steplabel-durval">
		Duração e Valores
	</span>
	<span xingling="steplabel-duracao" i18n="@@plano:steplabel-duracao">
		Duração
	</span>
	<span
		xingling="stepdescription-modalidade"
		i18n="@@plano:stepdescription-modalidade">
		Defina as modalidades que farão parte do plano.
	</span>
	<span xingling="stepdescription-durval" i18n="@@plano:stepdescription-durval">
		Informe as variações de tempo e valor do plano.
	</span>

	<span xingling="descricao-length-error" i18n="@@plano:descricao-length-error">
		O nome do plano deve conter menos que 200 caracteres!
	</span>
	<span xingling="modalidade-zerada-hint" i18n="@@plano:modalidade-zerada-hint">
		Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais
		uma modalidade junto que tenha valor, ou então coloque valor no cadastro
		dela.
	</span>
	<span
		xingling="modalidade-zerada-error"
		i18n="@@plano:modalidade-zerada-error">
		Você adicionou modalidade com valor zerado na aba "modalidades". Como este
		plano não foi marcado como "PLANO BOLSA", esta operação não é permitida.
	</span>
	<span xingling="saved-success" i18n="@@plano:saved-success">
		Plano salvo com sucesso.
	</span>
</pacto-traducoes-xingling>
