import { PerfilRecursoPermissoTipo } from "./perfil-acesso-recurso.model";

export interface PerfilAcessoUnificado {
	codigo: number;
	tipo: string;
	nome: string;
	unificado: boolean;
}

export interface Perfil {
	id: string;
	nome: string;
}

export class PerfilAcessoDetalheDTO {
	id: number;
	nome: string;
	recursos: {
		recurso: string;
		tipoPermissoes: string[];
	}[];
	funcionalidades: {
		nome: string;
		possuiFuncionalidade: boolean;
	}[];
}

/**
 * Representa um perfil de acesso no sistema treino. Um perfil
 * possui algumas funcionalidades habilitadas e outra não. Além disso,
 * para cada recurso um perfil pode ter diferentes tipos de permissão:
 *
 *   CONSULTAR = 'CONSULTAR', INCLUIR = 'INCLUIR',
 *   EDITAR = 'EDITAR', EXCLUIR = 'EXCLUIR',
 *   TOTAL_EXCETO_EXCLUIR = 'TOTAL_EXCETO_EXCLUIR',
 *   TOTAL = 'TOTAL'
 *
 */
export class PerfilAcessoDetalheSdk {
	id: number;
	nome: string;
	tipo: string;
	recursos: Map<string, PerfilAcessoRecurso>;
	funcionalidades: Map<string, boolean>;

	constructor(dto: PerfilAcessoDetalheDTO) {
		this.nome = dto.nome;
		this.recursos = new Map();
		this.funcionalidades = new Map();

		dto.recursos.forEach((recursoItem) => {
			this.recursos.set(
				recursoItem.recurso as string,
				new PerfilAcessoRecurso(
					recursoItem.recurso as string,
					recursoItem.tipoPermissoes as PerfilRecursoPermissoTipo[]
				)
			);
		});

		for (const recurso in dto.recursos) {
			if (dto.recursos.hasOwnProperty(recurso)) {
			}
		}

		dto.funcionalidades.forEach((funcionalidade, index) => {
			if (dto.funcionalidades.hasOwnProperty(index)) {
				this.funcionalidades.set(
					funcionalidade.nome as string,
					funcionalidade.possuiFuncionalidade
				);
			}
		});
	}

	possuiRecurso(recursoNecessario: PerfilAcessoRecurso) {
		let allowed = false;
		if (!recursoNecessario) {
			return true;
		} else if (recursoNecessario.permissao.length === 0) {
			return true;
		} else if (recursoNecessario) {
			const usuario = this.recursos.get(recursoNecessario.recurso);
			recursoNecessario.permissao.forEach((tipo) => {
				if (usuario) {
					allowed = usuario.permissao.includes(tipo) || allowed;
				}
			});
		}
		return allowed;
	}

	possuiFuncionalidade(funcionalidadeNecessario: PerfilAcessoFuncionalidade) {
		let allowed = false;
		if (!funcionalidadeNecessario) {
			return true;
		} else if (funcionalidadeNecessario) {
			const usuarioFuncionalidade = this.funcionalidades.get(
				funcionalidadeNecessario.funcionalidade
			);
			allowed = funcionalidadeNecessario.permissao === usuarioFuncionalidade;
		}
		return allowed;
	}
}

export class PerfilAcessoRecurso {
	recurso: string;
	permissao: PerfilRecursoPermissoTipo[];

	get consultar(): boolean {
		const consultar = this.permissao.includes(
			PerfilRecursoPermissoTipo.CONSULTAR
		);
		const todosExcetoExc = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		return consultar || todosExcetoExc || todos;
	}

	get incluir() {
		const incluir = this.permissao.includes(PerfilRecursoPermissoTipo.INCLUIR);
		const todosExcetoExc = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		return incluir || todosExcetoExc || todos;
	}

	get editar() {
		const editar = this.permissao.includes(PerfilRecursoPermissoTipo.EDITAR);
		const todosExcetoExc = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		return editar || todosExcetoExc || todos;
	}

	get excluir() {
		const totalExcExcluir = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const excluir = this.permissao.includes(PerfilRecursoPermissoTipo.EXCLUIR);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		if (totalExcExcluir) {
			return false;
		} else {
			return excluir || todos;
		}
	}

	constructor(recurso: string, permissao: PerfilRecursoPermissoTipo[]) {
		this.recurso = recurso;
		this.permissao = permissao;
	}
}

export class PerfilAcessoFuncionalidade {
	funcionalidade: string;
	permissao: boolean;

	get permitir(): boolean {
		return this.permissao;
	}

	constructor(funcionalidade: string, permissao: boolean) {
		this.funcionalidade = funcionalidade;
		this.permissao = permissao;
	}
}
