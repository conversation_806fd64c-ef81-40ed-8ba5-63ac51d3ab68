import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Fatura, Boleto, Nota } from "./fatura.model";

import { Observable, of } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { CclRestService } from "canal-cliente/app/ccl-rest.service";

@Injectable({
	providedIn: "root",
})
export class FaturasService {
	constructor(private http: HttpClient, public restService: CclRestService) {}

	private transformFaturas(faturas: Array<Fatura>): Array<Fatura> {
		if (faturas.length > 0) {
			return faturas.map((fatura) => {
				return this.transformFatura(fatura);
			});
		} else {
			return [];
		}
	}

	private transformFatura(fatura: Fatura): Fatura {
		const partesData = fatura.vencimento.split("/");
		fatura.vencimentoData = new Date(
			partesData[2] + "-" + partesData[1] + "-" + partesData[0]
		);
		return fatura;
	}

	consultarUltimasFaturas(limite: number): Observable<Fatura[]> {
		const url = this.restService.buildFullUrlZw(
			"canalCliente/consultar-parcelas?chave={chave}&empresa={empresa}&qtde=" +
				limite
		);
		return this.http.get(url).pipe(
			map((faturas: Array<Fatura>) => {
				return this.transformFaturas(faturas);
			}),
			catchError((err) => {
				console.log("Esta falhando a conexão com FINANCEIRO");
				return of(null);
			})
		);
	}

	conultarNotas(faturas: Fatura[]): Observable<Nota[]> {
		let parcelasParam = "";
		faturas.forEach((fatura) => {
			parcelasParam += fatura.codigo + ",";
		});
		parcelasParam = parcelasParam.substring(0, parcelasParam.length - 1);
		const url = this.restService.buildFullUrlZw(
			"canalCliente/consultar-notas?chave={chave}&parcelas=" + parcelasParam
		);

		return this.http.get(url).pipe(
			map((notas: Nota[]) => {
				return notas;
			})
		);
	}

	emitirBoleto(fatura: Fatura, calcularCobrancasExtras: boolean = true) {
		const url = this.restService.buildFullUrlZw(
			"canalCliente/emitir-boleto?chave={chave}&parcela=" + fatura.codigo
		);
		return this.http.get(url).pipe(map((boleto: Boleto) => boleto.url));
	}
}
