import { Injectable } from "@angular/core";
import { PlanoApiBaseService } from "../base/plano-api-base.service";
import { PlanoApiModule } from "../plano-api.module";
import { Observable } from "rxjs";
import { ApiResponseList } from "../base/base.model";
import { ConvenioDescontoConfiguracao } from "./convenio-desconto.model";

@Injectable({
	providedIn: PlanoApiModule,
})
export class PlanoApiConvenioDescontoConfiguracaoService {
	constructor(private planoApiBaseService: PlanoApiBaseService) {}

	public getConfiguracaoByConvenioDesconto(
		id: number | string
	): Observable<ApiResponseList<ConvenioDescontoConfiguracao>> {
		return this.planoApiBaseService.get<
			ApiResponseList<ConvenioDescontoConfiguracao>
		>(`convenio-desconto-configuracao/config/${id}`);
	}
}
