<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row">
			<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
				<pacto-cat-form-input-number
					[formControl]="codigoControl"
					i18n-label="@@cad-aux:cidade:codigoLabel"
					label="Código"
					placeholder="000"
					readonly="true"></pacto-cat-form-input-number>
			</div>
			<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
				<pacto-cat-form-input
					[control]="form.get('nome')"
					errorMsg="Informe o nome do país"
					i18n-errorMsg="@@cad-aux:cidade:paisNomeErrorMsg"
					i18n-label="@@cad-aux:cidade:paisNomeLabel"
					i18n-placeholder="@@cad-aux:cidade:paisNomePlaceholder"
					label="Nome"
					maxlength="40"
					placeholder="Brasil"></pacto-cat-form-input>
			</div>
			<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
				<pacto-cat-form-input
					[control]="form.get('nacionalidade')"
					i18n-label="@@cad-aux:cidade:nacionalLabel"
					i18n-placeholder="nacionalPlaceholder"
					label="Nacionalidade"
					maxlength="255"
					placeholder="Brasileira(o)"></pacto-cat-form-input>
			</div>
		</div>
		<div class="row">
			<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
				<pacto-cat-form-input
					[control]="formEstado.get('sigla')"
					i18n-label="@@cad-aux:cidade:estadoSiglaLabel"
					label="Sigla"
					maxlength="2"
					placeholder="GO"></pacto-cat-form-input>
			</div>
			<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
				<pacto-cat-form-input
					[control]="formEstado.get('descricao')"
					i18n-label="@@cad-aux:cidade:estadoLabel"
					label="Descrição"
					maxlength="50"
					placeholder="Goiás"></pacto-cat-form-input>
			</div>
			<div
				class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 d-flex align-items-center">
				<div (click)="addEstado()" class="add-estado cor-azulim-pri">
					<i class="pct pct-plus-circle"></i>
					<span i18n="@@cad-aux:cidade:addEstadoBttn" style="margin-left: 4px">
						Adicionar
					</span>
				</div>
			</div>
		</div>
		<div class="table-wrapper">
			<pacto-relatorio
				#tableEstadosComponent
				(iconClick)="deleteEstado($event)"
				(pageChangeEvent)="changePageEstados($event)"
				(pageSizeChange)="changePageSizeEstados($event)"
				[showShare]="false"
				[table]="tableEstados"></pacto-relatorio>
		</div>
	</div>

	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="cancel()"
			i18n-label="@@adm:cad-aux:cancelBttn"
			label="Cancelar"
			style="margin-right: 10px"
			type="OUTLINE_DARK"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvarPais()"
			i18n-label="@@adm:cad-aux:saveBttn"
			label="Salvar"
			style="margin-right: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<ng-template #columnSigla>
	<span i18n="@@cad-aux:cidade:tableColumnSigla">Sigla</span>
</ng-template>

<ng-template #columnDescricao>
	<span i18n="@@cad-aux:cidade:tableColumnDescricao">Descrição</span>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@cad-aux:addPais:delete" xingling="ACTION_DELETE">Excluir</span>
	<span i18n="@@cad-aux:addPais:delete" xingling="BTTN_DELETE">
		Excluir um estado
	</span>
	<span i18n="@@cad-aux:addPais:success" xingling="PAIS_SUCCESS">
		País cadastrado com sucesso
	</span>
	<span i18n="@@cad-aux:addPais:novoPais" xingling="NOVO_PAIS">Novo País</span>
</pacto-traducoes-xingling>
