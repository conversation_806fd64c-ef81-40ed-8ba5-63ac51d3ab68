import {
	Component,
	OnInit,
	forwardRef,
	ChangeDetectorRef,
	Input,
	SimpleChanges,
	OnChanges,
	OnDestroy,
	ChangeDetectionStrategy,
} from "@angular/core";
import { NG_VALUE_ACCESSOR, FormArray, FormControl } from "@angular/forms";
import { BaseControlValueAccessor } from "@base-shared/BaseControlValueAccessor";
import { AvaliacaoObjetivo } from "treino-api";
import { Subscription } from "rxjs";

export type AvaliacaoObjetivosDto = string[];

@Component({
	selector: "pacto-cat-avaliacao-objetivos-input",
	templateUrl: "./cat-avaliacao-objetivos-input.component.html",
	styleUrls: ["./cat-avaliacao-objetivos-input.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => CatAvaliacaoObjetivosInputComponent),
			multi: true,
		},
	],
})
export class CatAvaliacaoObjetivosInputComponent
	extends BaseControlValueAccessor<AvaliacaoObjetivosDto>
	implements OnInit, OnChanges, OnDestroy
{
	@Input() objetivos: AvaliacaoObjetivo[];

	controls: FormArray = new FormArray([]);
	private changeSubscription: Subscription;
	private _value: AvaliacaoObjetivosDto;

	constructor(private cd: ChangeDetectorRef) {
		super();
	}

	get value() {
		return this._value;
	}

	set value(value: AvaliacaoObjetivosDto) {
		this._value = value;
		this.updateCheckboxes();
		this.onChange(this._value);
	}

	ngOnInit() {}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.objetivos) {
			this.createFormControls(changes.objetivos.currentValue);
		}
	}

	ngOnDestroy() {
		if (this.changeSubscription) {
			this.changeSubscription.unsubscribe();
		}
	}

	get valueSet() {
		return this._value && this._value.length;
	}

	private updateCheckboxes() {
		this.objetivos.forEach((objetivo, index) => {
			const tratamentoCaractereEspecial = objetivo.nome.replace(
				/[.*+?^${}()|[\]\\]/g,
				"\\$&"
			);
			const regex = new RegExp(tratamentoCaractereEspecial, "i");
			const isChecked = this._value
				? this._value.find((item) => regex.test(item)) !== undefined
				: false;
			this.controls.at(index).setValue(isChecked, { emitEvent: false });
		});
	}

	private createFormControls(objetivos: AvaliacaoObjetivo[]) {
		this.controls = new FormArray([]);
		objetivos.forEach((objetivo) => {
			const selected = this.valueSet
				? this._value.includes(objetivo.nome)
				: false;
			this.controls.push(new FormControl(selected));
		});
		this.changeSubscription = this.controls.valueChanges.subscribe(() => {
			this.updateInternalValue();
		});
	}

	private updateInternalValue() {
		const result = [];
		if (this.objetivos) {
			this.objetivos.forEach((objetivo, index) => {
				if (this.controls.at(index).value) {
					result.push(objetivo.nome);
				}
			});
		}
		this._value = result;
		this.onChange(result);
	}
}
