import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-line",
	templateUrl: "./ds3-chart-line.component.html",
	styleUrls: ["./ds3-chart-line.component.scss"],
})
export class Ds3ChartLineComponent implements OnInit, OnChanges, OnDestroy {
	public chartOptions!: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() labels: string[];
	@Input() series: ApexAxisChartSeries;
	@Input() height: number = 350;
	@Input() disableAnimations: boolean = false;
	@Input() tooltip: ApexTooltip = {
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			yaxis: this.yaxis,
			labels: this.labels,
			tooltip: this.tooltip,
			chart: {
				height: this.height,
				type: "line",
				animations: {
					enabled: !this.disableAnimations,
				},
				toolbar: {
					show: false,
				},
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
			theme: {
				mode: "light",
			},
			dataLabels: {
				enabled: false,
			},
			stroke: {
				curve: "straight",
				width: 3,
			},
			grid: {
				row: {
					colors: ["transparent"],
				},
			},
			markers: {
				strokeWidth: 0,
				size: 4,
			},
		};
		return options;
	}
}
