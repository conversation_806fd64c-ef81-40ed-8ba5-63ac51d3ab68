<!DOCTYPE html>
<html lang="pt-br">
	<head>
		<meta charset="utf-8" />
		<meta content="IE=edge" http-equiv="X-UA-Compatible" />
		<meta content="width=device-width, initial-scale=1" name="viewport" />
		<meta content="" name="description" />
		<meta content="" name="author" />
		<meta content="pt-br" http-equiv="Content-Language" />
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
		<meta
			content="MGWSf7lIeTNACusZiZCvnrjIEbGep2YNHE-g9OlGQpA"
			name="google-site-verification" />
		<title>Sistema Pacto</title>
		<link href="https://fonts.googleapis.com" rel="preconnect" />
		<link crossorigin href="https://fonts.gstatic.com" rel="preconnect" />
		<link
			href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap"
			rel="stylesheet" />
		<link
			href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
			rel="stylesheet" />
		<link
			href="assets/images/logos/pct-icone-fundo-pacto-branco.svg"
			id="favicon"
			rel="icon"
			sizes="32x32"
			type="image/png" />

		<script src="https://global.localizecdn.com/localize.js"></script>
		<script>
			!(function (a) {
				if (!a.Localize) {
					a.Localize = {};
					for (
						var e = [
								"translate",
								"untranslate",
								"phrase",
								"initialize",
								"translatePage",
								"setLanguage",
								"getLanguage",
								"getSourceLanguage",
								"detectLanguage",
								"getAvailableLanguages",
								"untranslatePage",
								"bootstrap",
								"prefetch",
								"on",
								"off",
								"hideWidget",
								"showWidget",
							],
							t = 0;
						t < e.length;
						t++
					)
						a.Localize[e[t]] = function () {};
				}
			})(window);
		</script>

		<script>
			if (
				document.querySelector("base") &&
				document.querySelector("base").getAttribute("href").includes("/en")
			) {
				Localize.initialize({
					key: "ACMPYVTMijnjb",
					rememberLanguage: true,
				});
				Localize.setLanguage("en-EU");
			}
		</script>
	</head>

	<body class="card-no-border">
		<pacto-root></pacto-root>

		<!-- Google Recaptcha v3 -->
		<script src="https://www.google.com/recaptcha/api.js?render=6LfZOsEUAAAAAAi2N7v0EvX8PS3HDJ9m-Qqg79iP"></script>

		<script src="https://www.amcharts.com/lib/3/amcharts.js"></script>
		<script src="https://www.amcharts.com/lib/3/serial.js"></script>
		<script src="https://www.amcharts.com/lib/3/pie.js"></script>
		<script src="https://www.amcharts.com/lib/3/themes/light.js"></script>
		<!-- Global site tag (gtag.js) - Google Analytics -->
		<script
			async
			src="https://www.googletagmanager.com/gtag/js?id=UA-153078031-1"></script>

		<!-- Hotjar Tracking Code for Novo Treino | ZW101 -->
		<script>
			(function (h, o, t, j, a, r) {
				h.hj =
					h.hj ||
					function () {
						(h.hj.q = h.hj.q || []).push(arguments);
					};
				h._hjSettings = { hjid: 1582381, hjsv: 6 };
				a = o.getElementsByTagName("head")[0];
				r = o.createElement("script");
				r.async = 1;
				r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
				a.appendChild(r);
			})(window, document, "https://static.hotjar.com/c/hotjar-", ".js?sv=");
		</script>

		<script>
			window.dataLayer = window.dataLayer || [];

			function gtag() {
				dataLayer.push(arguments);
			}

			gtag("js", new Date());

			gtag("create", "UA-113292205-2", "auto");
		</script>
	</body>
</html>
