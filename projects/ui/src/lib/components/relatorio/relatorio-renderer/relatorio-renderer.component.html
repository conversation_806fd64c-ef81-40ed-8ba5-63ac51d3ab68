<table
	[id]="idSuffix"
	[ngClass]="{
		'row-colored-first': alternatingColors === 'first',
		'row-colored-second': alternatingColors === 'second',
		'ds3-table': enableDs3
	}"
	class="table">
	<thead *ngIf="!(dados.length === 0 && !loading && customEmptyContent)">
		<tr *ngIf="!customRows" class="first-line">
			<th *ngIf="dataGridConfig.valueRowCheck"></th>
			<th
				*ngFor="let column of visibleColumns"
				[ngClass]="{
					sortable: isSortable(
						column.orderColumn ? column.orderColumn : column.nome
					)
				}"
				class="{{ column.styleClass }}">
				<span
					(click)="sortClick(column)"
					*ngIf="column.mostrarTitulo"
					[id]="'column-title-sort-' + column.nome">
					<ng-container *ngIf="isTemplate(column.titulo)">
						<ng-container *ngTemplateOutlet="column.titulo"></ng-container>
					</ng-container>
					<ng-container *ngIf="!isTemplate(column.titulo)">
						{{ column.titulo }}
					</ng-container>

					<i
						class="{{
							getSortIconClass(
								column.orderColumn ? column.orderColumn : column.nome
							)
						}}"
						*ngIf="
							isSortable(
								column.orderColumn ? column.orderColumn : column.nome
							) && !enableDs3
						"></i>

					<button
						ds3-icon-button
						size="sm"
						*ngIf="
							isSortable(
								column.orderColumn ? column.orderColumn : column.nome
							) && enableDs3
						">
						<i
							class="{{
								getSortIconClass(
									column.orderColumn ? column.orderColumn : column.nome
								)
							}}"></i>
					</button>
				</span>
			</th>
			<th *ngIf="dataGridConfig.actions.length" class="action-column">
				{{
					actionTitulo ? actionTitulo : ("relatorio.acoesColumn" | translate)
				}}
			</th>
			<th *ngIf="dataGridConfig.dropDownActions.length" class="action-column">
				{{
					dropDownActionTitulo
						? dropDownActionTitulo
						: ("relatorio.acoesColumn" | translate)
				}}
			</th>
			<th *ngIf="dataGridConfig?.ordenable" class="action-column">Ordenação</th>
		</tr>
	</thead>
	<tbody
		(cdkDropListDropped)="onDrop($event)"
		[ngClass]="{ 'pct-table-zebra': enableDs3 || enableZebraStyle }"
		cdkDropList>
		<ng-container *ngIf="customContent">
			<ng-container
				*ngTemplateOutlet="
					customContent;
					context: { dados: dados, colunas: visibleColumns }
				"></ng-container>
		</ng-container>

		<ng-container *ngIf="!customContent">
			<ng-container *ngIf="!customRows">
				<tr
					*ngFor="let row of dados; let lastRow = last; let rowIndex = index"
					[@fadeIn]="dados.length"
					[cdkDragDisabled]="!dataGridConfig?.ordenable"
					[id]="
						idSuffix
							? 'element-' + rowIndex + '-' + idSuffix
							: 'element-' + rowIndex
					"
					[ngClass]="{
						'total-row': dataGridConfig.totalRow && lastRow,
						'row-clickable': dataGridConfig.rowClick,
						ghost: dataGridConfig.ghostLoad && loading
					}"
					cdkDrag>
					<td *ngIf="dataGridConfig.valueRowCheck" class="selectable">
						<input
							(click)="checkRow(row)"
							[checked]="rowChecked(row)"
							class="form-check-input"
							type="checkbox" />
					</td>
					<!-- Data columns -->
					<td
						(click)="rowClickHandler(row, $event, rowIndex)"
						*ngFor="let column of visibleColumns; let firstColumn = first"
						[ngClass]="column.styleClass"
						[style.max-width]="column.width"
						[style.width]="column.width">
						<ng-container
							[ngSwitch]="dataGridConfig.totalRow && firstColumn && lastRow">
							<!-- TOTAL COLUMN -->
							<ng-container *ngSwitchCase="true">
								<span>Total</span>
							</ng-container>

							<!-- DATA COLUMNS -->
							<ng-container *ngSwitchCase="false">
								<span
									(click)="columnCellClickHandler(row, column)"
									[ngClass]="{ 'hover-cell': column.cellPointerCursor }"
									class="column-cell">
									<ng-container *ngIf="!column.celula && column.campo && row">
										{{
											column.valueTransform
												? column.valueTransform(row[column.campo], row)
												: row[column.campo]
										}}
									</ng-container>
									<ng-container *ngIf="column.celula && row">
										<ng-container
											*ngTemplateOutlet="
												column.celula;
												context: { item: row, index: rowIndex }
											"></ng-container>
									</ng-container>
								</span>
								<div
									*ngIf="dataGridConfig.ghostLoad && !row"
									class="ghost-bar"
									style="height: 20px !important; width: 100% !important"></div>
							</ng-container>
						</ng-container>
					</td>

					<!--Action column -->
					<td *ngIf="dataGridConfig.actions.length" class="action-cell">
						<i
							(click)="iconClickHandler(row, icon, rowIndex)"
							*ngFor="let icon of rawDataIcons[rowIndex]"
							[class]="icon.iconClass"
							[id]="
								idSuffix
									? 'element-' +
									  rowIndex +
									  '-' +
									  icon.nome.toLowerCase() +
									  '-' +
									  idSuffix
									: 'element-' + rowIndex + '-' + icon.nome.toLowerCase()
							"
							[ngbTooltip]="icon.tooltipText"></i>
					</td>

					<!--Dropdown Action column -->
					<td *ngIf="dataGridConfig.dropDownActions.length" class="action-cell">
						<div
							#dropdownActions="ngbDropdown"
							[autoClose]="'outside'"
							[placement]="'bottom-right'"
							class="d-inline-block"
							ngbDropdown>
							<div
								[id]="
									idSuffix
										? 'dropdownActions-' + rowIndex + '-' + idSuffix
										: 'dropdownActions-' + rowIndex
								"
								ngbDropdownToggle>
								<i
									[id]="
										idSuffix
											? 'ddaction-' + rowIndex + '-' + idSuffix
											: 'ddaction-' + rowIndex
									"
									class="pct pct-more-horizontal"
									title=""></i>
							</div>

							<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
								<div
									*ngFor="
										let item of rawDropdownActionItems[rowIndex];
										let i = index
									"
									[attr.data-index]="i">
									<hr *ngIf="i > 0" class="solid" />
									<div
										(click)="item.actionFn(row, rowIndex)"
										[id]="
											idSuffix
												? 'ddAction-' +
												  rowIndex +
												  '-' +
												  item.nome.toLowerCase() +
												  '-' +
												  idSuffix
												: 'ddAction-' + rowIndex + '-' + item.nome.toLowerCase()
										"
										class="type-p-small item-dropdown"
										title="{{ item.tooltipText }}">
										<i
											*ngIf="item.iconClass.length"
											class="{{ item.iconClass }}"></i>
										{{ item.nome }}
									</div>
								</div>
							</div>
						</div>
					</td>

					<!-- Ordenable icon column -->
					<td
						*ngIf="dataGridConfig?.ordenable"
						class="column-ordelable-icon cor-action-default-able04">
						<i class="pct pct-move-2"></i>
					</td>
				</tr>
			</ng-container>
			<ng-container *ngIf="customRows">
				<tr
					*ngFor="let row of dados; let lastRow = last; let rowIndex = index"
					[@fadeIn]="dados.length"
					[id]="
						idSuffix
							? 'elements-' + rowIndex + '-' + idSuffix
							: 'elements-' + rowIndex
					"
					[ngClass]="{
						'total-row': dataGridConfig.totalRow && lastRow,
						ghost: dataGridConfig.ghostLoad && loading
					}">
					<!-- Data columns -->
					<td style="border-top: 0">
						<table *ngIf="row != undefined" class="table-tabe">
							<tr>
								<ng-container [ngSwitch]="dataGridConfig.totalRow && lastRow">
									<!-- DATA COLUMNS -->
									<span>
										<ng-container>
											<table class="table-tabe-inside">
												<tr>
													<td
														*ngIf="
															!isUndefinedOrNullOrEmpty(nameEnumHeaderField)
														"
														colspan="3">
														<i
															class="pct  {{
																row[nameFieldHeader][iconEnumHeaderField]
															}} icon-color-head">
															<span class="item-title">
																{{
																	" " +
																		row[nameFieldHeader][nameEnumHeaderField]
																}}
															</span>
														</i>
													</td>
													<td
														*ngIf="
															isUndefinedOrNullOrEmpty(nameEnumHeaderField)
														"
														colspan="3">
														<i
															class="pct {{ iconFieldHeader }} icon-color-head">
															<span class="item-title">
																{{ row[nameFieldHeader] }}
															</span>
														</i>
													</td>
												</tr>
												<tr>
													<td
														*ngIf="checkHtmlTags(row[nameFirstFieldContent])"
														colspan="3">
														<span
															[innerHTML]="row[nameFirstFieldContent]"
															class="item-content"></span>
													</td>
													<td
														*ngIf="!checkHtmlTags(row[nameFirstFieldContent])"
														colspan="3">
														<span>{{ row[nameFirstFieldContent] }}</span>
													</td>
												</tr>
												<tr
													*ngIf="
														!isUndefinedOrNullOrEmpty(nameSecondFieldContent)
													">
													<td colspan="3">
														<span class="item-content">
															{{ row[nameSecondFieldContent] }}
														</span>
													</td>
												</tr>
												<tr>
													<td style="vertical-align: middle">
														<i class="pct {{ iconFieldfooterleft }} icon-color">
															<span class="item-footer">
																{{ row[nameFieldfooterleft] }}
															</span>
														</i>
													</td>
													<td colspan="2" style="float: right">
														<table class="table-tabe-inside">
															<tr>
																<td
																	*ngIf="
																		!isUndefinedOrNullOrEmpty(
																			nameFieldFirstObjectfooterright
																		)
																	">
																	<i
																		class="pct {{
																			iconFieldFirstfooterright
																		}} icon-color">
																		<span class="item-footer">
																			{{
																				row[nameFieldFirstfooterright][
																					nameFieldFirstObjectfooterright
																				]
																			}}
																		</span>
																	</i>
																</td>
																<td>
																	<i
																		class="pct {{
																			iconFieldSecondfooterright
																		}} icon-color">
																		<span class="item-footer">
																			{{ row[nameFieldSecondfooterright] }}
																		</span>
																	</i>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</ng-container>
									</span>
									<div
										*ngIf="dataGridConfig.ghostLoad && !row"
										class="ghost-bar"
										style="
											height: 20px !important;
											width: 100% !important;
										"></div>
								</ng-container>
							</tr>
						</table>
					</td>

					<!--Dropdown Action column -->
					<td *ngIf="dataGridConfig.dropDownActions.length" class="action-cell">
						<div
							#dropdownActions="ngbDropdown"
							[autoClose]="'outside'"
							[placement]="'bottom-right'"
							class="d-inline-block"
							ngbDropdown>
							<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
								<div
									*ngFor="
										let item of rawDropdownActionItems[rowIndex];
										let i = index
									"
									[attr.data-index]="i">
									<hr *ngIf="i > 0" class="solid" />
									<div
										(click)="item.actionFn(row, rowIndex)"
										[id]="
											idSuffix
												? 'ddActions-' +
												  rowIndex +
												  '-' +
												  item.nome.toLowerCase() +
												  '-' +
												  idSuffix
												: 'ddActions-' +
												  rowIndex +
												  '-' +
												  item.nome.toLowerCase()
										"
										class="type-p-small item-dropdown"
										title="{{ item.tooltipText }}">
										<i
											*ngIf="item.iconClass.length"
											class="{{ item.iconClass }}"></i>
										{{ item.nome }}
									</div>
								</div>
							</div>
						</div>
					</td>
				</tr>
			</ng-container>
		</ng-container>
	</tbody>
</table>

<div
	*ngIf="dados.length === 0 && !loading && !customEmptyContent && !enableDs3"
	class="empty-state"
	i18n="@@component-relatorio:sem-dados-disponiveis">
	{{ emptyStateMessage }}
</div>
<div
	*ngIf="dados.length === 0 && !loading && !customEmptyContent && enableDs3"
	class="ds3-empty-state"
	i18n="@@component-relatorio:sem-dados-disponiveis">
	<img alt="" src="pacto-ui/images/empty-state-search.svg" />
	<h2>
		{{ emptyStateMessage ? emptyStateMessage : "Nenhum item encontrado" }}
	</h2>
	<p>
		{{
			emptyStateDescription
				? emptyStateDescription
				: "Refaça os filtros ou cadastre um novo"
		}}
	</p>
</div>

<ng-container *ngIf="dados.length === 0 && !loading && customEmptyContent">
	<ng-container *ngTemplateOutlet="customEmptyContent"></ng-container>
</ng-container>

<div *ngIf="loading && !dataGridConfig.ghostLoad" class="loading-state">
	<span
		[id]="idSuffix ? 'carregando_dados-' + idSuffix : 'carregando_dados'"
		i18n="@@component-relatorio:carregando-dados">
		Carregando dados...
	</span>
	<img class="loading-data-icon" src="pacto-ui/images/loading.svg" />
</div>
