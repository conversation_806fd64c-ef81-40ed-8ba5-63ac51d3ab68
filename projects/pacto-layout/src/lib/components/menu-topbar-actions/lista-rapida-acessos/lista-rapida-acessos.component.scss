@import "./dist/ui-kit/assets/import.scss";
@import "./dist/ui-kit/assets/ds3/ds3.scss";

.background-lista-acesso-rapida {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.5);

	.header-modal-notificacao-versao {
		font-family: Poppins, sans-serif;
		width: 100%;
		height: auto;
		display: grid;
		align-items: center;
		grid-template-columns: 3fr 1fr;
		background: #fafafa;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		padding: 15px 15px 0 15px;
		color: #55585e;
		font-size: 14px;
		font-weight: 600;
	}

	.gaveta-acessos .header-modal-notificacao-versao {
		padding: 15px;
		span {
			font-family: Poppins;
		}
	}

	.gaveta-acessos {
		width: 570px;
		height: 100vh;
		top: 0;
		right: 0;
		position: fixed;
		border-radius: 8px 0px 0px 0px;
		border: 2px solid #c9cbcf;
		background-color: #ffffff;
	}

	.container-gaveta {
		padding-left: 8px;
		padding-right: 8px;
		height: calc(100% - 120px);
		overflow: auto;
	}

	.linha-gaveta {
		display: flex;
		padding-bottom: 16px;
		align-items: center;
		font-size: 14px;
	}
	ds3-diviser {
		padding-bottom: 16px;
	}

	.linha-gaveta .horario-acesso-cliente {
		text-align: center;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
		padding-right: 4px;
		color: #797d86;
	}

	.linha-gaveta .card-acesso-cliente {
		border-radius: 4px;
		border: 1px solid #c9cbcf;
		background-color: #fafafa;
		padding: 8px;
		width: 100%;
		min-height: 54px;
		display: grid;
		align-items: center;
		grid-template-columns: 14px 1fr;
	}

	.linha-gaveta .barra-cor-acesso {
		width: 6px;
		height: 100%;
		border-radius: 4px;
		background-color: #494b50;
	}

	.linha-gaveta .barra-cor-acesso.vermelho-acesso {
		background-color: #af0404;
	}

	.linha-gaveta .barra-cor-acesso.amarelo-acesso {
		background-color: #afaf04;
	}

	.linha-gaveta .barra-cor-acesso.azul-acesso {
		background-color: #b4cafd;
	}

	.linha-gaveta .info-cliente-acesso {
		display: grid;
		gap: 4px;
	}

	.linha-gaveta .foto-nome-situacao-plano {
		display: grid;
		text-transform: capitalize;
		align-items: center;
		grid-template-columns: 42px 1fr;
		color: #494b50;
		font-size: 12px;
		font-style: normal;
		line-height: 125%;
	}

	.linha-gaveta .nome-plano-parq {
		display: grid;
		gap: 4px;
	}

	.linha-gaveta .foto-nome-situacao-plano img {
		width: 32px;
		height: 32px;
		border-radius: 50%;
	}
	.alerta-acesso-cliente i {
		margin-right: 4px;
	}
	.linha-gaveta .acesso-gaveta-nome {
		span {
			color: #1e60fa;
			font-family: Poppins, sans-serif;
		}
		text-transform: capitalize;
		font-family: Poppins, sans-serif;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: 100%; /* 14px */
		display: grid;
		align-items: center;
		justify-content: space-between;
		grid-template-columns: 1fr auto;
	}

	.linha-gaveta .acesso-mais-info {
		padding-left: 8px;
		color: #1e60fa;
		font-family: Poppins, sans-serif;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: 100%; /* 14px */
		letter-spacing: 0.25px;
	}

	.linha-gaveta .acesso-situacoes {
		display: grid;
		grid-template-columns: repeat(4, auto);
		text-align: right;
		gap: 3px;
		margin-left: 1px;
	}

	.linha-gaveta .acesso-situacoes .sit-acesso {
		width: 16px;
		height: 16px;
		padding: 5px 16px;
		justify-content: center;
		align-items: center;
		gap: 4px;
		border-radius: 50px;
		text-align: center;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
		margin-right: 5px;
		text-transform: uppercase;
		display: inline-table;
	}

	.linha-gaveta .acesso-situacoes span.sit-vi.sit-acesso {
		color: #163e9c;
		background-color: #bccdf5;
	}

	.linha-gaveta .acesso-situacoes span.sit-at.sit-acesso {
		color: #037d03;
		background-color: #b4fdb4;
	}

	.linha-gaveta .acesso-situacoes span.sit-in.sit-acesso {
		color: #7d0303;
		background-color: #fdb4b4;
	}

	.linha-gaveta .acesso-situacoes span.sit-tr.sit-acesso {
		color: #797d86;
		background-color: #e4e5e7;
	}

	.linha-gaveta .acesso-situacoes span.sit-no.sit-acesso {
		color: #0a4326;
		background-color: #8fefbf;
	}

	.linha-gaveta .acesso-situacoes span.sit-di.sit-acesso {
		color: #0a4326;
		background-color: #63e9a6;
	}

	.linha-gaveta .acesso-situacoes span.sit-pe.sit-acesso {
		color: #0a4326;
		background-color: #1dc973;
	}

	.linha-gaveta .acesso-situacoes span.sit-av.sit-acesso {
		color: #705810;
		background-color: #efd78f;
	}

	.linha-gaveta .acesso-situacoes span.sit-ve.sit-acesso {
		color: #705810;
		background-color: #e9c763;
	}

	.linha-gaveta .acesso-situacoes span.sit-tv.sit-acesso {
		color: #705810;
		background-color: #e2b736;
	}

	.linha-gaveta .acesso-situacoes span.sit-ca.sit-acesso {
		color: #701028;
		background-color: #f5bcca;
	}

	.linha-gaveta .acesso-situacoes span.sit-de.sit-acesso {
		color: #701028;
		background-color: #ef8fa7;
	}

	.linha-gaveta .acesso-situacoes span.sit-in.sit-acesso {
		color: #701028;
		background-color: #e96384;
	}

	.linha-gaveta .acesso-situacoes span.sit-ae.sit-acesso {
		color: #105870;
		background-color: #63c7e9;
	}

	.linha-gaveta .acesso-situacoes span.sit-cr.sit-acesso {
		color: #105870;
		background-color: #8fd7ef;
	}

	.linha-gaveta .acesso-situacoes span.sit-gympass.sit-acesso {
		color: #9c5316;
		background-color: #f5d6bc;
	}

	.linha-gaveta .acesso-situacoes span.sit-totalPass.sit-acesso {
		color: #9c5316;
		background-color: #efba8f;
	}

	.linha-gaveta .acesso-situacoes span.sit-freepass.sit-acesso {
		color: #0a4326;
		background-color: #1dc973;
	}

	.linha-gaveta .acesso-situacoes span.sit-aa.sit-acesso {
		color: #0a4326;
		background-color: #1dc973;
	}

	.filtros-gaveta {
		padding: 16px 16px 24px 43px;
		display: grid;
		align-items: center;
		justify-content: space-between;
		grid-template-columns: 1fr 1fr;
	}

	.filtros-gaveta i.pct-settings,
	.filtros-gaveta i.pct-arrow-left,
	.header-modal-notificacao-versao i.pct-x {
		font-size: 20px;
		color: #1e60fa;
		cursor: pointer;
		text-align: right;
	}

	.filtros-gaveta.configuracoes i.pct-arrow-left {
		text-align: left;
	}

	.filtros-gaveta.configuracoes .info-cfg-gaveta {
		font-family: Nunito Sans;
		font-size: 14px;
		font-weight: 400;
		line-height: 17.5px;
		text-align: center;
		margin-top: 16px;
	}

	.filtros-gaveta.configuracoes {
		grid-template-columns: auto 1fr;
		text-align: center;
		display: grid;
		span {
			font-family: Poppins;
			font-size: 14px;
			font-weight: 600;
			line-height: 17.5px;
			letter-spacing: 0.25px;
		}
	}

	.filtros-gaveta .cb-container select,
	.filtros-gaveta .cb-container select:hover {
		color: #494b50;
	}

	.alerta-aniversario-cliente {
		font-weight: 900;
	}

	.gaveta-acessos .filtros-gaveta.configuracoes,
	.gaveta-acessos .container-gaveta.configuracoes {
		display: grid;
	}

	.item-cfg-gaveta {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
		padding: 5px;
		font-size: 14px;
		font-weight: 400;
		line-height: 17.5px;
	}

	.container-gaveta.configuracoes {
		height: auto;
	}
}

.actionable:hover {
	cursor: pointer;
}
