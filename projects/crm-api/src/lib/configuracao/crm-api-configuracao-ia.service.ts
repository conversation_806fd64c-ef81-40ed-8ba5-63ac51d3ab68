import { Injectable } from "@angular/core";
import { CrmApiBaseService } from "../base/crm-api-base.service";
import { interval, Observable } from "rxjs";
import { map, switchMap } from "rxjs/operators";
import { CrmApiModule } from "../crm-api.module";
import { ApiMessage, ApiResponseV2 } from "../base/base.model";

@Injectable({
	providedIn: CrmApiModule,
})
export class CrmApiConfigIAService {
	constructor(private restService: CrmApiBaseService) {}

	consultarConfiguracao(idEmpresa: number = null): Observable<any[]> {
		return this.restService
			.get(
				`v1/configuracao/ia/consultar${
					idEmpresa ? "?idEmpresa=" + idEmpresa : ""
				}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	consultarConfiguracaoDeFases(idEmpresa: number = null): Observable<any[]> {
		return this.restService
			.get(
				`v1/configuracao/ia/consultar/fases${
					idEmpresa ? "?idEmpresa=" + idEmpresa : ""
				}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	consultarConfiguracaoDeFasesExtrasIA(
		codigoEmpresa: number = null
	): Observable<any[]> {
		return this.restService
			.get(
				`v1/configuracao/ia/consultar/fases-extra-ia${
					codigoEmpresa ? "?codigoEmpresa=" + codigoEmpresa : ""
				}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	alterar(config): Observable<any> {
		return this.restService
			.put(`v1/configuracao/ia/${config.codigo}`, config)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	excluir(codigo: number): Observable<ApiMessage[]> {
		return this.restService.delete(`v1/configuracao/ia/${codigo}`).pipe(
			map((response: ApiResponseV2<any>) => {
				return response.message;
			})
		);
	}

	incluirFases(config, codigoEmpresa): Observable<any> {
		return this.restService
			.post(`v1/configuracao/ia/incluir/fases/${codigoEmpresa}`, config)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	incluirConfiguracaoIA(config): Observable<any> {
		return this.restService.post(`v1/configuracao/ia/incluir`, config).pipe(
			map((response: ApiResponseV2<any>) => {
				return Array.isArray(response.result)
					? response.result[0]
					: response.result;
			})
		);
	}

	incluirConfiguracaoRedeIA(config): Observable<any> {
		return this.restService.post(`v1/configuracao/ia/rede`, config).pipe(
			map((response: ApiResponseV2<any>) => {
				return Array.isArray(response.result)
					? response.result[0]
					: response.result;
			})
		);
	}

	consultarConfiguracaoRedeIA(idEmpresa: number = null): Observable<any[]> {
		return this.restService
			.get(
				`v1/configuracao/ia/consultar/fases${
					idEmpresa ? "?idEmpresa=" + idEmpresa : ""
				}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	salvarUsuarioPactoConversas(data: any): Observable<any> {
		return this.restService
			.post("v1/configuracao/ia/usuario-pacto-conversas", data)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	iniciarConversaPorFase(chave: string, fases: any[]): Observable<any> {
		return this.restService
			.post(`v1/ia/conversa/fase/crm/${chave}`, fases)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	criarInstancia(configuracaoInstancia: any): Observable<any> {
		return this.restService
			.post(`v1/configuracao/ia/criar-instancia`, configuracaoInstancia)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	getQrCode(
		idInstancia: string,
		token: string,
		isDevice: boolean = false
	): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/ia/qrcode/whatsapp?idInstancia=${idInstancia}&token=${token}&isDevice=${isDevice}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	getQrCodeBase64(instanceId: string, token: string): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/ia/qrCode-base64?instance_id=${instanceId}&token=${token}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	getInstanceStatusPeriodically(): Observable<any> {
		return interval(3000).pipe(
			switchMap(() =>
				this.restService.get(`v1/configuracao/ia/check_instance`).pipe(
					map((response: ApiResponseV2<any>) => {
						return Array.isArray(response.result)
							? response.result[0]
							: response.result;
					})
				)
			)
		);
	}

	desconectarInstancia(codigoEmpresa: number): Observable<any> {
		return this.restService
			.post(
				`v1/configuracao/ia/desconectar-instancia?codigoEmpresa=${codigoEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	cancelarInstancia(
		codigoEmpresa: number,
		instanceId: string,
		token: string
	): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/ia/cancelar-instancia?instance_id=${instanceId}&token=${token}&codigoEmpresa=${codigoEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	obterInstanciaEmpresa(chaveEmpresa: string): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/ia/instancia-empresa?chaveEmpresa=${chaveEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return Array.isArray(response.result)
						? response.result[0]
						: response.result;
				})
			);
	}

	atualizarContextosCrm(codigoEmpresa: number) {
		return this.restService
			.put(`v1/ia/contextos/atualizar?codigoEmpresa=${codigoEmpresa}`, {})
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result;
				})
			);
	}

	obterWhatsappConectado(instanceId: string, token: string): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/ia/status-whatsapp-conectado?instance_id=${instanceId}&token=${token}`
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.result[0];
				})
			);
	}

	atualizarToken(codigoEmpresa: number, tokenGymbot): Observable<any> {
		return this.restService
			.post(
				`v1/configuracao/gymbot/atualizar-token?codigoEmpresa=${codigoEmpresa}&tokenGymbot=${tokenGymbot}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	consultarTokenGymbot(codigoEmpresa: number): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/gymbot/consultar-token?codigoEmpresa=${codigoEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	removerConfiguracaoGymbot(codigoEmpresa: number): Observable<any> {
		return this.restService
			.delete(
				`v1/configuracao/gymbot/remover-configuracao?codigoEmpresa=${codigoEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	obterDepartamentos(codigoEmpresa: number, tokenGymbot): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/gymbot/departamentos?codigoEmpresa=${codigoEmpresa}&tokenGymbot=${tokenGymbot}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	enviarConfiguracaoGymbot(data: any): Observable<any> {
		return this.restService
			.post(`v1/configuracao/gymbot/enviar-configuracao-gymbot`, data)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	obterCampanhas(codigoEmpresa: number): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/campanhas/consultar?codigoEmpresa=${codigoEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	salvarCampanha(data: any): Observable<any> {
		return this.restService.post(`v1/configuracao/campanhas`, data).pipe(
			map((response: ApiResponseV2<any>) => {
				return Array.isArray(response.result)
					? response.result[0]
					: response.result;
			})
		);
	}

	alterarCampanha(id: any, data: any): Observable<any> {
		return this.restService.put(`v1/configuracao/campanhas/${id}`, data).pipe(
			map((response: ApiResponseV2<any>) => {
				return Array.isArray(response.result)
					? response.result[0]
					: response.result;
			})
		);
	}

	excluirCampanha(id: any, codigoEmpresa): Observable<ApiMessage[]> {
		return this.restService
			.delete(`v1/configuracao/campanhas/${id}?codigoEmpresa=${codigoEmpresa}`)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response.message;
				})
			);
	}

	enviarConfiguracaoNotificacao(data: any): Observable<any> {
		return this.restService
			.post(`v1/configuracao/proativos/enviar-instrucao-notificacao`, data)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	obterNotificacoesProativo(codigoEmpresa: number): Observable<any> {
		return this.restService
			.get(
				`v1/configuracao/proativos/obter-notificacoes?codigoEmpresa=${codigoEmpresa}`,
				{}
			)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	processarMetas(): Observable<any> {
		return this.restService.post(`v1/configuracao/meta/processar`, {}).pipe(
			map((response: ApiResponseV2<any>) => {
				return response.result;
			})
		);
	}

	incluirPDFConversas(data: any): Observable<any> {
		return this.restService.post(`v1/configuracao/ia/incluir/pdf`, data).pipe(
			map((response: ApiResponseV2<any>) => {
				return response;
			})
		);
	}

	consultarPDFConversas(empresa: number): Observable<any> {
		return this.restService
			.get(`v1/configuracao/ia/consultar/pdf?empresa=${empresa}`)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}

	excluirPDFConversas(empresa: string): Observable<any> {
		return this.restService
			.delete(`v1/configuracao/ia/delete/pdf?empresa=${empresa}`)
			.pipe(
				map((response: ApiResponseV2<any>) => {
					return response;
				})
			);
	}
}
