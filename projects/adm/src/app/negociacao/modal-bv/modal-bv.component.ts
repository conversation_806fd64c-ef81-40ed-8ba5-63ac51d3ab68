import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Inject,
	OnDestroy,
	OnInit,
	Pipe,
	PipeTransform,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import {
	MAT_DIALOG_DATA,
	MatDialog,
	MatDialogRef,
} from "@angular/material/dialog";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	CatTableEditableComponent,
	DialogAutorizacaoAcessoComponent,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import {
	AdmCoreApiColaboradorService,
	AdmCoreApiEventoService,
	AdmCoreApiFreePassService,
	AdmCoreApiQuestionarioClienteService,
	ApiResponseList,
	Colaborador,
	Evento,
	Produto,
	QuestionarioCliente,
	Usuario,
	Vinculo,
} from "adm-core-api";
import { AdmRestService } from "../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { SessionService } from "sdk";
import { PermissaoService } from "pacto-layout";
import { ModalBvConfirmComponent } from "./modal-bv-confirm/modal-bv-confirm.component";
import { debounceTime, takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";

@Pipe({
	name: "tipoVinculoName",
})
export class TipoVinculoNamePipe implements PipeTransform {
	transform(
		codigo: string,
		tiposVinculos: Array<{ codigo: string; descricao: string }>
	): any {
		return tiposVinculos.find((c) => c.codigo === codigo).descricao;
	}
}

@Pipe({
	name: "colaboradorName",
})
export class ColaboradorNamePipe implements PipeTransform {
	transform(
		codigo: number,
		colaboradores: Array<{ codigo: number; nome: string }>
	): any {
		return colaboradores.find((c) => c.codigo === codigo).nome;
	}
}

@Component({
	selector: "adm-modal-bv",
	templateUrl: "./modal-bv.component.html",
	styleUrls: ["./modal-bv.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	encapsulation: ViewEncapsulation.None,
	host: {
		"[class.modal-bv]": "true",
	},
})
export class ModalBvComponent implements OnInit, AfterViewInit, OnDestroy {
	@ViewChild("traducoes", { static: false })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("tableVinculo", { static: false })
	tableVinculo: CatTableEditableComponent;

	form: FormGroup = new FormGroup({
		consultor: new FormControl(null, Validators.required),
		data: new FormControl(null, Validators.required),
		evento: new FormControl(),
		freepass: new FormControl(),
		observacao: new FormControl(),
		questionarioClienteGroup: new FormGroup({}),
	});
	groupNsPrefix = "groupNs";
	controlNsPrefix = "controlNs";
	groupMePrefix = "groupMe";
	controlMePrefix = "controlMe";
	groupSePrefix = "groupSe";
	controlSePrefix = "controlSe";
	groupSnPrefix = "groupSn";
	controlSnPrefix = "controlSn";
	groupTePrefix = "groupTe";
	controlTePrefix = "controlTe";

	consultores: Array<any> = new Array<any>();
	eventoList: Array<any> = new Array<any>();
	produtosFreepass: Array<any> = new Array<any>();
	tipoVinculos: Array<any> = new Array<any>();
	tipoVinculosNotSelectable: Array<any> = new Array<any>();
	tableConfig: PactoDataGridConfig;
	questionarioCliente: QuestionarioCliente;
	permissaoIncluirAlterarVinculo: boolean = false;
	vinculosData: Array<{
		codigo: number;
		tipoVinculo: { codigo: string; descricao: string };
		vinculo: { codigo: number; nome: string };
	}> = new Array<{
		codigo: number;
		tipoVinculo: { codigo: string; descricao: string };
		vinculo: { codigo: number; nome: string };
	}>();
	vinculosDataPageable: {
		content: Array<{
			codigo: number;
			tipoVinculo: { codigo: string; descricao: string };
			vinculo: { codigo: number; nome: string };
		}>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<{
			codigo: number;
			tipoVinculo: { codigo: string; descricao: string };
			vinculo: { codigo: number; nome: string };
		}>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	page = 1;
	size = 3;
	itensPerPage = [
		{ id: 3, label: "3" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
	];

	private editingVinculo: boolean = false;
	private usuarioFreepass: Usuario;
	private usuarioAlteracaoDataBv: Usuario;
	private valorDataAlterada: number;
	private dataHoje: Date = new Date();
	bvObrigatorio: boolean = false;
	private _destroy: Subject<void> = new Subject();

	constructor(
		@Inject(MAT_DIALOG_DATA) public modalData: any,
		public dialogRef: MatDialogRef<ModalBvComponent>,
		private dialog: MatDialog,
		private colaboradorService: AdmCoreApiColaboradorService,
		private freepassService: AdmCoreApiFreePassService,
		private cd: ChangeDetectorRef,
		private restService: AdmRestService,
		private questionarioClienteService: AdmCoreApiQuestionarioClienteService,
		private snotifyService: SnotifyService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		private permissaoService: PermissaoService,
		private eventoService: AdmCoreApiEventoService
	) {}

	ngOnInit() {
		if (this.modalData && !this.modalData.questionarioCliente) {
			throw Error("Necessário informar os dados do questionário do cliente!");
		}
		this.questionarioCliente = this.modalData.questionarioCliente;
		this.permissaoIncluirAlterarVinculo =
			this.permissaoService.temRecursoAdm("2.29");
		if (!this.permissaoIncluirAlterarVinculo) {
			this.form.get("consultor").disable();
		}
		this.buildQuestionarioForm();
		this.obterConsultores();
		this.obterProdutosFreepass();
		this.obterEventos();
		this.validaAlteracaoDataBv();
		this.validaAlteracaoConsultor();

		this.bvObrigatorio =
			this.permissaoService.temConfiguracaoEmpresaAdm("bvObrigatorio");
	}

	ngAfterViewInit() {
		this.initTipoVinculos();

		if (this.questionarioCliente.vinculos) {
			this.vinculosData = this.questionarioCliente.vinculos.map((v) => {
				let tipoVinculo = this.tipoVinculos.find(
					(tv) => tv.codigo === v.tipoVinculo
				);
				if (!tipoVinculo) {
					tipoVinculo = this.tipoVinculosNotSelectable.find(
						(tv) => tv.codigo === v.tipoVinculo
					);
				}
				return {
					codigo: v.codigo,
					tipoVinculo,
					vinculo: { codigo: v.codigoColaborador, nome: v.colaborador },
				};
			});
		}
		this.createVinculoPageObject(this.page, this.size, false);
		this.configTable();
	}

	ngOnDestroy() {
		this._destroy.next();
	}

	private validaAlteracaoDataBv() {
		this.dataHoje.setHours(0, 0, 0, 0);
		this.form.get("data").setValue(this.dataHoje);
		this.form
			.get("data")
			.valueChanges.pipe(debounceTime(300), takeUntil(this._destroy))
			.subscribe((v) => {
				this.valorDataAlterada = new Date(v).setHours(0, 0, 0, 0);
				if (
					this.form.get("data").valid &&
					this.valorDataAlterada !== this.dataHoje.getTime()
				) {
					const modalConfirmacao: any = this.dialog.open(
						DialogAutorizacaoAcessoComponent,
						{
							disableClose: true,
							id: "autorizacao-acesso",
							autoFocus: false,
						}
					);
					modalConfirmacao.componentInstance.form
						.get("usuario")
						.setValue(this.sessionService.loggedUser.username);
					modalConfirmacao.componentInstance.cancel
						.pipe(takeUntil(this._destroy))
						.subscribe((result) => {
							this.form
								.get("data")
								.setValue(this.dataHoje, { emitEvent: false });
						});
					modalConfirmacao.componentInstance.confirm
						.pipe(takeUntil(this._destroy))
						.subscribe((result) => {
							this.autorizarAcessoService
								.validarPermissao(
									this.sessionService.chave,
									result.data.usuario,
									result.data.senha,
									"AlterarDataBoletim",
									"2.65 - Alterar data do boletim de visita",
									this.sessionService.empresaId
								)
								.pipe(takeUntil(this._destroy))
								.subscribe(
									(responseAutorization: any) => {
										this.usuarioAlteracaoDataBv = {
											codigo: responseAutorization.content,
											username: result.data.usuario,
										} as Usuario;
										this.questionarioCliente.responsavelAlteracaoDataBv =
											this.usuarioAlteracaoDataBv;
									},
									(error) => {
										this.form
											.get("data")
											.setValue(this.dataHoje, { emitEvent: false });
										this.snotifyService.error(error.error.meta.message);
									}
								);
						});
				}
			});
	}

	private validaAlteracaoConsultor() {
		this.form
			.get("consultor")
			.valueChanges.pipe(takeUntil(this._destroy))
			.subscribe((v) => {
				if (
					this.questionarioCliente.codigoConsultorAntesAlteracao !== null &&
					this.questionarioCliente.codigoConsultorAntesAlteracao !== v
				) {
					this.questionarioClienteService
						.possuiAgendamentoConsultorAtual(
							this.modalData.cliente.codigo,
							this.questionarioCliente.codigoConsultorAntesAlteracao
						)
						.pipe(takeUntil(this._destroy))
						.subscribe((response) => {
							if (response.content) {
								const dialogRef = this.dialog.open(ModalBvConfirmComponent, {
									autoFocus: false,
									disableClose: true,
									panelClass: "mdl-bv-negociacao",
									data: {
										question: `Deseja alterar os agendamentos deste cliente para o novo colaborador?`,
									},
								});
								dialogRef
									.afterClosed()
									.pipe(takeUntil(this._destroy))
									.subscribe((responseModal) => {
										if (responseModal && responseModal.confirm) {
											this.questionarioCliente.alterarAgendamentoConsultorAtual =
												true;
										} else {
											this.questionarioCliente.alterarAgendamentoConsultorAtual =
												false;
										}
									});
							}
						});
				}
			});
	}

	private buildQuestionarioForm() {
		const formQuestionarioCliente = this.form.get(
			"questionarioClienteGroup"
		) as FormGroup;
		this.questionarioCliente.questionarioPerguntaCliente.forEach(
			(questionarioPerguntaCliente, indexPergunta) => {
				if (questionarioPerguntaCliente) {
					if (questionarioPerguntaCliente.perguntaCliente) {
						if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "NS"
						) {
							const groupPerguntaName = `${this.groupNsPrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlNsPrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName)
										.valueChanges.pipe(takeUntil(this._destroy))
										.subscribe((v) => {
											for (const controlsKey in (
												formQuestionarioCliente.get(
													groupPerguntaName
												) as FormGroup
											).controls) {
												if (controlsKey !== controlRespostaName) {
													formQuestionarioCliente
														.get(groupPerguntaName)
														.get(controlsKey)
														.setValue(false, { emitEvent: false });
												}
											}
										});
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "ME"
						) {
							const groupPerguntaName = `${this.groupMePrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlMePrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SE"
						) {
							const groupPerguntaName = `${this.groupSePrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlSePrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName)
										.valueChanges.pipe(takeUntil(this._destroy))
										.subscribe((v) => {
											for (const controlsKey in (
												formQuestionarioCliente.get(
													groupPerguntaName
												) as FormGroup
											).controls) {
												if (controlsKey !== controlRespostaName) {
													formQuestionarioCliente
														.get(groupPerguntaName)
														.get(controlsKey)
														.setValue(false, { emitEvent: false });
												}
											}
										});
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SN"
						) {
							const groupPerguntaName = `${this.groupSnPrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlSnPrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName)
										.valueChanges.pipe(takeUntil(this._destroy))
										.subscribe((v) => {
											for (const controlsKey in (
												formQuestionarioCliente.get(
													groupPerguntaName
												) as FormGroup
											).controls) {
												if (controlsKey !== controlRespostaName) {
													formQuestionarioCliente
														.get(groupPerguntaName)
														.get(controlsKey)
														.setValue(false, { emitEvent: false });
												}
											}
										});
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "TE"
						) {
							const groupPerguntaName = `${this.groupTePrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlTePrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
								}
							);
						}
					}
				}
			}
		);
	}

	private initTipoVinculos() {
		this.tipoVinculos = new Array<any>(
			{
				codigo: "OR",
				descricao: this.traducoes.getLabel("orientador"),
			},
			{
				codigo: "PE",
				descricao: this.traducoes.getLabel("personal-externo"),
			},
			{
				codigo: "PI",
				descricao: this.traducoes.getLabel("personal-interno"),
			},
			{
				codigo: "PT",
				descricao: this.traducoes.getLabel("personal-trainer"),
			},
			{
				codigo: "PR",
				descricao: this.traducoes.getLabel("professor"),
			},
			{
				codigo: "TW",
				descricao: this.traducoes.getLabel("professor-treino-web"),
			},
			{
				codigo: "TE",
				descricao: this.traducoes.getLabel("terceirizado"),
			}
		);

		this.tipoVinculosNotSelectable = new Array<any>(
			{
				codigo: "ES",
				descricao: this.traducoes.getLabel("estudio"),
			},
			{
				codigo: "FO",
				descricao: this.traducoes.getLabel("fornecedor"),
			},
			{
				codigo: "CR",
				descricao: this.traducoes.getLabel("coordenador"),
			},
			{
				codigo: "MD",
				descricao: this.traducoes.getLabel("medico"),
			},
			{
				codigo: "FC",
				descricao: this.traducoes.getLabel("funcionario"),
			},
			{
				codigo: "AD",
				descricao: this.traducoes.getLabel("administrador"),
			}
		);
	}

	private configTable() {
		this.tableConfig = new PactoDataGridConfig({
			formGroup: new FormGroup({
				vinculo: new FormControl(null, Validators.required),
				tipoVinculo: new FormControl(null, Validators.required),
			}),
			dataAdapterFn: () => this.vinculosDataPageable,
			beforeConfirm: (row: any, form: FormGroup, data: any, indexRow: any) =>
				this.beforeConfirm(row, form, data, indexRow),
			columns: [
				{
					nome: "tipoVinculo",
					titulo: "Tipo de vínculo",
					inputType: "select",
					visible: true,
					editable: this.permissaoIncluirAlterarVinculo,
					showAddSelectBtn: false,
					selectOptionChange: (option: any, form: any, row: any) =>
						this.carregarColaboradorPorVinculo(option.codigo),
					valueTransform: (value: any) => {
						console.log(value);
						return value;
					},
					labelFn: (item) => {
						console.log(item);
						return item.descricao;
					},
					inputSelectData: this.tipoVinculos,
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
				},
				{
					nome: "vinculo",
					titulo: "Vínculo",
					inputType: "select",
					visible: true,
					editable: this.permissaoIncluirAlterarVinculo,
					showAddSelectBtn: false,
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					responseParser: (result) =>
						result.content.map((v) => ({
							codigo: v.codigo,
							nome: v.pessoa.nome,
						})),
					selectParamBuilder: (nome) => {
						return {
							filters: JSON.stringify({
								nome: nome ? nome : "",
								empresa: this.modalData.cliente.empresa,
							}),
						};
					},
				},
			],
		});
	}

	private obterConsultores() {
		this.colaboradorService
			.findAllConsultoresAtivos()
			.pipe(takeUntil(this._destroy))
			.subscribe((response: ApiResponseList<Colaborador>) => {
				if (response.content) {
					this.consultores = response.content.map((c) => ({
						codigo: c.codigo,
						nome: c.pessoa.nome,
					}));
					const consultorEncontrado = this.consultores.find(
						(v) => v.codigo === this.questionarioCliente.consultor.codigo
					);
					if (consultorEncontrado) {
						this.form
							.get("consultor")
							.setValue(consultorEncontrado.codigo, { emitEvent: false });
					}
					this.cd.detectChanges();
				}
			});
	}

	private obterProdutosFreepass() {
		this.freepassService
			.obterProdutosAtivos()
			.pipe(takeUntil(this._destroy))
			.subscribe((response: ApiResponseList<Produto>) => {
				if (response.content) {
					this.produtosFreepass = response.content.map((p) => {
						let descricao = `${p.descricao} - ${p.nrDiasVigencia} `;
						if (p.nrDiasVigencia === 1) {
							descricao += "dia";
						} else {
							descricao += "dias";
						}

						return {
							codigo: p.codigo,
							descricao,
						};
					});
				}
			});
	}

	private obterEventos() {
		this.eventoService
			.obterEventosHoje()
			.pipe(takeUntil(this._destroy))
			.subscribe((response) => {
				this.eventoList = response.content;
			});
	}

	save() {
		if (this.form.invalid) {
			if (this.form.get("consultor").invalid) {
				this.snotifyService.error("Consultor não informado!");
				return;
			}
			if (this.form.get("data").invalid) {
				this.snotifyService.error("Data não informada!");
				return;
			}
		}
		if (!this.validaAndConvertQuestoesObrigatorias()) {
			return;
		}

		const respondeuTodasQuestoes = this.validaRespondeuTodasQuestoes();
		if (
			this.permissaoService.temConfiguracaoEmpresaAdm("bvObrigatorio") &&
			!respondeuTodasQuestoes.valid
		) {
			this.snotifyService.error(respondeuTodasQuestoes.message);
			return;
		}

		if (!respondeuTodasQuestoes.valid) {
			const dialogRef = this.dialog.open(ModalBvConfirmComponent, {
				panelClass: "mdl-bv-negociacao",
				disableClose: true,
				autoFocus: false,
			});
			dialogRef
				.afterClosed()
				.pipe(takeUntil(this._destroy))
				.subscribe((result) => {
					if (result && result.confirm) {
						this.saveFinal();
					}
				});
		} else {
			this.saveFinal();
		}
	}

	private saveFinal() {
		this.questionarioCliente.consultor = {
			codigo: this.form.get("consultor").value,
		} as Colaborador;
		this.questionarioCliente.data = this.form.get("data").value;
		if (this.form.get("evento").value !== null) {
			this.questionarioCliente.evento = {
				codigo: this.form.get("evento").value,
			} as Evento;
		}
		if (this.form.get("freepass").value !== null) {
			this.questionarioCliente.freepass = {
				codigo: this.form.get("freepass").value,
			} as Produto;
		}
		this.questionarioCliente.observacao = this.form.get("observacao").value;
		let hasInvalidTipoVinculo = false;
		let hasInvalidVinculo = false;

		for (const v of this.vinculosData) {
			if (!v.vinculo) {
				hasInvalidVinculo = true;
				break;
			}
			if (!v.tipoVinculo) {
				hasInvalidTipoVinculo = true;
				break;
			}
		}

		if (hasInvalidTipoVinculo) {
			this.snotifyService.error(
				"Há vínculos que não foram preenchidos corretamente. Por favor, verifique e tente novamente."
			);
			return;
		}

		if (hasInvalidVinculo) {
			this.snotifyService.error(
				"Há vínculos que não foram preenchidos corretamente. Por favor, verifique e tente novamente."
			);
			return;
		}

		this.questionarioCliente.vinculos = this.vinculosData.map(
			(v) =>
				({
					codigo: v.codigo,
					tipoVinculo: v.tipoVinculo.codigo,
					codigoColaborador: +v.vinculo.codigo,
					colaborador: null,
				} as Vinculo)
		);

		if (
			this.questionarioCliente.freepass &&
			this.questionarioCliente.freepass.codigo
		) {
			const dialogRef = this.dialog.open(ModalBvConfirmComponent, {
				autoFocus: false,
				disableClose: true,
				panelClass: "mdl-bv-negociacao",
				data: {
					question: `Você está cadastrando o cliente ${this.modalData.cliente.nome} com um Freepass!`,
				},
			});
			dialogRef
				.afterClosed()
				.pipe(takeUntil(this._destroy))
				.subscribe((response) => {
					if (response && response.confirm) {
						const modalConfirmacao: any = this.dialog.open(
							DialogAutorizacaoAcessoComponent,
							{
								disableClose: true,
								id: "autorizacao-acesso",
								autoFocus: false,
							}
						);
						modalConfirmacao.componentInstance.form
							.get("usuario")
							.setValue(this.sessionService.loggedUser.username);
						modalConfirmacao.componentInstance.confirm
							.pipe(takeUntil(this._destroy))
							.subscribe((result) => {
								this.autorizarAcessoService
									.validarPermissao(
										this.sessionService.chave,
										result.data.usuario,
										result.data.senha,
										"PermissaoFreePass",
										"2.51 - Lançar Free Pass",
										this.sessionService.empresaId
									)
									.pipe(takeUntil(this._destroy))
									.subscribe(
										(responseAutorization: any) => {
											this.usuarioFreepass =
												modalConfirmacao.componentInstance.form.getRawValue();
											this.questionarioCliente.responsavelFreepass =
												this.usuarioFreepass;
											this.cadastrarBv();
										},
										(error) => {
											this.snotifyService.error(error.error.meta.message);
										}
									);
							});
					}
				});
		} else {
			this.cadastrarBv();
		}
	}

	private cadastrarBv() {
		this.questionarioClienteService
			.cadastrarBv(this.questionarioCliente, this.modalData.rematricula)
			.pipe(takeUntil(this._destroy))
			.subscribe(
				(response) => {
					this.snotifyService.success("Boletim de visita salvo com sucesso!");
					this.questionarioCliente = response.content;
					this.dialogRef.close({
						atualizouBV: true,
						questionarioCliente: this.questionarioCliente,
					});
				},
				(httpResponseError) => {
					if (httpResponseError.error) {
						if (httpResponseError.error.meta) {
							if (httpResponseError.error.meta.message) {
								this.snotifyService.error(httpResponseError.error.meta.message);
							}
						}
					}
				}
			);
	}

	private validaAndConvertQuestoesObrigatorias(): boolean {
		const formQuestionarioCliente = this.form.get(
			"questionarioClienteGroup"
		) as FormGroup;
		for (
			let indexPergunta = 0;
			indexPergunta <
			this.questionarioCliente.questionarioPerguntaCliente.length;
			indexPergunta++
		) {
			const questionarioPerguntaCliente =
				this.questionarioCliente.questionarioPerguntaCliente[indexPergunta];
			if (questionarioPerguntaCliente) {
				if (
					questionarioPerguntaCliente.perguntaCliente &&
					questionarioPerguntaCliente.perguntaCliente.obrigatoria
				) {
					if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "NS"
					) {
						const groupPerguntaName = `${this.groupNsPrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlNsPrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (!hasAswer) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "ME"
					) {
						const groupPerguntaName = `${this.groupMePrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlMePrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (!hasAswer) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SE"
					) {
						const groupPerguntaName = `${this.groupSePrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlSePrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (!hasAswer) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SN"
					) {
						const groupPerguntaName = `${this.groupSnPrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlSnPrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (!hasAswer) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "TE"
					) {
						const groupPerguntaName = `${this.groupTePrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlTePrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaTextual =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (!hasAswer) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					}
				}
			}
		}
		return true;
	}

	private validaRespondeuTodasQuestoes(): { valid: boolean; message?: string } {
		const formQuestionarioCliente = this.form.get(
			"questionarioClienteGroup"
		) as FormGroup;
		for (
			let indexPergunta = 0;
			indexPergunta <
			this.questionarioCliente.questionarioPerguntaCliente.length;
			indexPergunta++
		) {
			const questionarioPerguntaCliente =
				this.questionarioCliente.questionarioPerguntaCliente[indexPergunta];
			if (questionarioPerguntaCliente) {
				if (questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "NS") {
					const groupPerguntaName = `${this.groupNsPrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlNsPrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "ME"
				) {
					const groupPerguntaName = `${this.groupMePrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlMePrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SE"
				) {
					const groupPerguntaName = `${this.groupSePrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlSePrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SN"
				) {
					const groupPerguntaName = `${this.groupSnPrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlSnPrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "TE"
				) {
					const groupPerguntaName = `${this.groupTePrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlTePrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaTextual =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				}
			}
		}
		return { valid: true };
	}

	private carregarColaboradorPorVinculo(tipoVinculo?: string) {
		this.tableConfig.columns.find((c) => c.nome === "vinculo").endpointUrl =
			tipoVinculo
				? this.restService.buildFullUrlAdmCore(
						`colaboradores/ativos/${tipoVinculo}/full`
				  )
				: null;
	}

	editVinculo(event: { row; form: FormGroup }) {
		this.editingVinculo = true;
		this.carregarColaboradorPorVinculo(event.row.tipoVinculo.codigo);
	}

	saveVinculo(event: {
		row: any;
		form: FormGroup;
		data: any;
		rowIndex: number;
	}) {
		this.carregarColaboradorPorVinculo(null);
		if (!this.editingVinculo) {
			this.vinculosData.push(event.form.value);
		} else {
			const realIndex = event.rowIndex + this.size * (this.page - 1);
			this.vinculosData[realIndex] = { ...event.form.value };
		}
		this.editingVinculo = false;
		this.createVinculoPageObject();
	}

	removeVinculo(event: { row: any; index: number; data: any }) {
		if (!event.row.edit || this.editingVinculo) {
			this.vinculosData.splice(event.index + this.size * (this.page - 1), 1);
		}
		this.createVinculoPageObject();
	}

	changePageVinculos(page) {
		console.trace("changePageVinculos");
		this.page = page;
		this.createVinculoPageObject();
	}

	changePageSizeVinculos(size) {
		this.size = size;
		this.createVinculoPageObject();
	}

	createVinculoPageObject(
		page?: number,
		size?: number,
		reloadTable: boolean = true
	) {
		if (!page) {
			page = this.page;
		}
		if (!size) {
			size = this.size;
		}
		this.vinculosDataPageable.totalElements = this.vinculosData.length;
		this.vinculosDataPageable.size = size;
		this.vinculosDataPageable.totalPages = Math.ceil(
			+(
				this.vinculosDataPageable.totalElements / this.vinculosDataPageable.size
			)
		);
		if (
			this.vinculosDataPageable.totalPages > 0 &&
			this.vinculosDataPageable.totalPages < this.page
		) {
			this.page -= 1;
			page = this.page;
		}
		this.vinculosDataPageable.content = this.vinculosData.slice(
			size * page - size,
			size * page
		);
		this.vinculosDataPageable.number = page;
		this.vinculosDataPageable.first = page === 0 || page === 1;
		this.vinculosDataPageable.last =
			page === this.vinculosDataPageable.totalPages;
		if (this.tableVinculo && reloadTable) {
			this.tableVinculo.data = this.vinculosDataPageable;
			this.tableVinculo.reloadData();
		}
	}

	private beforeConfirm(
		row: any,
		form: FormGroup,
		data: Array<any>,
		indexRow: any
	) {
		const vinculoJaAdicionado = this.vinculosData.findIndex(
			(v, index) =>
				index !== indexRow + this.size * (this.page - 1) &&
				v.vinculo.codigo === row.vinculo.codigo
		);
		if (vinculoJaAdicionado !== -1) {
			this.snotifyService.error(
				"Já foi adicionado o vínculo para esse colaborador"
			);
			return false;
		}

		if (form.get("tipoVinculo").invalid) {
			this.snotifyService.error("Vínculo não selecionado!");
			return false;
		}
		if (form.get("vinculo").invalid) {
			this.snotifyService.error("Vínculo não selecionado!");
			return false;
		}
		return true;
	}
}
