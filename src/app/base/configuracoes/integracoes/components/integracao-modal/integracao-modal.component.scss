@import "src/assets/scss/pacto/plataforma-import.scss";

.block-info {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-column-gap: 12px;
	padding: 0px 28px 24px 28px;
}

.table {
	margin-bottom: 0px;
}

.block-table {
	display: grid;
	padding: 0px 28px 24px 28px;
}

.block-table-mqv {
	display: grid;
	padding: 24px 28px 24px 28px;
}

.unidade {
	display: flex;
	align-items: center;
	height: 42px;
}

.text-table {
	font-weight: 700;
	margin-bottom: 8px;
}

.input-gymId {
	width: 114px;
	height: 40px;
	float: right;
	border: 1px solid $cinza03;
	border-radius: 4px;
}

.input-gymId[type="text"] {
	padding-left: 7px;
	color: $preto02;
}

.line {
	border-bottom: 1px solid $cinza02;
}

.select-pageSize {
	margin-right: 20px;
	margin-top: 4px;
}

.input-pageSize[type="number"] {
	padding-left: 7px;
	color: $preto02;
}

.col-gym {
	display: flex;
	justify-content: flex-end;
	margin-right: 56px;
}

.results {
	display: flex;
	width: 53.5%;
	margin-top: 9px;
}

.footer-pagination {
	display: flex;
	justify-content: flex-end;
	margin-right: 11px;
	margin-top: 20px;
}

.sk-fading-circle {
	position: absolute;
	width: 30px;
	height: 30px;
	left: calc(50% - 30px / 2);
	top: calc(50% - 30px / 2);
	-webkit-animation-name: spin;
	-webkit-animation-duration: 2000ms;
	-webkit-animation-iteration-count: infinite;
	-webkit-animation-timing-function: linear;
	-moz-animation-name: spin;
	-moz-animation-duration: 2000ms;
	-moz-animation-iteration-count: infinite;
	-moz-animation-timing-function: linear;

	animation-name: spin;
	animation-duration: 2000ms;
	animation-iteration-count: infinite;
	animation-timing-function: linear;
}

.pct-refresh-cw {
	color: $azulPacto04;
	font-size: 30px;
	line-height: 30px;
	font-style: normal;
}

.loading-text {
	@extend .type-h6-bold;
	color: $azulPacto04;
	position: absolute;
	width: 168px;
	height: 16px;
	left: calc(50% - 168px / 2);
	top: calc(50% + 35px / 2);
}
