import {
	ConfiguracoesA<PERSON>s,
	ConfiguracoesAplicativos,
	ConfiguracoesAvaliacao,
	ConfiguracoesGerais,
	ConfiguracoesTreino,
	ConfiguracoesGestao,
	ConfiguracoesManutencao,
	ConfiguracoesNotificacao,
	ConfiguracoesIa,
} from "../configuracoes-treino.model";

export class ConfigTreinoPayload {
	mobile_sempre_atualizar_carga_ficha?: boolean;
	permitir_reagendamento_por_aluno?: boolean;
	modulo_aulas?: boolean;
	modulo_aulas_aba_turmas?: boolean;
	modulo_aulas_aba_saldo?: boolean;
	modulo_treinar?: boolean;
	aluno_marcar_propria_aula?: boolean;
	habilitar_crossfit?: boolean;
	nao_exibir_numero_de_vezes_no_app?: boolean;
	proibir_marcar_aula_parcela_vencida?: boolean;
	proibir_buscar_programa_parcela_vencida?: boolean;
	nome_aplicativo_para_envio_email?: string;
	link_app_email?: string;
	dias_mostrar_totem?: string;
	habilitar_fila_espera?: boolean;
	habilitar_ver_wod_todas_empresas_app?: boolean;
	//--
	nr_aula_experimental_aluno?: string;
	minutos_agendar_com_antecedencia?: string;
	minutos_desmarcar_com_antecedencia?: string;
	validar_modalidade?: string;
	controlar_por_freepass?: string;
	validar_horario_contrato?: string;
	permitir_aluno_marcar_aula_por_tipo_modalidade?: string;
	limite_dias_reposicao_aula_coletiva?: string;
	manter_reposicao_aula_coletiva?: boolean;
	bloquear_mesmo_ambiente?: boolean;
	desmarcar_aulas_futuras_parcela_atrasada?: string;
	bloquear_aula_coletiva_nao_pertence_modalidade?: string;
	nr_validar_vezes_modalidade: string;
	//--
	cfg_objetivos_anamnese?: boolean;
	cfg_peso_altura_pa_fc?: boolean;
	cfg_parq?: boolean;
	cfg_dobras_cutaneas?: boolean;
	cfg_perimetria?: boolean;
	cfg_composicao_corporal?: boolean;
	cfg_flexibilidade?: boolean;
	cfg_postura?: boolean;
	cfg_rml?: boolean;
	cfg_rml_opcoes?: boolean;
	cfg_rml_separado?: boolean;
	cfg_tipo_rml?: string;
	cfg_vo2max?: boolean;
	cfg_recomendacoes?: boolean;
	cfg_ventilometria?: boolean;
	cfg_testes_campo?: boolean;
	cfg_teste_bike?: boolean;
	cfg_somatotipia?: boolean;
	cfg_teste_queens?: boolean;
	cfg_importacao_biosanny?: boolean;
	lancar_agendamento_proxima_avaliacao?: boolean;
	lancar_produto_avaliacao?: boolean;
	validar_produto_avaliacao?: any;
	produto_avaliacao?: any;
	lancar_produto_avaliacao_data_vencimento?: boolean;
	usar_pressao_sistolica_diastolica?: any;
	usar_ordem_dobras_cutaneas?: boolean;
	ordens_dobras?: string;
	modelo_evolucao_fisica?: string;
	produtos?: any;
	obrigar_campos_dobras_bioimpedancia?: boolean;
	//
	validar_agenda_aulacheia?: boolean;
	desativar_tela_aluno_treino?: boolean;
	troca_nomenclatura_crossfit?: string;
	configuracoes_treino_replicar_rede_empresa?: boolean;
	duracao_aluno_na_academia?: string;
	//
	periodo_usado_bi?: string;
	inativos_a_x_dias?: string;
	somente_aluno_contrato_desistente?: string;
	//
	aplicativo_personalizado?: boolean;
	aplicativo_personalizado_nome?: string;
	aplicativo_personalizado_url?: string;
	//
	iniciou_treino?: boolean;
	aumentou_carga?: boolean;
	diminuiu_carga?: boolean;
	aluno_chegou?: boolean;
	agenda?: boolean;
	agendamento_confirmado?: boolean;
	solicitar_renovacao?: boolean;
	lembrar_aluno_compromisso?: boolean;
	agendamento_alterado?: boolean;
	aluno_em_risco?: boolean;
	aluno_agendou?: boolean;
	numero_dias_notificar_treino_vencido?: boolean;
	sms_notificacao?: boolean;
	//
	emitir_ficha_apos_vencimento_treino?: string;
	numero_impressao_ficha?: string;
	bloquear_impressao_ficha_apos_todas_execucoes?: string;
	dias_antes_vencimento?: string;
	permitir_apenas_alunos_ativos?: string;
	visualizar_mensagem_aviso?: string;
	permitir_visualizar_wod?: string;
	permitir_visualizar_cref?: string;
	permitir_visualizar_par_q_10_perguntas?: string;
	permitir_visualizar_lei_parq?: string;
	agrupamento_series_set?: boolean;
	//
	permitir_criar_treino_automatizado_ia?: boolean;
	tempo_aprovacao_automatica?: number;
	tempo_maximo_revisao?: number;
	habilitar_obrigatoriedade_aprovacao_professor?: boolean;
	permitir_aluno_criar_treino_ia_app?: boolean;

	constructor() {}

	public initConfiguracoesAulas(data: ConfiguracoesAulas) {
		this.nr_aula_experimental_aluno = data.nr_aula_experimental_aluno;
		this.minutos_agendar_com_antecedencia =
			data.minutos_agendar_com_antecedencia;
		this.minutos_desmarcar_com_antecedencia =
			data.minutos_desmarcar_com_antecedencia;
		this.validar_modalidade = data.validar_modalidade;
		this.controlar_por_freepass = data.controlar_por_freepass;
		this.validar_horario_contrato = data.validar_horario_contrato;
		this.permitir_aluno_marcar_aula_por_tipo_modalidade =
			data.permitir_aluno_marcar_aula_por_tipo_modalidade;
		this.limite_dias_reposicao_aula_coletiva =
			data.limite_dias_reposicao_aula_coletiva;
		this.manter_reposicao_aula_coletiva = data.manter_reposicao_aula_coletiva;
		this.bloquear_mesmo_ambiente = data.bloquear_mesmo_ambiente;
		this.desmarcar_aulas_futuras_parcela_atrasada =
			data.desmarcar_aulas_futuras_parcela_atrasada;
		this.bloquear_aula_coletiva_nao_pertence_modalidade =
			data.bloquear_aula_coletiva_nao_pertence_modalidade;
		this.nr_validar_vezes_modalidade = data.nr_validar_vezes_modalidade;
	}

	public initConfiguracoesAplicativos(data: ConfiguracoesAplicativos) {
		this.mobile_sempre_atualizar_carga_ficha =
			data.mobile_sempre_atualizar_carga_ficha;
		this.permitir_reagendamento_por_aluno =
			data.permitir_reagendamento_por_aluno;
		this.modulo_aulas = data.modulo_aulas;
		this.modulo_aulas_aba_turmas = data.modulo_aulas_aba_turmas;
		this.modulo_aulas_aba_saldo = data.modulo_aulas_aba_saldo;
		this.modulo_treinar = data.modulo_treinar;
		this.aluno_marcar_propria_aula = data.aluno_marcar_propria_aula;
		this.habilitar_crossfit = data.habilitar_crossfit;
		this.nao_exibir_numero_de_vezes_no_app =
			data.nao_exibir_numero_de_vezes_no_app;
		this.proibir_marcar_aula_parcela_vencida =
			data.proibir_marcar_aula_parcela_vencida;
		this.proibir_buscar_programa_parcela_vencida =
			data.proibir_buscar_programa_parcela_vencida;
		this.nome_aplicativo_para_envio_email =
			data.nome_aplicativo_para_envio_email;
		this.link_app_email = data.link_app_email;
		this.dias_mostrar_totem = data.dias_mostrar_totem;
		this.habilitar_fila_espera = data.habilitar_fila_espera;
		this.habilitar_ver_wod_todas_empresas_app =
			data.habilitar_ver_wod_todas_empresas_app;
	}

	public initAvaliacaoFisica(data: ConfiguracoesAvaliacao) {
		this.cfg_objetivos_anamnese = data.cfg_objetivos_anamnese;
		this.cfg_peso_altura_pa_fc = data.cfg_peso_altura_pa_fc;
		this.cfg_parq = data.cfg_parq;
		this.cfg_dobras_cutaneas = data.cfg_dobras_cutaneas;
		this.cfg_perimetria = data.cfg_perimetria;
		this.cfg_composicao_corporal = data.cfg_composicao_corporal;
		this.cfg_flexibilidade = data.cfg_flexibilidade;
		this.cfg_postura = data.cfg_postura;
		this.cfg_rml = data.cfg_rml;
		this.cfg_rml_opcoes = data.cfg_rml_opcoes;
		this.cfg_rml_separado = data.cfg_rml_separado;
		this.cfg_tipo_rml = data.cfg_tipo_rml;
		this.cfg_vo2max = data.cfg_vo2max;
		this.cfg_recomendacoes = data.cfg_recomendacoes;
		this.cfg_ventilometria = data.cfg_ventilometria;
		this.cfg_testes_campo = data.cfg_testes_campo;
		this.cfg_teste_bike = data.cfg_teste_bike;
		this.cfg_somatotipia = data.cfg_somatotipia;
		this.cfg_teste_queens = data.cfg_teste_queens;
		this.cfg_importacao_biosanny = data.cfg_importacao_biosanny;
		this.lancar_agendamento_proxima_avaliacao =
			data.lancar_agendamento_proxima_avaliacao;
		this.lancar_produto_avaliacao = data.lancar_produto_avaliacao;
		this.validar_produto_avaliacao = data.validar_produto_avaliacao;
		this.produto_avaliacao = data.produto_avaliacao;
		this.lancar_produto_avaliacao_data_vencimento =
			data.lancar_produto_avaliacao_data_vencimento;
		this.usar_pressao_sistolica_diastolica =
			data.usar_pressao_sistolica_diastolica;
		this.usar_ordem_dobras_cutaneas = data.usar_ordem_dobras_cutaneas;
		this.ordens_dobras = data.ordens_dobras;
		this.modelo_evolucao_fisica = data.modelo_evolucao_fisica;
		this.produtos = data.produtos;
		this.obrigar_campos_dobras_bioimpedancia =
			data.obrigar_campos_dobras_bioimpedancia;
	}

	public initConfigGerais(data: ConfiguracoesGerais) {
		this.validar_agenda_aulacheia = data.validar_agenda_aulacheia;
		this.desativar_tela_aluno_treino = data.desativar_tela_aluno_treino;
		this.troca_nomenclatura_crossfit = data.troca_nomenclatura_crossfit;
		this.configuracoes_treino_replicar_rede_empresa =
			data.configuracoes_treino_replicar_rede_empresa;
		this.duracao_aluno_na_academia = data.duracao_aluno_na_academia;
	}

	public initConfigGestao(data: ConfiguracoesGestao) {
		this.periodo_usado_bi = data.periodo_usado_bi;
		this.inativos_a_x_dias = data.inativos_a_x_dias;
		this.somente_aluno_contrato_desistente =
			data.somente_aluno_contrato_desistente;
	}

	public initConfigManutencao(data: ConfiguracoesManutencao) {
		this.aplicativo_personalizado = data.aplicativo_personalizado;
		this.aplicativo_personalizado_nome = data.aplicativo_personalizado_nome;
		this.aplicativo_personalizado_url = data.aplicativo_personalizado_url;
	}

	public initConfigNotificacao(data: ConfiguracoesNotificacao) {
		this.iniciou_treino = data.iniciou_treino;
		this.aumentou_carga = data.aumentou_carga;
		this.diminuiu_carga = data.diminuiu_carga;
		this.aluno_chegou = data.aluno_chegou;
		this.agenda = data.agenda;
		this.agendamento_confirmado = data.agendamento_confirmado;
		this.solicitar_renovacao = data.solicitar_renovacao;
		this.lembrar_aluno_compromisso = data.lembrar_aluno_compromisso;
		this.agendamento_alterado = data.agendamento_alterado;
		this.aluno_em_risco = data.aluno_em_risco;
		this.aluno_agendou = data.aluno_agendou;
		this.numero_dias_notificar_treino_vencido =
			data.numero_dias_notificar_treino_vencido;
		this.sms_notificacao = data.sms_notificacao;
	}

	public initConfigTreino(data: ConfiguracoesTreino) {
		this.emitir_ficha_apos_vencimento_treino =
			data.emitir_ficha_apos_vencimento_treino;
		this.numero_impressao_ficha = data.numero_impressao_ficha;
		this.bloquear_impressao_ficha_apos_todas_execucoes =
			data.bloquear_impressao_ficha_apos_todas_execucoes;
		this.dias_antes_vencimento = data.dias_antes_vencimento;
		this.permitir_apenas_alunos_ativos = data.permitir_apenas_alunos_ativos;
		this.visualizar_mensagem_aviso = data.visualizar_mensagem_aviso;
		this.permitir_visualizar_wod = data.permitir_visualizar_wod;
		this.permitir_visualizar_cref = data.permitir_visualizar_cref;
		this.permitir_visualizar_par_q_10_perguntas =
			data.permitir_visualizar_par_q_10_perguntas;
		this.permitir_visualizar_lei_parq = data.permitir_visualizar_lei_parq;
		this.agrupamento_series_set = data.agrupamento_series_set;
	}

	public static getConfiguracoesAulas(
		data: ConfigTreinoPayload
	): ConfiguracoesAulas {
		return {
			nr_aula_experimental_aluno: data.nr_aula_experimental_aluno,
			minutos_agendar_com_antecedencia: data.minutos_agendar_com_antecedencia,
			minutos_desmarcar_com_antecedencia:
				data.minutos_desmarcar_com_antecedencia,
			validar_modalidade: data.validar_modalidade,
			controlar_por_freepass: data.controlar_por_freepass,
			validar_horario_contrato: data.validar_horario_contrato,
			permitir_aluno_marcar_aula_por_tipo_modalidade:
				data.permitir_aluno_marcar_aula_por_tipo_modalidade,
			limite_dias_reposicao_aula_coletiva:
				data.limite_dias_reposicao_aula_coletiva,
			manter_reposicao_aula_coletiva: data.manter_reposicao_aula_coletiva,
			bloquear_mesmo_ambiente: data.bloquear_mesmo_ambiente,
			desmarcar_aulas_futuras_parcela_atrasada:
				data.desmarcar_aulas_futuras_parcela_atrasada,
			bloquear_aula_coletiva_nao_pertence_modalidade:
				data.bloquear_aula_coletiva_nao_pertence_modalidade,
			nr_validar_vezes_modalidade: data.nr_validar_vezes_modalidade,
		};
	}

	public static getConfiguracoesAplicativos(
		data: ConfigTreinoPayload
	): ConfiguracoesAplicativos {
		return {
			mobile_sempre_atualizar_carga_ficha:
				data.mobile_sempre_atualizar_carga_ficha,
			permitir_reagendamento_por_aluno: data.permitir_reagendamento_por_aluno,
			modulo_aulas: data.modulo_aulas,
			modulo_aulas_aba_turmas: data.modulo_aulas_aba_turmas,
			modulo_aulas_aba_saldo: data.modulo_aulas_aba_saldo,
			modulo_treinar: data.modulo_treinar,
			aluno_marcar_propria_aula: data.aluno_marcar_propria_aula,
			habilitar_crossfit: data.habilitar_crossfit,
			nao_exibir_numero_de_vezes_no_app: data.nao_exibir_numero_de_vezes_no_app,
			proibir_marcar_aula_parcela_vencida:
				data.proibir_marcar_aula_parcela_vencida,
			proibir_buscar_programa_parcela_vencida:
				data.proibir_buscar_programa_parcela_vencida,
			nome_aplicativo_para_envio_email: data.nome_aplicativo_para_envio_email,
			link_app_email: data.link_app_email,
			dias_mostrar_totem: data.dias_mostrar_totem,
			habilitar_fila_espera: data.habilitar_fila_espera,
			habilitar_ver_wod_todas_empresas_app:
				data.habilitar_ver_wod_todas_empresas_app,
		};
	}

	public static getConfiguracoesAvaliacao(
		data: ConfigTreinoPayload
	): ConfiguracoesAvaliacao {
		return {
			cfg_objetivos_anamnese: data.cfg_objetivos_anamnese,
			cfg_peso_altura_pa_fc: data.cfg_peso_altura_pa_fc,
			cfg_parq: data.cfg_parq,
			cfg_dobras_cutaneas: data.cfg_dobras_cutaneas,
			cfg_perimetria: data.cfg_perimetria,
			cfg_composicao_corporal: data.cfg_composicao_corporal,
			cfg_flexibilidade: data.cfg_flexibilidade,
			cfg_postura: data.cfg_postura,
			cfg_rml: data.cfg_rml,
			cfg_rml_opcoes: data.cfg_rml_opcoes,
			cfg_rml_separado: data.cfg_rml_separado,
			cfg_tipo_rml: data.cfg_tipo_rml,
			cfg_vo2max: data.cfg_vo2max,
			cfg_recomendacoes: data.cfg_recomendacoes,
			cfg_ventilometria: data.cfg_ventilometria,
			cfg_testes_campo: data.cfg_testes_campo,
			cfg_teste_bike: data.cfg_teste_bike,
			cfg_somatotipia: data.cfg_somatotipia,
			cfg_teste_queens: data.cfg_teste_queens,
			cfg_importacao_biosanny: data.cfg_importacao_biosanny,
			lancar_agendamento_proxima_avaliacao:
				data.lancar_agendamento_proxima_avaliacao,
			lancar_produto_avaliacao: data.lancar_produto_avaliacao,
			validar_produto_avaliacao: data.validar_produto_avaliacao,
			produto_avaliacao: data.produto_avaliacao,
			lancar_produto_avaliacao_data_vencimento:
				data.lancar_produto_avaliacao_data_vencimento,
			usar_pressao_sistolica_diastolica: data.usar_pressao_sistolica_diastolica,
			usar_ordem_dobras_cutaneas: data.usar_ordem_dobras_cutaneas,
			ordens_dobras: data.ordens_dobras,
			modelo_evolucao_fisica: data.modelo_evolucao_fisica,
			produtos: data.produtos,
			obrigar_campos_dobras_bioimpedancia:
				data.obrigar_campos_dobras_bioimpedancia,
		};
	}

	public static getConfiguracoesGerais(
		data: ConfigTreinoPayload
	): ConfiguracoesGerais {
		return {
			validar_agenda_aulacheia: data.validar_agenda_aulacheia,
			desativar_tela_aluno_treino: data.desativar_tela_aluno_treino,
			troca_nomenclatura_crossfit: data.troca_nomenclatura_crossfit,
			configuracoes_treino_replicar_rede_empresa:
				data.configuracoes_treino_replicar_rede_empresa,
			duracao_aluno_na_academia: data.duracao_aluno_na_academia,
		};
	}
	public static getConfigGestao(
		data: ConfigTreinoPayload
	): ConfiguracoesGestao {
		return {
			periodo_usado_bi: data.periodo_usado_bi,
			inativos_a_x_dias: data.inativos_a_x_dias,
			somente_aluno_contrato_desistente: data.somente_aluno_contrato_desistente,
		};
	}

	public static getConfigManutencao(
		data: ConfigTreinoPayload
	): ConfiguracoesManutencao {
		return {
			aplicativo_personalizado: data.aplicativo_personalizado,
			aplicativo_personalizado_nome: data.aplicativo_personalizado_nome,
			aplicativo_personalizado_url: data.aplicativo_personalizado_url,
		};
	}

	public static getConfigNotificacao(
		data: ConfigTreinoPayload
	): ConfiguracoesNotificacao {
		return {
			iniciou_treino: data.iniciou_treino,
			aumentou_carga: data.aumentou_carga,
			diminuiu_carga: data.diminuiu_carga,
			aluno_chegou: data.aluno_chegou,
			agenda: data.agenda,
			agendamento_confirmado: data.agendamento_confirmado,
			solicitar_renovacao: data.solicitar_renovacao,
			lembrar_aluno_compromisso: data.lembrar_aluno_compromisso,
			agendamento_alterado: data.agendamento_alterado,
			aluno_em_risco: data.aluno_em_risco,
			aluno_agendou: data.aluno_agendou,
			numero_dias_notificar_treino_vencido:
				data.numero_dias_notificar_treino_vencido,
			sms_notificacao: data.sms_notificacao,
		};
	}

	public static getConfigTreino(
		data: ConfigTreinoPayload
	): ConfiguracoesTreino {
		return {
			emitir_ficha_apos_vencimento_treino:
				data.emitir_ficha_apos_vencimento_treino,
			numero_impressao_ficha: data.numero_impressao_ficha,
			bloquear_impressao_ficha_apos_todas_execucoes:
				data.bloquear_impressao_ficha_apos_todas_execucoes,
			dias_antes_vencimento: data.dias_antes_vencimento,
			permitir_apenas_alunos_ativos: data.permitir_apenas_alunos_ativos,
			visualizar_mensagem_aviso: data.visualizar_mensagem_aviso,
			permitir_visualizar_wod: data.permitir_visualizar_wod,
			permitir_visualizar_cref: data.permitir_visualizar_cref,
			permitir_visualizar_par_q_10_perguntas:
				data.permitir_visualizar_par_q_10_perguntas,
			permitir_visualizar_lei_parq: data.permitir_visualizar_lei_parq,
			agrupamento_series_set: data.agrupamento_series_set,
		};
	}

	public initConfigIa(data: ConfiguracoesIa) {
		this.permitir_criar_treino_automatizado_ia =
			data.permitir_criar_treino_automatizado_ia;
		this.tempo_aprovacao_automatica = data.tempo_aprovacao_automatica;
		this.tempo_maximo_revisao = data.tempo_maximo_revisao;
		this.habilitar_obrigatoriedade_aprovacao_professor =
			data.habilitar_obrigatoriedade_aprovacao_professor;
		this.permitir_aluno_criar_treino_ia_app =
			data.permitir_aluno_criar_treino_ia_app;
	}

	public static getConfigIa(data: ConfigTreinoPayload): ConfiguracoesIa {
		return {
			permitir_criar_treino_automatizado_ia:
				data.permitir_criar_treino_automatizado_ia,
			tempo_aprovacao_automatica: data.tempo_aprovacao_automatica,
			tempo_maximo_revisao: data.tempo_maximo_revisao,
			habilitar_obrigatoriedade_aprovacao_professor:
				data.habilitar_obrigatoriedade_aprovacao_professor,
			permitir_aluno_criar_treino_ia_app:
				data.permitir_aluno_criar_treino_ia_app,
		};
	}
}
