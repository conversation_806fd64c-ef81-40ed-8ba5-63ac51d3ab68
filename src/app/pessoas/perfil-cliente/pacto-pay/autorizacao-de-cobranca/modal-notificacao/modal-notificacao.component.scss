@import "dist/ui-kit/assets/import.scss";

.wrapper {
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	flex-direction: column;
	color: #43474b;
	min-height: 400px;
	padding: 1rem;
}

#type {
	font-size: 140px;
}

.pct-check {
	color: $verdinho05;
}

.pct-alert-triangle {
	color: $pequizaoPri;
}

.pct-x-circle {
	color: $hellboy04;
}

.title {
	color: #43474b;
	font-weight: bold;
	font-size: 24px;
	line-height: 16px;
	text-align: center;
}

.subtitle,
.body {
	font-weight: normal;
	font-size: 20px;
	line-height: 16px;
	text-align: center;
}

.actions {
	display: flex;
	justify-content: space-evenly;
	align-items: center;

	pacto-cat-button {
		min-width: 100px;
		margin-left: 0.5rem;
		margin-right: 0.5rem;
	}
}

:host ::ng-deep pacto-cat-button .pacto-button {
	text-transform: none;
}
