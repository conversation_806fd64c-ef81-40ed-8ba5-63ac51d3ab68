import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { LoginUrlQueries } from "@base-core/client/client-model";

import { SessionService } from "@base-core/client/session.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";

import { of } from "rxjs";
import { switchMap } from "rxjs/operators";
import {
	Funcionalidade,
	PerfilAcessoDetalheDTO,
	PerfilAcessoDetalheSdk,
	PerfilUsuarioAdm,
	SessionService as SessionServiceAdm,
} from "sdk";
import { PlataformaModulo } from "src/app/microservices/client-discovery/client-discovery.model";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
} from "treino-api";
import { TreinoConfigCacheService } from "../../configuracoes/configuration.service";

@Component({
	selector: "pacto-add-account-controller",
	templateUrl: "./add-account-controller.component.html",
	styleUrls: ["./add-account-controller.component.scss"],
})
export class AddAccountControllerComponent implements OnInit {
	loading = true;
	fail = false;

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private localStorageSession: LocalStorageSessionService,
		private congifiguracoesService: TreinoConfigCacheService,
		private sessionService: SessionService,
		private sessionServiceAdm: SessionServiceAdm
	) {}

	ngOnInit() {
		this.localStorageSession.clear();
		const query: LoginUrlQueries = this.route.snapshot
			.queryParams as LoginUrlQueries;
		this.localStorageSession.setLocalStorageParams(query);

		if (query.usuarioOamd) {
			this.sessionService.usuarioOamd = query.usuarioOamd;
		}

		this.sessionService
			.loadSession(query)
			.pipe(
				switchMap((success) => {
					if (success) {
						this.sessionService.montarChatMovDesk();
						this.sessionService.montarPactoPay();
						if (!this.sessionService.isTreinoIndependente()) {
							this.loadSessionAdm();
						}
						if (
							this.sessionService.modulosHabilitados.includes(
								PlataformaModulo.TR
							) ||
							this.sessionService.modulosHabilitados.includes(
								PlataformaModulo.NTR
							)
						) {
							return this.congifiguracoesService.loadTreinoConfigCache();
						} else {
							return of(true);
						}
					} else {
						return of(false);
					}
				})
			)
			.subscribe((success) => {
				this.loading = false;
				this.fail = !success;
				if (success) {
					if (query.matricula) {
						this.router.navigateByUrl(
							`/cadastros/alunos/perfil/${query.matricula}%3Forigem%3Dbi`
						);
					} else if (query.redirect) {
						this.router.navigateByUrl(query.redirect);
					} else {
						this.redirectToModule(query.moduleId as any);
					}
				}
			});
	}

	private redirectToModule(target?: PlataformaModulo) {
		const finalTarget = this.getTargetModule(target);
		if (finalTarget === PlataformaModulo.NTR) {
			this.router.navigate(["treino"]);
		} else if (finalTarget === PlataformaModulo.NCR) {
			this.router.navigate(["cross"]);
		} else if (finalTarget === PlataformaModulo.NAV) {
			this.router.navigate(["avaliacao"]);
		} else if (finalTarget === PlataformaModulo.GRD) {
			this.router.navigate(["graduacao"]);
		} else if (finalTarget === PlataformaModulo.NZW) {
			this.router.navigate(["adm"]);
		} else if (
			finalTarget === PlataformaModulo.AGENDA ||
			finalTarget === PlataformaModulo.AGE ||
			finalTarget === PlataformaModulo.AGN
		) {
			this.router.navigate(["agenda"]);
		} else if (finalTarget === PlataformaModulo.PAY) {
			this.router.navigate(["pactopay"]);
		} else {
			this.router.navigate(["adm"]);
		}
	}

	private getTargetModule(target?: PlataformaModulo): PlataformaModulo {
		const habilitados = this.sessionService.modulosHabilitados;
		if (target && habilitados.includes(target)) {
			return target;
		} else if (
			habilitados.includes(PlataformaModulo.NTR) &&
			(target === PlataformaModulo.AGENDA ||
				target === PlataformaModulo.AGN ||
				target === PlataformaModulo.AGE)
		) {
			return target;
		} else {
			return null;
		}
	}

	private loadSessionAdm() {
		this.sessionService.loggedUser.nome =
			this.sessionService.perfilUsuarioAdm.user.nome;
		this.sessionServiceAdm.loggedUser =
			this.sessionService.perfilUsuarioAdm.user;
		this.sessionServiceAdm.usuarioOamd = this.sessionService.usuarioOamd;
		this.sessionServiceAdm.token = this.sessionService.apiToken;
		this.sessionServiceAdm.modulosHabilitados =
			this.sessionService.modulosHabilitados;
		this.sessionServiceAdm.empresas = this.sessionService.empresas;
		this.sessionServiceAdm.financeiroEmpresas =
			this.sessionService.financeiroEmpresas;
		this.sessionServiceAdm.empresaId = this.sessionService.empresaId;
		this.sessionServiceAdm.chave = this.sessionService.chave;
		this.sessionServiceAdm.colaborador = this.sessionService.colaborador;
		this.sessionServiceAdm.codUsuarioZW = this.sessionService.codUsuarioZW;
		this.sessionServiceAdm.empresasAcesso = this.sessionService.empresasAcesso;

		const perfilAcessoDTO: PerfilAcessoDetalheDTO = {
			id: this.sessionService.perfilUsuario.id,
			nome: this.sessionService.perfilUsuario.nome,
			recursos: this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos,
			funcionalidades:
				this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades,
		};

		this.sessionServiceAdm.perfilUsuario = new PerfilAcessoDetalheSdk(
			perfilAcessoDTO
		);
		const perfilUsuarioAdm: PerfilUsuarioAdm = {
			nome: this.sessionService.perfilUsuario.nome,
			funcionalidades:
				this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades,
			recursos: this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos,
		};
		this.sessionServiceAdm.perfilUsuarioAdm = {
			perfilUsuario: perfilUsuarioAdm,
			unidadesEmpresa: this.sessionService.perfilUsuarioAdm.unidadesEmpresa,
			user: this.sessionService.perfilUsuarioAdm.user,
		};
		this.sessionServiceAdm.perfilUsuarioTreino =
			this.sessionService.perfilUsuarioTreino;
		this.sessionServiceAdm.goBackPage = this.sessionService.goBackPage;
		this.sessionServiceAdm.goBackModule = this.sessionService.goBackModule;
		this.sessionServiceAdm.zwJSId = this.sessionService.zwJSId;
		this.sessionServiceAdm.funcionalidadesInativas =
			this.sessionService.funcionalidadesInativas;
		this.sessionServiceAdm.funcionalidadesAtivas =
			this.sessionService.funcionalidadesAtivas;
	}
}
