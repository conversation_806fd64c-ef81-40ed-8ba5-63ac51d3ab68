import { Injectable } from "@angular/core";
import {
	<PERSON>ttpEvent,
	HttpHandler,
	HttpInterceptor,
	HttpRequest,
} from "@angular/common/http";

import { Observable } from "rxjs";
import { LoaderService } from "ui-kit";
import { finalize } from "rxjs/operators";

@Injectable()
export class LoaderInterceptor implements HttpInterceptor {
	constructor(private loaderService: LoaderService) {}

	intercept(
		request: HttpRequest<any>,
		next: <PERSON>ttp<PERSON>and<PERSON>
	): Observable<HttpEvent<any>> {
		this.loaderService.show();

		return next.handle(request).pipe(finalize(() => this.loaderService.hide()));
	}
}
