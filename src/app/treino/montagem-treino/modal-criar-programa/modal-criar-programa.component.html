<div *ngIf="!this.configForcarCriarNovoPrograma">
	<div class="modal-body">
		<div class="sub-titulo-modal">
			<span *ngIf="programasPreDefinidos.length === 0">
				Não existem programas predefinidos.
			</span>
			<span
				*ngIf="programasPreDefinidos.length > 0 && !alunoPossuiMaisProgramas">
				Escolha um programa predefinido ou crie um novo.
			</span>
			<span
				*ngIf="programasPreDefinidos.length > 0 && alunoPossuiMaisProgramas"
				i18n="@@perfil-aluno-dados-pessoal:info:programa:descricao">
				Escolha um programa predefinido, renove o atual ou crie um novo.
			</span>
		</div>
		<div class="search">
			<div>
				<input
					(keyup)="inputSearchPrograma()"
					[(ngModel)]="pesquisa"
					class="form-control"
					id="input-busca-rapida"
					placeholder="Pesquise um programa..."
					type="text" />
				<i class="pct pct-search"></i>
			</div>
		</div>
		<div
			*ngIf="programasPreDefinidos.length > 0"
			[maxHeight]="'160px'"
			class="block-fichas-predefinida"
			pactoCatSmoothScroll>
			<div
				(click)="
					criarProgramaHandler(
						programaPredefinido.id,
						programaPredefinido.chaveOrigem
					)
				"
				*ngFor="
					let programaPredefinido of programasPreDefinidos | filter : pesquisa
				"
				[classToApply]="'font-search'"
				[content]="programaPredefinido.nome"
				[searchedWord]="pesquisa"
				[setTitle]="'true'"
				appHighlight
				class="row-items">
				<div class="descricao-ficha" title="{{ programaPredefinido?.nome }}">
					{{ programaPredefinido?.nome }}
				</div>
				<div class="atividades-ficha">
					{{
						programaPredefinido?.fichas?.length > 0
							? getFichasResumido(programaPredefinido)
							: "Não atribuido"
					}}
				</div>
			</div>
		</div>
	</div>

	<div class="modal-footer">
		<pacto-cat-button
			*ngIf="
				permitirCriarTreinoAutomatizadoIA && permissaoPrescricaoDeTreinoPorIa
			"
			(click)="treinoPorIA()"
			[icon]="'pct pct-plus'"
			[id]="'gerar-treino-por-ia'"
			[type]="'OUTLINE'"
			class="treinoIA"
			i18n-label="@@perfil-aluno-programa-cria:gerar-treino-ia"
			label="Gerar treino por IA"></pacto-cat-button>
		<pacto-cat-button
			(click)="criarProgramaHandler(null, null, true)"
			*ngIf="alunoPossuiMaisProgramas"
			[icon]="'pct pct-refresh-cw'"
			[id]="'button-renovar-atual-programa'"
			[label]="'Renovar atual'"
			[type]="'OUTLINE'"></pacto-cat-button>
		<pacto-cat-button
			(click)="criarProgramaHandler()"
			[icon]="'pct pct-plus-circle'"
			[id]="'button-criar-novo-programa'"
			[label]="'Criar um novo'"></pacto-cat-button>
	</div>
</div>

<div *ngIf="configForcarCriarNovoPrograma">
	<div class="modal-body">
		<h3 style="color: #9b9da3; text-align: center">Criando novo programa...</h3>
	</div>
</div>
<pacto-traducoes-xingling #traducoes>
	<span xingling="userSemPermission">
		Seu usuário não possui permissão, procure seu administrador
	</span>
	<span xingling="nomeProgramaVazio">
		O nome do programa atual deve estar preenchido, revise antes de prosseguir
	</span>
	<span xingling="dataInicioProgramaVazio">
		A data de início do programa atual deve estar preenchida, revise antes de
		prosseguir
	</span>
	<span xingling="dataTerminoProgramaVazio">
		A data de término previsto do programa atual deve estar preenchida, revise
		antes de prosseguir
	</span>
	<span xingling="diasPorSemanaProgramaVazio">
		A quantidade de dias por semana do programa atual não pode estar vazia,
		revise antes de prosseguir
	</span>
	<span xingling="dataInicioMaiorDataFim">
		As datas de início e término do programa atual devem estar preenchidas
		corretamente, revise antes de prosseguir
	</span>
	<span xingling="diasSemanaMaior7">
		O programa atual não pode possuir mais de 7 dias por semana para realizar a
		renovação, revise antes de prosseguir
	</span>
	<span xingling="diasSemanaVazio">
		Dias por Semana deve estar preenchido, revise antes de prosseguir
	</span>
</pacto-traducoes-xingling>
