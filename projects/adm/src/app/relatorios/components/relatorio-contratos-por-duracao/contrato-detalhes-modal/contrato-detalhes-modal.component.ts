import { Component, Input, OnInit } from "@angular/core";
import { ClientePorDuracao } from "../../../classes/relatorio-contrato-por-duracao/cliente-por-duracao.model";

@Component({
	selector: "adm-contrato-detalhes-modal",
	templateUrl: "./contrato-detalhes-modal.component.html",
	styleUrls: ["./contrato-detalhes-modal.component.scss"],
})
export class ContratoDetalhesModalComponent implements OnInit {
	@Input() clientePorContrato: ClientePorDuracao = new ClientePorDuracao();

	constructor() {}

	ngOnInit() {}
}
