import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable, Optional } from "@angular/core";
import { Observable } from "rxjs";
import { mergeMap } from "rxjs/operators";
import { AdmCoreApiModule } from "./adm-core-api.module";

import {
	AdmCoreApiConfig,
	AdmCoreApiConfigProviderBase,
} from "./adm-core-api-config-provider-base.service";

interface RequestOptions {
	[key: string]: any;

	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };
}

@Injectable({
	providedIn: AdmCoreApiModule,
})
export class AdmCoreApiBaseService {
	private constructor(
		private httpClient: HttpClient,
		@Optional() private apiConfigProvider: AdmCoreApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para AdmCoreApiConfigProviderBase"
			);
		}
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: AdmCoreApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		const mergedHeaders = {
			...headers,
			Authorization: `Bearer ${apiConfig.authToken}`,
			empresaId: apiConfig.empresaId,
			"Accept-Language": apiConfig.acceptLanguage,
		};

		if (options) {
			options.headers = mergedHeaders;
			return options;
		} else {
			return { headers: mergedHeaders };
		}
	}

	public get<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get<T>(fullUrl, mergedOptions);
			})
		);
	}

	public delete<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete<T>(fullUrl, mergedOptions);
			})
		);
	}

	public put<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	public post<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	private buildUrl(baseUrl: string, relativeUrl: string) {
		return `${baseUrl}/${relativeUrl}`;
	}
}
