import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { PessoaMsApiBase } from "./base/pessoa-ms-api-base.service";
import { PessoaMsApiModule } from "./pessoa-ms-api.module";

@Injectable({
	providedIn: PessoaMsApiModule,
})
export class CidadeService {
	constructor(private restService: PessoaMsApiBase) {}

	public getCidadeByEstado(
		estadoCod?: number,
		quicksearchValue?: string
	): Observable<any> {
		if (!estadoCod && !quicksearchValue) {
			console.warn(
				"o método getCidadeByEstado deve ter ao menos um do sistema de pesquisa estadoCod ou quicksearchValue"
			);
			return;
		}
		return this.restService.get(`cidade/${estadoCod || quicksearchValue}`).pipe(
			map((result: any) => {
				return result.content;
			})
		);
	}
}
