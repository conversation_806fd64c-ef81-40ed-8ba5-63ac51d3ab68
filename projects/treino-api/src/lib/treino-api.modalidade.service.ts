import { Injectable } from "@angular/core";
import { ModalidadeCreateEdit } from "./modalidade.model";
import { ApiResponseList, ApiResponseSingle } from "./base.model";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { TreinoApiModule } from "./treino-api.module";
import { TreinoApiBaseService } from "./treino-api-base.service";
import { Modalidade, ModalidadeCor } from "./aula.model";

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiModalidadeService {
	constructor(private restService: TreinoApiBaseService) {}

	obterModalidadeCores(): Observable<Array<ModalidadeCor>> {
		return this.restService.get(`modalidades/cores`).pipe(
			map((response: ApiResponseList<ModalidadeCor>) => {
				return response.content;
			})
		);
	}

	obterModalidade(id: number): Observable<Modalidade> {
		return this.restService.get(`modalidades/${id}`).pipe(
			map((result: any) => {
				return result.content;
			})
		);
	}

	criarModalidade(
		modalidade: ModalidadeCreateEdit
	): Observable<ModalidadeCreateEdit> {
		return this.restService.post(`modalidades`, modalidade).pipe(
			map((response: ApiResponseSingle<ModalidadeCreateEdit>) => {
				return response.content;
			})
		);
	}

	obterModalidades(filtro?: string): Observable<Array<Modalidade>> {
		const params: any = {};
		if (filtro) {
			params.filtroNome = filtro;
		}
		return this.restService.get(`modalidades`, { params }).pipe(
			map((response: ApiResponseList<Modalidade>) => {
				return response.content;
			})
		);
	}

	atualizarModalidade(
		dados: ModalidadeCreateEdit,
		id: string
	): Observable<Modalidade> {
		return this.restService.put(`modalidades/${id}`, dados).pipe(
			map((response: ApiResponseSingle<Modalidade>) => {
				return response.content;
			})
		);
	}

	removerModalidade(id: string): Observable<boolean> {
		return this.restService.delete(`modalidades/${id}`).pipe(
			map((response: boolean) => {
				return true;
			})
		);
	}

	obterTodasModalidades(): Observable<ApiResponseList<Modalidade>> {
		return this.restService.get(`modalidades/all`).pipe(
			map((response: ApiResponseList<Modalidade>) => {
				return response;
			})
		);
	}

	obterTodasModalidadesTurma(): Observable<ApiResponseList<Modalidade>> {
		return this.restService.get(`modalidades/all?turma=true`).pipe(
			map((response: ApiResponseList<Modalidade>) => {
				return response;
			})
		);
	}
}
