import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import {
	AlunoBase,
	AlunoTreinoDetalhes,
	ConfigsTreinoRede,
	DiasQueTreinouProgramaAtual,
	FichaDoDia,
	FichasRelacionadas,
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	Programa,
	ProgramaAtual,
	TreinoApiAlunosService,
	TreinoApiProgramaService,
} from "treino-api";
import { Observable, Subscription, zip } from "rxjs";
import { map, switchMap, tap } from "rxjs/operators";
import { SessionService } from "@base-core/client/session.service";
import {
	CatPieChartComponent,
	ColumnChartSet,
	PactoColor,
	PactoDataGridConfig,
	PieChartSet,
	RelatorioComponent,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { SnotifyService } from "ng-snotify";
import { ConfigurarFichaService } from "../../../../treino/montagem-treino/configurar-ficha.service";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { ModalCriarProgramaComponent } from "../../../../treino/montagem-treino/modal-criar-programa/modal-criar-programa.component";
import { ResultCriarPrograma } from "../../../../base/alunos/perfil-aluno/components/treinamento/treinamento.component";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { ModalEdicaoProgramaComponent } from "../../../../treino/montagem-treino/modal-edicao-programa/modal-edicao-programa.component";
import { HorariosQueTreinouProgramaAtual } from "../../../../../../projects/treino-api/src/lib/programa.model";
import { TreinoStateService } from "../../../../treino/montagem-treino/lista-prescricao-treino/treino-state.service";
import { TreinoBiStateService } from "../../../../treino/treino-bi/components/treino-bi-home-v2/treino-bi-state.service";
import { ClientDiscoveryService, PerfilRecursoPermissoTipo } from "sdk";
import { FormControl, FormGroup } from "@angular/forms";
import { ApiResponseList } from "@base-core/rest/rest.model";

declare var moment;

@Component({
	selector: "pacto-treino-container",
	templateUrl: "./treino-container.component.html",
	styleUrls: ["./treino-container.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreinoContainerComponent implements OnInit, OnDestroy {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("nomeColumnInicio", { static: true }) nomeColumnInicio;
	@ViewChild("nomeColumnTermino", { static: true }) nomeColumnTermino;
	@ViewChild("nomeColumnFichas", { static: true }) nomeColumnFichas;
	@ViewChild("nomeColumnCompleto", { static: true }) nomeColumnCompleto;

	@ViewChild("removerTitulo", { static: true }) removerTitulo;
	@ViewChild("removerBody", { static: true }) removerBody;
	@ViewChild("erroRemover", { static: true }) erroRemover;
	@ViewChild("sucessoRemover", { static: true }) sucessoRemover;
	@ViewChild("sucessoCreate", { static: true }) sucessoCreate;
	@ViewChild("messageErrorOlympia", { static: true }) messageErrorOlympia;
	@ViewChild("messageErrorConfigPrescricao", { static: true })
	messageErrorConfigPrescricao;

	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("realizado", { static: true }) realizado;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;

	matricula: string;
	atual: ProgramaAtual;
	fichaDia: FichaDoDia;
	treinoDetalhes: AlunoTreinoDetalhes;
	relacionadas: FichasRelacionadas;
	diasQueTreinouProgramaAtual: DiasQueTreinouProgramaAtual;
	horariosQueTreinouProgramaAtual: HorariosQueTreinouProgramaAtual;
	treinosExecutadosPeriodo = 0;
	permissaoProgramaTreino;
	permissaoAtribuirProgramaTreinoPreDefinido;
	chartInstance;
	chartColorsHorario: PactoColor[] = [
		PactoColor.PEQUIZAO_PRI,
		PactoColor.LARANJINHA,
		PactoColor.ACAI,
	];
	chartColors: PactoColor[] = [
		PactoColor.AZULIM_PRI,
		PactoColor.AZULIM_PRI,
		PactoColor.AZULIM_PRI,
		PactoColor.AZULIM_PRI,
		PactoColor.AZULIM_PRI,
		PactoColor.AZULIM_PRI,
		PactoColor.AZULIM_PRI,
	];
	dias: ColumnChartSet;
	pieHorario: PieChartSet[] = [
		{ name: "Manhã", data: 0 },
		{ name: "Tarde", data: 0 },
		{ name: "Noite", data: 0 },
	];
	table: PactoDataGridConfig;
	ready = false;
	temProgramas = false;
	temProgramaAtual = true;
	programaNome;
	aluno: AlunoBase;
	private programasPreDefinidos;
	porcentagemTreinoManha = 0;
	porcentagemTreinoTarde = 0;
	porcentagemTreinoNoite = 0;
	clienteMensagemAviso: string;
	showMensagemAviso = false;
	situacaoTreino: string;
	listaFichas: {
		nome: string;
		id: number;
		partes: {
			nome: string;
			id: string;
			qtdExercicios: number;
			porcentagemTreino: number;
			lado: "frontal" | "traseiro";
		}[];
	}[];
	listaAtiva;
	tooltipAtivo: {
		nome: string;
		id: string;
		qtdExercicios: number;
		porcentagemTreino: number;
		lado: "frontal" | "traseiro";
	};

	baseFilter = {
		filters: { quicksearchValue: "", quicksearchFields: ["nome"] },
		configs: {},
		page: 0,
		size: 10,
	};

	formGroup = new FormGroup({
		fichaTreino: new FormControl(""),
	});

	@ViewChild("pieChartHorarioRef", { static: true })
	pieChartHorarioRef: CatPieChartComponent;
	professorFC = new FormControl(null);
	podeAlterarProfessor = false;
	professorAtual: any;
	permissaoTrocarProfessorAluno;
	permissaoCliente2_29: any;
	private professorEditSubscription: Subscription;

	constructor(
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private localStorageService: LocalStorageSessionService,
		private programaService: TreinoApiProgramaService,
		private rest: RestService,
		private appModal: ModalService,
		private snotify: SnotifyService,
		private configurarFichaService: ConfigurarFichaService,
		private alunoService: TreinoApiAlunosService,
		private configurationService: TreinoConfigCacheService,
		private treinoConfigService: TreinoConfigCacheService,
		private router: Router,
		private state: TreinoBiStateService,
		private treinoState: TreinoStateService,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private readonly locale: string
	) {
		this.localStorageService.setLocalStorageItem("tabAluno", "treinos");
	}

	ngOnInit() {
		// ajuste para recarregar o componente sempre que houver alteração no parametro na url
		this.route.paramMap
			.pipe(
				switchMap((params: ParamMap) => {
					return this.verificarAlteracoesParam(params);
				})
			)
			.subscribe(() => {});
		this.formGroup.get("fichaTreino").valueChanges.subscribe((response) => {
			const lista = this.listaFichas.find((v) => v.id == response);
			if (lista) {
				this.listaAtiva = lista;
			} else {
				this.listaAtiva = [];
			}
		});
		this.setUpEvents();
		this.getFichaDeTreino();
		if (!this.permissaoTrocarProfessorAluno && !this.permiteEditarVinculo()) {
			this.professorFC.disable({ emitEvent: false });
		}
	}

	private loadBasicInfoTreino(alunoId: number): Observable<any> {
		return zip(this.alunoService.obterDetalhesTreinoAluno(alunoId)).pipe(
			tap((result) => {
				this.treinoDetalhes = result[0];
			})
		);
	}

	carregarProfessor() {
		if (this.aluno && this.aluno.professor) {
			this.professorFC.setValue(this.aluno.professor);
			this.professorAtual = this.professorFC.value.id;
		} else if (this.aluno && !this.aluno.professor) {
			this.professorFC.setValue(null);
			this.professorAtual = 0;
		}
	}

	ngOnDestroy() {
		if (this.professorEditSubscription) {
			this.professorEditSubscription.unsubscribe();
		}
	}

	verificarAlteracoesParam(params: ParamMap): Observable<any> {
		this.loadInit();
		return new Observable<any>();
	}

	loadInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		if (this.matricula) {
			this.loadAluno().subscribe();
			this.carregarPermissoes();
			this.configTable();
			this.ready = true;
			this.state.update$.subscribe((ready) => {
				if (ready) {
					this.getClienteMensagemAviso();
				}
			});
		}
	}

	private loadData(): Observable<any> {
		this.loadBasicInfoTreino(this.aluno.id).subscribe();
		const $atual = this.programaService.obterProgramaAtual(this.matricula);
		const $fichaDia = this.programaService.obterFichaDoDiaNaoVigente(
			this.matricula
		);
		const $fichasRelacionadas = this.programaService.obterFichasRelacionadas(
			this.matricula
		);
		const $diasQueTreinouProgramaAtual =
			this.programaService.obterDiasQueTreinouProgramaAtual(this.matricula);
		const $horariosQueTreinouProgramaAtual =
			this.programaService.obterHorariosQueTreinouProgramaAtual(this.matricula);
		const dataToLoad = [
			$atual,
			$fichaDia,
			$fichasRelacionadas,
			$diasQueTreinouProgramaAtual,
			$horariosQueTreinouProgramaAtual,
		];
		return zip(...dataToLoad).pipe(
			tap((result) => {
				this.atual = result[0];
				this.fichaDia = result[1];
				this.relacionadas = result[2];
				this.diasQueTreinouProgramaAtual = result[3];
				this.horariosQueTreinouProgramaAtual = result[4];
				this.temProgramaAtual = this.atual.id !== null;
				if (this.temProgramaAtual) {
					this.buildChart();
					this.buildDiasTreinou();
					this.buildHorariosTreinou();
					if (this.tableData && this.table) {
						this.tableData.reloadData();
					}
				}
				this.cd.detectChanges();
			})
		);
	}

	private loadAluno(): Observable<any> {
		if (this.matricula) {
			const $aluno = this.alunoService.obterAlunoCompletoPorMatricula(
				this.matricula
			);
			const dataToLoad = [$aluno];
			return zip(...dataToLoad).pipe(
				tap((result) => {
					this.aluno = result[0];
					this.temProgramas = this.aluno.programas.length === 0 ? false : true;
					this.carregarProfessor();
					this.loadData().subscribe();
					this.getClienteMensagemAviso();
					this.cd.detectChanges();
				})
			);
		}
	}

	private carregarPermissoes() {
		if (!this.sessionService.isTreinoIndependente()) {
			this.permissaoCliente2_29 =
				this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
					(r) => r.referenciaRecurso === "2.29"
				);
		}
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
		this.permissaoAtribuirProgramaTreinoPreDefinido =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.ATRIBUIR_PROGRAMA_TREINO_PRE_DEFINIDO
			);
		this.permissaoTrocarProfessorAluno =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.TROCAR_PROFESSOR
			);
		this.cd.detectChanges();
	}

	get configsRede(): ConfigsTreinoRede {
		let configsTreinoRede = this.sessionService.configsTreinoRede;
		if (!configsTreinoRede) {
			configsTreinoRede = {
				empresaLogadoIsFranqueadora: false,
				chaveFranqueadora: "",
				configTreinoFranqueadora: false,
				treinoPredefinidoFranqueadora: false,
			};
		}
		return configsTreinoRede;
	}

	criarProgramaHandler(titleModal = "Criar programa", situacaoTreino?: string) {
		if (
			this.permissaoProgramaTreino.incluir ||
			this.permissaoAtribuirProgramaTreinoPreDefinido
		) {
			if (this.validarAlunoOlympia()) {
				if (this.validarConfigPrescricaoTreino()) {
					this.programaService
						.obterProgramaPreDefinidosSlim(
							"",
							this.configsRede.treinoPredefinidoFranqueadora,
							this.configsRede.chaveFranqueadora
						)
						.pipe(
							map((result) => {
								this.programasPreDefinidos = result;
							})
						)
						.subscribe(() => {
							const modal = this.appModal.open(
								titleModal,
								ModalCriarProgramaComponent
							);
							const cadastroPrograma: ModalCriarProgramaComponent =
								modal.componentInstance;
							cadastroPrograma.programasPreDefinidos = this
								.programasPreDefinidos
								? this.programasPreDefinidos
								: [];
							cadastroPrograma.aluno = this.aluno;
							cadastroPrograma.situacaoTreino = situacaoTreino;

							if (this.validarConfigForcarCriarNovoPrograma()) {
								cadastroPrograma.ngOnInit();
								cadastroPrograma.criarProgramaHandler();
							}

							modal.result.then((value: ResultCriarPrograma) => {
								this.aluno.programas.push(value.programa);
								const sucessoCreate = this.sucessoCreate
									? this.sucessoCreate.nativeElement.innerHTML
									: "Programa criado com sucesso.";
								this.snotify.success(sucessoCreate);
								this.sessionService.notificarRecursoEmpresa(
									RecursoSistema.CRIOU_PROGRAMA_NTO
								);
								this.loadAluno().subscribe();
								this.loadData().subscribe();
								this.treinoState.update$.next(value.programa);
								if (value.novo) {
									this.abrirModalEdicaoPrograma(value.programa);
								} else {
									this.abrirModalEdicaoFicha(value.programa);
								}
							});
						});
				}
			}
		} else {
			this.snotify.warning(this.notificacoesTranslate.getLabel("semPermissao"));
		}
	}

	private abrirModalEdicaoPrograma(programa: Programa) {
		const modalEditPrograma = this.appModal.open(
			"Editar programa",
			ModalEdicaoProgramaComponent,
			PactoModalSize.LARGE
		);
		modalEditPrograma.componentInstance.loadForm(programa);
		modalEditPrograma.componentInstance.apresentarBotaoPredefinir = false;
		modalEditPrograma.result.then((result: Programa) => {
			if (this.aluno.programas) {
				const indexProgramaDesatualizado = this.aluno.programas.findIndex(
					(programaDesatualizado) => programaDesatualizado.id === result.id
				);
				this.aluno.programas.splice(indexProgramaDesatualizado, 1);
				this.aluno.programas.push(result);
				this.ordenarProgramasPorDataInicio();
				this.fetchData();
			}
			this.abrirModalEdicaoFicha(result);
			this.treinoState.update$.next(result);
		});
	}

	private validarAlunoOlympia() {
		if (
			this.configurationService.configuracoesIntegracoes
				.integracao_sistema_olympia &&
			this.aluno.codigoExterno
		) {
			this.alunoService
				.validarCadastroOlympia(this.aluno.codigoExterno)
				.subscribe(
					(result) => {
						if (!result) {
							const messageBodyOlympia =
								this.messageErrorOlympia.nativeElement.innerHTML;
							this.snotify.error(messageBodyOlympia);
							return false;
						} else {
							return true;
						}
					},
					(mensagemError) => {
						this.snotify.error(
							"Mensagem retorno Olympia: " + mensagemError.error.meta.message
						);
					}
				);
		} else {
			return true;
		}
	}

	private validarConfigPrescricaoTreino() {
		if (
			this.configurationService &&
			this.configurationService.configuracoesTreino &&
			this.configurationService.configuracoesTreino
				.permitir_apenas_alunos_ativos &&
			this.aluno &&
			this.aluno.situacaoAluno &&
			(this.aluno.situacaoAluno === "VISITANTE" ||
				this.aluno.situacaoAluno === "INATIVO")
		) {
			const messageBodyConfigPrescricao =
				this.messageErrorConfigPrescricao.nativeElement.innerHTML;
			this.snotify.error(messageBodyConfigPrescricao);
			return false;
		} else {
			return true;
		}
	}

	private getOptions() {
		const value = this.atual.percentual;
		const label = `${this.atual.realizadas}/${this.atual.previstas}`;
		const options = {
			chart: {
				height: 280,
				type: "radialBar",
			},
			plotOptions: {
				radialBar: {
					startAngle: -135,
					endAngle: 135,
					offsetY: -25,
					dataLabels: {
						value: {
							offsetY: -5,
							fontSize: "28px",
							fontFamily: "'Nunito Sans', sans-serif",
							color: "#2C343B",
							formatter: (val) => {
								return val + "%";
							},
						},
						name: {
							fontSize: "16px",
							fontFamily: "'Nunito Sans', sans-serif",
							color: "#b4b7bb",
							offsetY: 50,
						},
					},
					track: {
						background: "#dadada",
					},
				},
			},
			fill: {
				colors: ["#1998FC"],
				type: "solid",
			},
			stroke: {
				dashArray: 5,
				width: 2,
			},
			series: [value],
			labels: [label, "aasas"],
		};
		return options;
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
		this.chartInstance = new ApexCharts(
			document.querySelector(`#grafico-andamento`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private buildDiasTreinou() {
		this.treinosExecutadosPeriodo =
			this.diasQueTreinouProgramaAtual.treinosExecutadosPeriodo;
		const dados = [
			Math.round(this.diasQueTreinouProgramaAtual.domingo),
			Math.round(this.diasQueTreinouProgramaAtual.segunda),
			Math.round(this.diasQueTreinouProgramaAtual.terca),
			Math.round(this.diasQueTreinouProgramaAtual.quarta),
			Math.round(this.diasQueTreinouProgramaAtual.quinta),
			Math.round(this.diasQueTreinouProgramaAtual.sexta),
			Math.round(this.diasQueTreinouProgramaAtual.sabado),
		];
		this.dias = {
			name: "",
			data: dados,
		};
		const indexOfMaxValue = dados.reduce(
			(iMax, x, i, arr) => (x > arr[iMax] ? i : iMax),
			0
		);
		this.chartColors[indexOfMaxValue] = PactoColor.HELLBOY_PRI;
	}

	private buildHorariosTreinou() {
		this.pieHorario = [
			{ name: "Manhã", data: this.horariosQueTreinouProgramaAtual.manha },
			{ name: "Tarde", data: this.horariosQueTreinouProgramaAtual.tarde },
			{ name: "Noite", data: this.horariosQueTreinouProgramaAtual.noite },
		];

		const getPercentage = (val, total) => {
			if (val === 0) {
				return 0;
			}

			return Math.round((val / total) * 100);
		};

		if (this.horariosQueTreinouProgramaAtual) {
			const total =
				this.horariosQueTreinouProgramaAtual.manha +
				this.horariosQueTreinouProgramaAtual.tarde +
				this.horariosQueTreinouProgramaAtual.noite;

			this.porcentagemTreinoManha = getPercentage(
				this.horariosQueTreinouProgramaAtual.manha,
				total
			);
			this.porcentagemTreinoTarde = getPercentage(
				this.horariosQueTreinouProgramaAtual.tarde,
				total
			);
			this.porcentagemTreinoNoite = getPercentage(
				this.horariosQueTreinouProgramaAtual.noite,
				total
			);
		}
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			rowClick: this.permissaoProgramaTreino.editar,
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "inicio",
					titulo: this.nomeColumnInicio,
					visible: true,
					ordenavel: true,
					date: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "termino",
					titulo: this.nomeColumnTermino,
					visible: true,
					ordenavel: true,
					date: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "qtFichas",
					titulo: this.nomeColumnFichas,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "porcentagemCompleto",
					titulo: this.nomeColumnCompleto,
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.permissaoProgramaTreino.excluir,
				},
			],
		});
		this.table.endpointUrl = this.rest.buildFullUrl(
			`alunos/obter-programas-aluno/${this.matricula}`
		);
		this.cd.detectChanges();
	}

	removerProgramaHandler(programa) {
		this.programaNome = programa.nome;

		setTimeout(() => {
			const title = this.removerTitulo.nativeElement.innerHTML;
			const body = this.removerBody.nativeElement.innerHTML;
			const mensagemError = this.erroRemover.nativeElement.innerHTML;
			const sucesso = this.sucessoRemover.nativeElement.innerHTML;

			const appModal = this.appModal.confirm(title, body);
			appModal.result
				.then(() => {
					this.programaService
						.removerPrograma(programa.id)
						.subscribe((result) => {
							if (result === "registro_esta_sendo_usado") {
								this.snotify.error(mensagemError);
							} else {
								this.snotify.success(sucesso);
								this.fetchData();
								this.recarregarProgramaAtual();
								if (this.aluno.programas) {
									const indexProgramaRemovido = this.aluno.programas.findIndex(
										(programaDesatualizado) => {
											return programaDesatualizado.id === programa.id;
										}
									);
									this.aluno.programas.splice(indexProgramaRemovido, 1);
								}
							}
						});
				})
				.catch(() => {});
		});
	}

	novaFichaTreino(): void {}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.removerProgramaHandler($event.row);
		}
	}

	public abrirModalPrograma(programaAtual: ProgramaAtual) {
		const program = {} as Programa;
		program.id = programaAtual.id.toString();
		this.abrirModalEdicaoFicha(program);
	}

	public carregarEdicaoFicha(codigoPrograma: number) {
		this.aluno = {};
		this.carregarPermissoes();
		const program = {} as Programa;
		program.id = codigoPrograma.toString();
		this.abrirModalEdicaoFicha(program);
	}

	public abrirModalEdicaoFicha(programa: Programa) {
		this.router.navigate([
			"treino",
			"cadastros",
			"programa",
			"aluno",
			programa.id,
		]);
	}

	recarregarProgramaAtual() {
		this.programaService
			.obterProgramaAtual(this.matricula)
			.subscribe((result) => {
				this.atual = result;
				this.temProgramaAtual = this.atual.id !== null;
				if (this.atual.id !== null) {
					this.recarregarDadosSecundariosProgramaAtual().subscribe();
				}
				this.cd.detectChanges();
			});
	}

	private recarregarDadosSecundariosProgramaAtual(): Observable<any> {
		const $fichaDia = this.programaService.obterFichaDoDia(this.matricula);
		const $fichasRelacionadas = this.programaService.obterFichasRelacionadas(
			this.matricula
		);
		const $diasQueTreinouProgramaAtual =
			this.programaService.obterDiasQueTreinouProgramaAtual(this.matricula);
		const $horariosQueTreinouProgramaAtual =
			this.programaService.obterHorariosQueTreinouProgramaAtual(this.matricula);
		const dataToLoad = [
			$fichaDia,
			$fichasRelacionadas,
			$diasQueTreinouProgramaAtual,
			$horariosQueTreinouProgramaAtual,
		];
		return zip(...dataToLoad).pipe(
			tap((result) => {
				this.fichaDia = result[0];
				this.relacionadas = result[1];
				this.diasQueTreinouProgramaAtual = result[2];
				this.horariosQueTreinouProgramaAtual = result[3];
				this.buildChart();
				this.buildDiasTreinou();
				this.buildHorariosTreinou();
				this.cd.detectChanges();
			})
		);
	}

	private fetchData() {
		if (this.tableData) {
			this.tableData.reloadData();
		}
	}

	private ordenarProgramasPorDataInicio() {
		this.aluno.programas.sort((a, b) => (a.inicio < b.inicio ? -1 : 1));
	}

	public callCriarPrograma() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.loadOnlyAluno().subscribe();
	}

	public callCriarProgramaMatricula(matricula, situacaoTreino) {
		this.matricula = matricula;
		this.situacaoTreino = situacaoTreino;
		this.loadOnlyAluno(situacaoTreino).subscribe();
	}

	private loadOnlyAluno(situacaoTreino?: string): Observable<any> {
		const $aluno = this.alunoService.obterAlunoCompletoPorMatricula(
			this.matricula
		);
		const dataToLoad = [$aluno];
		return zip(...dataToLoad).pipe(
			tap((result) => {
				this.aluno = result[0];
				this.cd.detectChanges();
				this.carregarPermissoes();
				if (situacaoTreino) {
					this.situacaoTreino = situacaoTreino;
				}
				this.criarProgramaHandler("Criar programa", situacaoTreino);
			})
		);
	}

	getClienteMensagemAviso() {
		this.alunoService
			.obterClienteMensagem(this.aluno.id, "AM")
			.subscribe((result) => {
				this.clienteMensagemAviso = result.toString().replace(/&nbsp;/g, " ");
				if (this.clienteMensagemAviso !== "") {
					this.validarConfigVisualizarMensagemAviso();
				} else {
					this.showMensagemAviso = false;
				}
				this.cd.detectChanges();
			});
	}

	validarConfigVisualizarMensagemAviso() {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino.visualizar_mensagem_aviso
		) {
			this.showMensagemAviso = true;
		} else {
			this.showMensagemAviso = false;
		}
	}

	validarConfigForcarCriarNovoPrograma(): boolean {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino.forcar_criar_novo_programa
		) {
			return true;
		} else {
			return false;
		}
	}

	criarProgramaColaborador(codigoColaborador) {
		this.aluno = {};
		this.matricula = null;
		this.carregarPermissoes();
		const titleModal = "Criar programa";
		if (
			this.permissaoProgramaTreino.incluir ||
			this.permissaoAtribuirProgramaTreinoPreDefinido
		) {
			this.programaService
				.obterProgramaPreDefinidosSlim(
					"",
					this.configsRede.treinoPredefinidoFranqueadora,
					this.configsRede.chaveFranqueadora
				)
				.pipe(
					map((result) => {
						this.programasPreDefinidos = result;
					})
				)
				.subscribe(() => {
					const modal = this.appModal.open(
						titleModal,
						ModalCriarProgramaComponent
					);
					const cadastroPrograma: ModalCriarProgramaComponent =
						modal.componentInstance;
					cadastroPrograma.programasPreDefinidos = this.programasPreDefinidos
						? this.programasPreDefinidos
						: [];
					cadastroPrograma.aluno = this.aluno;
					cadastroPrograma.codigoColaborador = codigoColaborador;
					modal.result.then((value: ResultCriarPrograma) => {
						const sucessoCreate = this.sucessoCreate
							? this.sucessoCreate.nativeElement.innerHTML
							: "Programa criado com sucesso.";
						this.snotify.success(sucessoCreate);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.CRIOU_PROGRAMA_NTO
						);
						this.loadData().subscribe();
						this.treinoState.update$.next(value.programa);
						if (value.novo) {
							this.abrirModalEdicaoPrograma(value.programa);
						} else {
							this.abrirModalEdicaoFicha(value.programa);
						}
					});
				});
		} else {
			this.snotify.warning(this.notificacoesTranslate.getLabel("semPermissao"));
		}
	}

	imprimirFichaTreino(programaId: number, fichaId: number) {
		const url = this.router.serializeUrl(
			this.router.createUrlTree(
				[
					"..",
					"treino",
					this.route.snapshot.paramMap.get("aluno-matricula"),
					"print",
					programaId,
					fichaId,
				],
				{
					relativeTo: this.route.parent,
				}
			)
		);

		const trueUrl = `${
			this.clientDiscoveryService.getUrlMap().treinoFrontUrl
		}/${this.locale}${url}`;
		window.open(trueUrl, "_blank");
	}

	getFichaDeTreino() {
		this.listaFichas = [
			{
				nome: "Treino Épico",
				id: 1,
				partes: [
					{
						nome: `Peitoral`,
						id: `peitoral`,
						qtdExercicios: 211,
						porcentagemTreino: 79,
						lado: "frontal",
					},
					{
						nome: `Bíceps`,
						id: `biceps`,
						qtdExercicios: 136,
						porcentagemTreino: 25,
						lado: "frontal",
					},
					{
						nome: `Pélvis`,
						id: `pelvis`,
						qtdExercicios: 166,
						porcentagemTreino: 46,
						lado: "frontal",
					},
					{
						nome: `Coxa Medial`,
						id: `coxaMedial`,
						qtdExercicios: 219,
						porcentagemTreino: 39,
						lado: "frontal",
					},
					{
						nome: `joelhoExt`,
						id: `joelhoExt`,
						qtdExercicios: 239,
						porcentagemTreino: 95,
						lado: "frontal",
					},
					{
						nome: `Joelho Interno`,
						id: `joelhoInt`,
						qtdExercicios: 141,
						porcentagemTreino: 1,
						lado: "frontal",
					},
					{
						nome: `Patela`,
						id: `patela`,
						qtdExercicios: 18,
						porcentagemTreino: 73,
						lado: "frontal",
					},
					{
						nome: `Abdominal`,
						id: `abdominal`,
						qtdExercicios: 162,
						porcentagemTreino: 92,
						lado: "frontal",
					},
					{
						nome: `escapular`,
						id: `escapular`,
						qtdExercicios: 12,
						porcentagemTreino: 77,
						lado: "frontal",
					},
					{
						nome: `ombros`,
						id: `ombros`,
						qtdExercicios: 230,
						porcentagemTreino: 69,
						lado: "frontal",
					},
					{
						nome: `pe`,
						id: `pe`,
						qtdExercicios: 202,
						porcentagemTreino: 27,
						lado: "frontal",
					},
					{
						nome: `panturrilha`,
						id: `panturrilha`,
						qtdExercicios: 1,
						porcentagemTreino: 7,
						lado: "frontal",
					},
					{
						nome: `triceps`,
						id: `triceps`,
						qtdExercicios: 31,
						porcentagemTreino: 69,
						lado: "frontal",
					},
					{
						nome: `antibraco`,
						id: `antibraco`,
						qtdExercicios: 50,
						porcentagemTreino: 81,
						lado: "frontal",
					},
					{
						nome: `costelas`,
						id: `costelas`,
						qtdExercicios: 30,
						porcentagemTreino: 43,
						lado: "frontal",
					},
					{
						nome: `supraIliaca`,
						id: `supraIliaca`,
						qtdExercicios: 155,
						porcentagemTreino: 35,
						lado: "frontal",
					},
					{
						nome: `axiliarMedia`,
						id: `axiliarMedia`,
						qtdExercicios: 8,
						porcentagemTreino: 36,
						lado: "frontal",
					},
					{
						nome: `mao`,
						id: `mao`,
						qtdExercicios: 33,
						porcentagemTreino: 1,
						lado: "frontal",
					},
					{
						nome: `pescoco`,
						id: `pescoco`,
						qtdExercicios: 44,
						porcentagemTreino: 86,
						lado: "frontal",
					},
					{
						nome: `antebraco`,
						id: `antebraco`,
						qtdExercicios: 119,
						porcentagemTreino: 25,
						lado: "frontal",
					},
					{
						nome: `maos`,
						id: `maos`,
						qtdExercicios: 40,
						porcentagemTreino: 96,
						lado: "frontal",
					},
					{
						nome: `dorsal`,
						id: `dorsal`,
						qtdExercicios: 220,
						porcentagemTreino: 70,
						lado: "frontal",
					},
					{
						nome: `nadegas`,
						id: `nadegas`,
						qtdExercicios: 96,
						porcentagemTreino: 66,
						lado: "frontal",
					},
					{
						nome: `lombar`,
						id: `lombar`,
						qtdExercicios: 50,
						porcentagemTreino: 14,
						lado: "frontal",
					},
					{
						nome: `ombro`,
						id: `ombro`,
						qtdExercicios: 3,
						porcentagemTreino: 22,
						lado: "traseiro",
					},
					{
						nome: `quadril`,
						id: `quadril`,
						qtdExercicios: 239,
						porcentagemTreino: 94,
						lado: "traseiro",
					},
					{
						nome: `coxas`,
						id: `coxas`,
						qtdExercicios: 50,
						porcentagemTreino: 84,
						lado: "traseiro",
					},
					{
						nome: `triceps`,
						id: `triceps`,
						qtdExercicios: 111,
						porcentagemTreino: 19,
						lado: "traseiro",
					},
					{
						nome: `panturrilha`,
						id: `panturrilha`,
						qtdExercicios: 146,
						porcentagemTreino: 25,
						lado: "traseiro",
					},
					{
						nome: `costas`,
						id: `costas`,
						qtdExercicios: 181,
						porcentagemTreino: 86,
						lado: "traseiro",
					},
					{
						nome: `subEscapular`,
						id: `subEscapular`,
						qtdExercicios: 4,
						porcentagemTreino: 4,
						lado: "traseiro",
					},
				],
			},
		];
	}

	paintDobras(nomeParte) {
		if (this.listaAtiva) {
			let porcentagemParte = this.listaAtiva.partes.find(
				(p) => p.id == nomeParte
			).porcentagemTreino;
			if (0 < porcentagemParte && porcentagemParte < 20) {
				return "#8FACEF";
			} else if (20 < porcentagemParte && porcentagemParte < 40) {
				return "#638BE9";
			} else if (40 < porcentagemParte && porcentagemParte < 60) {
				return "#366AE2";
			} else if (60 < porcentagemParte && porcentagemParte < 80) {
				return "#1D50C9";
			} else if (80 < porcentagemParte && porcentagemParte < 100) {
				return "#163E9C";
			} else {
				return "#BCBEC2";
			}
		} else {
			return "#BCBEC2";
		}
	}

	getPartes() {
		if (this.listaAtiva) {
			return this.listaAtiva.partes;
		} else {
			return [];
		}
	}

	stringToKebab(str: string) {
		if (str) {
			return str
				.match(
					/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
				)
				.map((x) => x.toLowerCase())
				.join("-");
		} else {
			return "";
		}
	}

	getSomaTreinos() {
		let total = 0;
		if (this.listaAtiva) {
			this.listaAtiva.partes.forEach((element) => {
				total += element.qtdExercicios;
			});
		}
		return total;
	}

	tooltipParte(idParte) {
		if (this.listaAtiva) {
			this.tooltipAtivo = this.listaAtiva.partes.find((p) => p.id == idParte);
		}
	}

	professorSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "200",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	get getUrlProfessorAluno() {
		if (
			this.professorFC &&
			this.professorFC.value &&
			this.professorFC.value.id != null
		) {
			return this.rest.buildFullUrl(
				"colaboradores/professores-dados-basicos/" + this.professorFC.value.id
			);
		} else {
			return this.rest.buildFullUrl(
				"colaboradores/professores-dados-basicos/0"
			);
		}
	}

	alterarProfessor() {
		this.podeAlterarProfessor = true;
	}

	private setUpEvents() {
		this.professorFC.valueChanges.subscribe(() => {
			setTimeout(() => {
				this.VerificarrProfessorAntesAlterar();
			}, 1000);
		});
	}

	VerificarrProfessorAntesAlterar() {
		if (
			this.professorFC.value &&
			this.professorAtual &&
			this.podeAlterarProfessor
		) {
			if (this.professorFC.value.id !== this.professorAtual) {
				this.professorUpdateHandler();
			}
		} else if (
			this.professorAtual === 0 &&
			this.professorFC.value != null &&
			this.podeAlterarProfessor
		) {
			this.professorUpdateHandler();
		} else if (this.professorAtual && this.professorFC.value === null) {
			this.professorUpdateHandler();
		}
		this.podeAlterarProfessor = false;
	}

	private professorUpdateHandler() {
		let prodId = 0;
		if (this.professorFC.value === null) {
			prodId = 0;
		} else {
			prodId = this.professorFC.value.id;
		}
		if (this.professorEditSubscription) {
			this.professorEditSubscription.unsubscribe();
		}
		this.professorEditSubscription = this.alunoService
			.editarProfessor(this.matricula, prodId)
			.subscribe(() => {
				this.snotify.success("Professor atualizado com sucesso.");
				this.professorAtual = this.professorFC.value.id;
			});
	}

	permiteEditarVinculo(): any {
		const permition = this.permissaoCliente2_29;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		return isPermited;
	}

	get ultimaFicha() {
		return this.treinoDetalhes
			? this.treinoDetalhes.ultimaFichaExecutada
			: null;
	}

	get fichaAtual() {
		return this.treinoDetalhes ? this.treinoDetalhes.fichaAtual : null;
	}

	get fichaProxima() {
		return this.treinoDetalhes ? this.treinoDetalhes.fichaProxima : null;
	}
}
