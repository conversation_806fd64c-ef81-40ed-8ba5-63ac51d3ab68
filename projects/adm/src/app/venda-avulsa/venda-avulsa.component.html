<div class="negociacao-contrato">
	<div class="negociacao-contrato-scroll" (scroll)="onScroll($event)">
		<adm-layout
			i18n-pageTitle="@@venda-avulsa:title"
			pageTitle="Venda avulsa"
			i18n-modulo="@@negociacao:modulo"
			modulo="Administrativo"
			(goBack)="voltarHome()"
			[recurso]="recurso"
			appStickyFooter>
			<pacto-cat-card-plain>
				<div class="table-wrapper pacto-shadow">
					<div class="info-top">
						<div>
							<span>Data de lançamento: {{ dataLancamento }}</span>
							<span>Data da primeira parcela: {{ dataPrimeiraParcela }}</span>
							<span>Número de parcelas: {{ nrParcelas }}</span>
						</div>

						<pacto-cat-button
							label="Editar"
							type="OUTLINE_NO_BORDER"
							(click)="abrirAutorizacaoEditarData()"></pacto-cat-button>
					</div>

					<div class="row">
						<div class="select col-sm-12 col-md-12 col-lg-12 col-xl-12">
							<pacto-cat-table-editable
								#tableProdutoComponent
								idSuffix="table-produto"
								[table]="tableProduto"
								[actionTitle]="'Ações'"
								[showAddRow]="false"
								[isEditable]="true"
								(edit)="editProduto($event)"
								(confirm)="confirmProduto($event)"
								(isEditingOrAddingItem)="isEditingOrAdding($event)"
								(delete)="removeProduto($event)"></pacto-cat-table-editable>
						</div>
					</div>
					<span class="add-row" (click)="novoProduto()">
						<i class="pct pct-plus-square"></i>
						Adicionar produto
					</span>

					<div
						*ngIf="descontosGerais && descontosGerais.length > 0"
						class="desconto-geral">
						<div>Desconto geral</div>

						<div>
							<span class="combo-desconto">
								<span>Selecione o desconto</span>
								<pacto-cat-form-select
									[disabled]="descontoSelecionado"
									[control]="descontoGeralForm"
									[items]="descontosGerais"
									[idKey]="'codigo'"
									[labelKey]="'descricao'"
									id="plano-novo-desconto-tipo-produto"></pacto-cat-form-select>
							</span>
							<pacto-cat-button
								*ngIf="!descontoSelecionado"
								type="OUTLINE"
								i18n-label="@@label-adicionar-desconto"
								label="Adicionar novo desconto"
								[disabled]="descontoNaoSelecionado"
								size="LARGE"
								(click)="aplicarDescontoGeral()"
								class="button"></pacto-cat-button>
							<i
								*ngIf="descontoSelecionado"
								(click)="limparDescontoGeral()"
								class="pct pct-trash-2"
								title="Excluir desconto"
								id="excluir-desconto"></i>
						</div>
					</div>

					<div #fluxo>
						<adm-venda-avulsa-info
							#info1
							(trocarCliente)="abrirEscolherCliente()"></adm-venda-avulsa-info>
					</div>

					<div class="botoes-negociacao">
						<div
							*ngIf="tipoCliente && valorFinal > 0.0"
							[ds3Tooltip]="
								linkPagamentoDesabilitado
									? 'Empresa não possui convênio padrão para link de pagamento'
									: false
							"
							tooltipPosition="top"
							style="display: inline-block">
							<pacto-cat-button
								id="btn-enviar"
								type="OUTLINE"
								i18n-label="@@label-confirmar-btn"
								label="Enviar link de pagamento"
								[disabled]="wait || linkPagamentoDesabilitado"
								size="LARGE"
								(click)="enviar()"
								class="button"></pacto-cat-button>
						</div>
						<div
							*ngIf="valorFinal > 0.0"
							[ds3Tooltip]="
								linkPagamentoDesabilitado
									? 'Empresa não possui convênio padrão para link de pagamento'
									: false
							"
							tooltipPosition="top"
							style="display: inline-block">
							<pacto-cat-button
								id="btn-caixa"
								type="OUTLINE"
								i18n-label="@@label-confirmar-btn"
								label="Deixar no caixa em aberto"
								[disabled]="wait || linkPagamentoDesabilitado"
								size="LARGE"
								(click)="vender()"
								class="button"></pacto-cat-button>
						</div>
						<div
							*ngIf="valorFinal > 0.0"
							[ds3Tooltip]="
								linkPagamentoDesabilitado
									? 'Empresa não possui convênio padrão para link de pagamento'
									: false
							"
							tooltipPosition="top"
							style="display: inline-block">
							<pacto-cat-button
								id="btn-receber"
								type="PRIMARY"
								i18n-label="@@label-confirmar-btn"
								label="Receber"
								[disabled]="wait || linkPagamentoDesabilitado"
								size="LARGE"
								(click)="receber()"></pacto-cat-button>
						</div>

						<pacto-cat-button
							*ngIf="valorFinal == 0.0"
							id="btn-concluir"
							type="PRIMARY"
							i18n-label="@@label-confirmar-btn"
							label="Concluir"
							[disabled]="wait"
							size="LARGE"
							(click)="concluir()"></pacto-cat-button>
					</div>
				</div>
			</pacto-cat-card-plain>
		</adm-layout>
	</div>
	<div class="fixed" #fixo>
		<adm-venda-avulsa-info #info2 (acao)="acao($event)"></adm-venda-avulsa-info>
	</div>
</div>
<ng-template #valorTitle>
	<span class="coluna-centro">Preço do produto</span>
</ng-template>

<ng-template #opcoesTitle>
	<span>Opções</span>
</ng-template>
<ng-template #opcoesColumn let-item="item">
	<span>
		<span
			*ngIf="item.tipoProduto === 'CP'"
			class="coluna-link"
			(click)="selecionarPacote(item)">
			Alterar pacote
		</span>
	</span>
</ng-template>
<ng-template #qtdColumn let-item="item">
	<span>
		<ds3-form-field *ngIf="!item.pacoteEscolhido && item.edit && item.qtdFC">
			<ds3-number-field
				ds3Input
				[min]="1"
				[formControl]="item.qtdFC"
				id="qtd-id-produto"></ds3-number-field>
		</ds3-form-field>
		<span *ngIf="item.pacoteEscolhido">
			{{ item.pacoteEscolhido.quantidade }}
		</span>
		<span *ngIf="!item.pacoteEscolhido && !item.edit">
			{{ item.quantidade.id }}
		</span>
	</span>
</ng-template>
<ng-template #valorColumn let-item="item">
	<span class="coluna-centro">
		{{ transformMoeda(item.valor) }}
	</span>
</ng-template>
<ng-template #valorFinalColumn let-item="item">
	<span>
		{{ transformMoeda(item.valorFinal) }}
	</span>
</ng-template>
<ng-template #pontosColumn let-item="item">
	<span class="coluna-centro">
		{{ item.pontos }}
	</span>
</ng-template>
<ng-template #descontoColumn let-item="item">
	<span
		class="descontos"
		*ngIf="
			!(
				item.descontoPadrao &&
				item.descontoPadrao.codigo &&
				item.descontoPadrao.codigo > 0
			) &&
			!item.descontoAutorizado &&
			!descontoSelecionado
		">
		<span>
			{{ transformMoeda(item.desconto) }}
		</span>
		<span class="edit-icon desbloquear" (click)="autorizarDesconto(item)">
			<i class="pct pct-key" title="Autorizar desconto manual"></i>
		</span>
	</span>

	<span
		class="descontos"
		*ngIf="
			(item.descontoPadrao &&
				item.descontoPadrao.codigo &&
				item.descontoPadrao.codigo > 0) ||
			descontoSelecionado
		">
		-
	</span>
	<span
		class="descontos"
		*ngIf="
			!(
				item.descontoPadrao &&
				item.descontoPadrao.codigo &&
				item.descontoPadrao.codigo > 0
			) &&
			item.descontoAutorizado &&
			!descontoSelecionado
		">
		<pacto-cat-form-input-number
			[formControl]="item.descontoFc"
			[decimalPrecision]="2"
			placeholder="00,00"
			decimal="true"></pacto-cat-form-input-number>

		<span
			class="edit-icon desbloquear"
			title="Aplicar desconto manual"
			(click)="aplicarDesconto(item)">
			<i class="pct pct-check"></i>
		</span>
	</span>
</ng-template>
