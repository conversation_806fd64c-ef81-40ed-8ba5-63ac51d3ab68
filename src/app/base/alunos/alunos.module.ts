import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { SnotifyModule } from "ng-snotify";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AlunoRoutingModule } from "./alunos.routing.module";
import { PerfilAlunoModule } from "./perfil-aluno/perfil-aluno.module";

import { AlunosListaComponent } from "./components/alunos-lista/alunos-lista.component";
import { AlunosCadastroComponent } from "./components/alunos-cadastro/alunos-cadastro.component";
import { AlunosListInsertTelefoneComponent } from "./components/alunos-list-insert-telefone/alunos-list-insert-telefone.component";
import { AlunoCardComponent } from "./components/aluno-card/aluno-card.component";
import { OlympiaModalComponent } from "./components/olympia-modal/olympia-modal.component";
import { AdicionarObservacaoModalComponent } from "./perfil-aluno/components/adicionar-observacao-modal/adicionar-observacao-modal.component";
import { AdiconarAnexoModalComponent } from "./perfil-aluno/components/adiconar-anexo-modal/adiconar-anexo-modal.component";
import { ReferenciaImcComponent } from "./perfil-aluno/components/avaliacoes/referencia-imc/referencia-imc.component";

@NgModule({
	imports: [
		BaseSharedModule,
		PerfilAlunoModule,
		SnotifyModule,
		RouterModule,
		AlunoRoutingModule,
		CommonModule,
	],
	declarations: [
		AlunosListaComponent,
		AlunosCadastroComponent,
		AlunosListInsertTelefoneComponent,
		AlunoCardComponent,
		OlympiaModalComponent,
		ReferenciaImcComponent,
	],
	exports: [ReferenciaImcComponent],
	entryComponents: [
		AdicionarObservacaoModalComponent,
		AdiconarAnexoModalComponent,
	],
})
export class AlunosModule {}
