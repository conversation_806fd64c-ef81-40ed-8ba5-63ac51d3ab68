import { ColaboradorSelect, ColaboradorTipoUsuario } from "./colaborador.model";
import { Perfil } from "./perfil-acesso.model";

export enum SituacaoUsuarioEnum {
	ATIVO = "Ativo",
	INATIVO = "Inativo",
}

export interface UsuarioColaboradorList {
	id: number;
	nome: string;
	appUserName: string;
}

export interface UsuarioColaborador {
	id?: number;
	colaborador?: ColaboradorSelect;
	empresas?: string;
	appUserName?: string;
	tipoUsuario?: ColaboradorTipoUsuario;
	perfilUsuarioPermissoes?: Perfil;
	situacao?: SituacaoUsuarioEnum;
	email?: string;
	emailVerificado?: boolean;
}

export interface UsuarioColaboradorCreateEdit {
	id?: number;
	colaboradorId: number;
	appUserName: string;
	appPassword: string;
	perfilUsuarioPermissoes: ColaboradorTipoUsuario;
	perfilUsuario: number;
	imagemData: string;
	extensaoImagem: string;
	situacao: SituacaoUsuarioEnum;
}

export enum UsuarioTipo {
	"Ativo" = 0,
	"Inativo" = 1,
	"Aguardando" = 3,
}

export interface Telefone {
	numero?: string;
	ddi?: string;
	tipotelefone?: string;
	descricao?: string;
}

export interface UsuarioBase {
	id?: any;
	codigo?: number;
	codigoColaborador?: number;
	username?: string;
	nome?: string;
	imageUri?: string;
	tipo?: UsuarioTipo;
	perfis?: Array<string>;
	tipoPerfis?: Array<string>;
	perfilUsuario?: PerfilUsuario;
	codigoPessoa?: number;
	telefone?: Telefone;
	email?: string;
	cpf?: string;
	professorResponse?: UsuarioBase;
	usuarioZw?: number;
	usuarioGeral?: string;
	permissaoFaturas?: boolean;
	pedirSenhaFuncionalidade?: boolean;
}

export interface PerfilUsuario {
	nome?: string;
	permissoes?: Array<PermissaoRecurso>;
}

export interface PermissaoRecurso {
	recurso?: string;
	tipoPermissoes?: Array<string>;
}
