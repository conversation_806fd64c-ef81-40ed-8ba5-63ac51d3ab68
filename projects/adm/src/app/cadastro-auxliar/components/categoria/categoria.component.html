<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@adm:moduleName"
	i18n-pageTitle="@@cad-aux:categoria:mainTitle"
	modulo="Administrativo"
	pageTitle="Categoria de clientes">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableData
			(btnAddClick)="novoCategoria()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editarCategoria($event)"
			[actionTitulo]="traducao.getLabel('table-column-acoes')"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="categoria"></pacto-relatorio>
	</div>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@cad-aux:categoria:edit" xingling="BTTN_EDIT">
		Editar categoria
	</span>
	<span i18n="@@cad-aux:categoria:delete" xingling="BTTN_DELETE">
		Excluir categoria
	</span>
	<span i18n="@@adm:action-editar" xingling="action-editar">Editar</span>
	<span i18n="@@adm:action-excluir" xingling="action-excluir">Excluir</span>
	<span i18n="@@cad-aux:categoria:permission" xingling="PERMISSION_MSG">
		Seu usuário não possui permissão, procure seu administrador.
	</span>
	<span i18n="@@cad-aux:categoria:deleteMsg" xingling="DELETE_MSG">
		Categoria excluída com sucesso.
	</span>
	<span
		i18n="@@ad-aux:categoria:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
	<span i18n="@@mensagem-sem-permissao" xingling="mensagem-sem-permissao">
		Seu usuário não possui permissão, procure seu administrador
	</span>

	<span i18n="@@categoria:naoSocio" xingling="NS">Não Sócio</span>
	<span i18n="@@categoria:aluno" xingling="AL">Aluno</span>
	<span i18n="@@categoria:socio" xingling="SO">Sócio</span>
	<span i18n="@@categoria:comerciario" xingling="CO">Comerciário</span>
	<span i18n="@@categoria:dependente" xingling="DE">Dependente</span>
	<span i18n="@@categoria:aluno" xingling="US">Usuário</span>
	<span i18n="@@categoria:eventos" xingling="EV">Eventos</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@cad-aux:categoria:tableColumnCodigo">Código</span>
</ng-template>

<ng-template #columnNome>
	<span i18n="@@cad-aux:categoria:tableColumnNome">Nome</span>
</ng-template>

<ng-template #columnTipoCategoria>
	<span i18n="@@cad-aux:categoria:tableColumnTipoCategoria">
		Tipo de Categoria
	</span>
</ng-template>
