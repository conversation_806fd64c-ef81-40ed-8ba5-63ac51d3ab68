<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('clientId'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-cliente-id-rewards"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-cliente-id-rewards"
				id="cliente-id-rewards"
				label="Cliente ID (Rewards)"
				placeholder="Digite o cliente ID (Rewards)"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('clientSecret'))"
				i18n-label="
					@@integracao-parceiro-fidelidade:label-cliente-secret-rewards"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-clinete-secret-rewards"
				id="cliente-secret-rewards"
				label="Cliente Secret (Rewards)"
				placeholder="Digite o cliente secret (Rewards)"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('clientIdRedemption'))"
				i18n-label="
					@@integracao-parceiro-fidelidade:label-cliente-id-redemption"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-cliente-id-redemption"
				id="cliente-id-redemption"
				label="Cliente ID (Redemption)"
				placeholder="Digite o cliente ID (Redemption)"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('clientSecretRedemption'))"
				i18n-label="
					@@integracao-parceiro-fidelidade:label-cliente-secret-redemption"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-clinete-secret-redemption"
				id="cliente-secret-redemption"
				label="Cliente Secret (Redemption)"
				placeholder="Digite o cliente secret (Redemption)"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('codigoLoja'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-codigo-loja"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-codigo-loja"
				id="store-code"
				label="Código Loja (StoreCode)"
				placeholder="Digite o código loja (StoreCode)"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('codigoMaquina'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-codigo-maquina"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-clinete-secret-redemption"
				id="device-code"
				label="Código Máquina (DeviceCode)"
				placeholder="Digite o código máquina (DeviceCode)"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('codigoOferta'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-codigo-ofeta"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-codigo-oferta"
				id="offer-code"
				label="Código Oferta (OfferCode)"
				placeholder="Digite o código oferta (OfferCode)"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('codigoResgate'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-codigo-resgate"
				i18n-placeholder="
					@@integracao-parceiro-fidelidade:placeholder-codigo-resgate"
				id="location-id"
				label="Código Resgate (LocationId)"
				placeholder="Digite o código resgate (LocationId)"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('tags'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-tags"
				i18n-placeholder="@@integracao-parceiro-fidelidade:placeholder-tags"
				id="tags"
				label="Tags"
				placeholder="Digite as Tags"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('cpf'))"
				i18n-label="@@integracao-parceiro-fidelidade:label-cpf"
				i18n-placeholder="@@integracao-parceiro-fidelidade:placeholder-cpf"
				id="cpf"
				label="CPF"
				placeholder="Digite o CPF"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row row-check-box">
		<div class="col-md-12 check-box">
			<pacto-cat-checkbox
				[control]="toFormControl(formGroup.get('ambienteProducao'))"
				id="checkbox-ambiente-producao"
				label="Ambiente de produção"></pacto-cat-checkbox>
		</div>
		<div class="col-md-12 check-box">
			<pacto-cat-checkbox
				[control]="toFormControl(formGroup.get('validarCliente'))"
				id="checkbox-gerar-pontos-cliente-dotz"
				label="Gerar pontos somente para cliente já cadastrado no DOTZ"></pacto-cat-checkbox>
		</div>
		<div class="col-md-12 check-box">
			<pacto-cat-checkbox
				[control]="toFormControl(formGroup.get('parcelaVencidaGeraPonto'))"
				id="checkbox-gerar-pontos-pg-recorrencia"
				label="Gerar pontos para pagamento automático(Recorrência) de parcela atrasada"></pacto-cat-checkbox>
		</div>
	</div>
	<div class="row row-table-dotz">
		<div class="col-md-3">
			<h3>Tabela Resgate DOTZ</h3>
		</div>
		<div>
			<!--            <pacto-cat-button
                    id="btn-consultar-produtos-dotz"
                    size="LARGE"
                    type="PRIMARY"
                    i18n-label="@@integracoes:btn-consultar-produtos-dotz"
                    label="Consultar produtos DOTZ"
                    (click)="consultarProdutosDotz()"
            >
            </pacto-cat-button>-->
		</div>
	</div>
	<div></div>
	<div class="line-1"></div>
	<div id="table-dotz">
		<pacto-relatorio
			#tableDataResgateDotzComponent
			(iconClick)="iconClickFn($event)"
			(pageChangeEvent)="pageChangeEvent($event)"
			(pageSizeChange)="pageSizeChange($event)"
			(sortEvent)="ordenarItensResgateDotz($event)"
			[actionTitulo]="traducao.getLabel('table-column-acoes')"
			[showShare]="false"
			[table]="dataGridTabelaResgateDotz"></pacto-relatorio>
	</div>
	<div class="row row-excluir-tabela-acumular-dotz">
		<div class="col-md-5 mr-auto title-dotz">
			<h3>Tabelas Acumular DOTZ</h3>
		</div>
		<div class="col-md-12">
			<div class="line-2"></div>
		</div>
	</div>
	<div class="row justify-content-center">
		<pacto-cat-button
			(click)="criarNovaTabelaAcumularDotz()"
			i18n-label="@@integracoes:btn-nova-tabela"
			id="btn-nova-tabela"
			label="Nova tabela"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>

	<div
		*ngFor="let tabela of integracao.configuracao.parceiroFidelidade.tabelas">
		<pacto-cat-accordion
			[id]="'table1'"
			[open]="false"
			title="{{
				tabela.nomeTabela + (tabela.defaultRecorrencia ? ' (Recorrência)' : '')
			}}">
			<accordion-body>
				<div>
					<pacto-table-acumular-dotz
						(dataTableChange)="atualizarDadosTabela()"
						(deletarTabelaEvent)="deletarTabelaAcumularDotz($event)"
						[tabelaParceiroFidelidade]="tabela"></pacto-table-acumular-dotz>
				</div>
			</accordion-body>
		</pacto-cat-accordion>
	</div>

	<div class="row justify-content-end btns-ativar-inativar">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-parceiro-fidelidade:label-btn-excluir-tabela"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px; margin-left: 17px"
			type="OUTLINE_GRAY"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@integracao-parceiro-fidelidade:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:falha-salvar-integracao"
		xingling="falha-salvar-integracao">
		Falha ao tentar salvar integração!
	</span>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:tooltip-excluir" xingling="tooltip-excluir">
		Excluir!
	</span>
</pacto-traducoes-xingling>
<ng-template #columnDescricao>
	<span i18n="@@integracao-parceiro-fidelidade:column-descricao">
		Descrição
	</span>
</ng-template>
<ng-template #columnSku>
	<span i18n="@@integracao-parceiro-fidelidade:column-sku">SKU</span>
</ng-template>
<ng-template #columnPontos>
	<span i18n="@@integracao-parceiro-fidelidade:column-pontos">Pontos</span>
</ng-template>
<ng-template #columnValor>
	<span i18n="@@integracao-parceiro-fidelidade:column-valor">Valor R$</span>
</ng-template>
