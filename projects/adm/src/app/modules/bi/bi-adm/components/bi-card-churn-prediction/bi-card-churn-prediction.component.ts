import {
	Component,
	EventEmitter,
	OnInit,
	Output,
	ChangeDetectionStrategy,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { SafeHtml } from "@angular/platform-browser";
import { BarCorLinha } from "ui-kit";
import { RelatorioChurnPredictionComponent } from "./modal/relatorio-churn-prediction.component";
import {
	Ds3SideFilterComponent,
	GridFilterConfig,
	GridFilterType,
} from "ui-kit";

import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";

@Component({
	selector: "adm-bi-card-churn-prediction",
	templateUrl: "./bi-card-churn-prediction.component.html",
	styleUrls: ["./bi-card-churn-prediction.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardChurnPredictionComponent implements OnInit {
	@Output() innerHtmlAjuda: EventEmitter<SafeHtml> = new EventEmitter();

	filtros = {
		unidade: "Pacto Matriz",
		data: new Date(),
		selecionados: ["Matrícula", "Rematrícula"],
	};
	config;

	JsonMock = {
		bars: [
			{
				percentual: 36.14,
				nome: "Zona de segurança",
				info: "60 clientes",
				cor: BarCorLinha.green,
			},
			{
				percentual: 22.89,
				nome: "Zona de atenção",
				info: "38 clientes",
				cor: BarCorLinha.orange,
			},
			{
				percentual: 13.86,
				nome: "Zona de risco",
				info: "23 clientes",
				cor: BarCorLinha.orange,
			},
			{
				percentual: 22.89,
				nome: "Zona crítica",
				info: "37 clientes",
				cor: BarCorLinha.red,
			},
			{
				percentual: 4.82,
				nome: "Zona de despedida",
				info: "08 clientes",
				cor: BarCorLinha.red,
			},
		],
	};

	barsList = [];

	constructor(
		public dialog: MatDialog,
		private biMdlAjudaService: BiMdlAjudaService
	) {}

	ngOnInit() {
		this.updateBarsList();
		this.loadFilters();
	}

	updateBarsList(): void {
		this.barsList = this.JsonMock.bars.map((bar) => ({
			etapas: [{ nome: bar.nome, mostraNome: false, corLinha: bar.cor }],
			porcentagem: bar.percentual,
			mostraNome: false,
			nome: bar.nome,
			info: bar.info,
			mostraPorcentagem: true,
			porcentagemComoInfo: false,
		}));
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Grupo de risco",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-grupo-de-risco-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openRelatorio(info: string) {
		console.log("Valor do info clicado:", info);

		const dialogRef = this.dialog.open(RelatorioChurnPredictionComponent, {
			width: "1000px",
			height: "616px",
			autoFocus: false,
			data: { codigo: info },
		});
	}

	openSideFilter() {
		const sideFilterRef = this.dialog.open(Ds3SideFilterComponent, {
			position: { right: "0px", top: "0px" },
			height: "100%",
			id: "side-filter-churn-prediction",
			disableClose: true,
			autoFocus: false,
			width: "30rem",
		});

		sideFilterRef.componentInstance.sidefilter = sideFilterRef;
		sideFilterRef.componentInstance.id = "bi-card-churn-prediction";
		sideFilterRef.componentInstance.config = this.config;
		sideFilterRef.componentInstance.values = this.filtros || {};
	}

	loadFilters() {
		this.config = {
			filters: [
				{
					label: "Grupo de colaboradores",
					name: "grupoColaborador",
					type: GridFilterType.DS3_SELECT_MANY,
					options: [
						{ value: 1, label: "Grupo 01" },
						{ value: 2, label: "Grupo 02" },
						{ value: 3, label: "Grupo 03" },
					],
					initialValue: 1,
				},
				{
					label: "Colaboradores",
					name: "colaboradores",
					type: GridFilterType.DS3_SELECT_MANY,
					options: [
						{ value: 1, label: "Colaborador 01" },
						{ value: 2, label: "Colaborador 02" },
						{ value: 3, label: "Colaborador 03" },
					],
					initialValue: 1,
				},
			],
		};
	}

	trackByIndex(index, item) {
		return index;
	}
}
