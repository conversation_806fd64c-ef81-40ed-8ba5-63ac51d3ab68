import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import {
	LoginAppApiConfig,
	LoginAppApiConfigProviderBase,
} from "login-app-api";
import { Observable, of } from "rxjs";
import { PactoLayoutSDKWrapper } from "../sdk-wrapper/sdk-wrappers";

@Injectable()
export class LoginAppApiConfigProviderService extends LoginAppApiConfigProviderBase {
	constructor(
		@Inject(LOCALE_ID) private readonly locale,
		private readonly pactoLayoutSDKWrapper: PactoLayoutSDKWrapper
	) {
		super();
	}

	getApiConfig(): Observable<LoginAppApiConfig> {
		const baseUrl = this.pactoLayoutSDKWrapper.serviceUrls().loginAppUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			acceptLanguage: this.locale,
		});
	}
}
