const toBoolean: (value: string | boolean) => boolean = (value) =>
	typeof value === "string" ? value === "true" : value;

const envBooleanValues = {
	enableCaptchaTemp: "false",
	newLoginEnabledTemp: "false",
};

export const environment = {
	production: true,
	discoveryMsUrl: "http://host.docker.internal:8101",
	enableCaptcha: toBoolean(envBooleanValues.enableCaptchaTemp),
	newLoginEnabled: toBoolean(envBooleanValues.newLoginEnabledTemp),
	coockieCredentials: "ncR33dL",
	cookieCredentialsPsw: "ntr3in0ck",
	cryptoKey: "SisTeP4ctoZwUi25", // Chave de 16 caracteres para AES
};
