import { NavModuleI18n } from "ui-kit";
import { relatorioMenuParentItem } from "../../../navigation/relatorios/relatorio-menu-parents-nav-config.service";
import {
	relatorioArmario,
	relatorioClientesVisitantes,
	relatorioContratosPorDuracao,
	relatorioConvidado,
	relatorioGympass,
	relatorioIndicadorAcesso,
	relatorioListaAcesso,
	relatorioProdutoComVigencia,
	relatorioSaldoCredito,
	relatorioTotalizadorAcesso,
	relatorioPersonal,
	relatorioCupomFiscal,
	relatorioClientesComRestricoes,
} from "../../../navigation/relatorios/relatorio-menu-nav-config.service";
import {
	relarioClientesAniversariantes,
	relatorioAgendamentos,
	relatorioBvs,
	relatorioCardex,
	relatorioCliente,
	relatorioClienteComCobrancaAutomaticaBloqueada,
	relatorioClientesCancelados,
	relatorioClientesComAtestado,
	relatorioClientesComBonus,
	relatorioClienteSimplificado,
	relatorioClientesSemSessao,
	relatorioClientesTrancados,
	relatorioComissao,
	relatorioComissaoConsultor,
	relatorioComissaoProfessor,
	relatorioCompetenciaMensal,
	relatorioConsolidadoParcelas,
	relatorioConsultaRecibos,
	relatorioConsultaTurma,
	relatorioControleLogs,
	relatorioDescontoOcupacao,
	relatorioDiario,
	relatorioFaturamento,
	relatorioFaturamentoRecebido,
	relatorioFechamentoAcesso,
	relatorioFechamentoCaixa,
	relatorioFrequenciaOcupacao,
	relatorioFrequenciaTurmas,
	relatorioGeralCliente,
	relatorioHistoricoPonto,
	relatorioListaChamada,
	relatorioMapaTurmas,
	relatorioMovimentoProduto,
	relatorioOrcamento,
	relatorioParcelas,
	relatorioPesquisa,
	relatorioPrevisaoRenovacao,
	relatorioReceitaPorPeriodo,
	relatorioRepasse,
	relatorioSaldoContaCorrente,
	relatorioTotalizadorTickets,
	relatorioTransacoesPix,
} from "../../../navigation/relatorios/relatorio-menu-nav-legado-config.service";

const relatorioGeralClientePt: NavModuleI18n = {
	[relatorioGeralCliente.id]: {
		name: "Geral de Clientes",
		description: "Relatório geral de clientes",
		searchTokens:
			"relatorio, relatório, clientes, geral, relatorio geral de clientes",
	},
};

const relatorioClientePt: NavModuleI18n = {
	[relatorioCliente.id]: {
		name: "Relatório de Clientes",
		description: "Relatório de Clientes",
		searchTokens: "relatorio,de,clientes",
	},
};

const relatorioClienteSimplificadoPt: NavModuleI18n = {
	[relatorioClienteSimplificado.id]: {
		name: "Cliente Simplificado",
		description: "Cliente Simplificado",
		searchTokens: "cliente, simplificado",
	},
};

const relatorioClientesVisitantesPt: NavModuleI18n = {
	[relatorioClientesVisitantes.id]: {
		name: "Visitantes",
		description: "Relatório de Visitantes",
		searchTokens: "relatorio, visitantes",
	},
};

const relatorioClientesCanceladosPt: NavModuleI18n = {
	[relatorioClientesCancelados.id]: {
		name: "Clientes Cancelados",
		description: "Relatório de clientes cancelados",
		searchTokens: "relatorio, clientes, cancelados",
	},
};

const relatorioClientesTrancadosPt: NavModuleI18n = {
	[relatorioClientesTrancados.id]: {
		name: "Clientes Trancados",
		description: "relatorio, clientes, trancados",
		searchTokens: "",
	},
};

const relatorioClientesComBonusPt: NavModuleI18n = {
	[relatorioClientesComBonus.id]: {
		name: "Clientes com Bônus",
		description: "Relatório de clientes com bônus",
		searchTokens: "relatorio, clientes, bonus",
	},
};

const relatorioClientesComAtestadoPt: NavModuleI18n = {
	[relatorioClientesComAtestado.id]: {
		name: "Clientes com Atestado",
		description: "Relatório de clientes com atestado",
		searchTokens: "relatorio, clientes, atestado",
	},
};

const relarioClientesAniversariantesPt: NavModuleI18n = {
	[relarioClientesAniversariantes.id]: {
		name: "Aniversariantes",
		description: "Relatório de aniversariantes",
		searchTokens:
			"relatorio, aviversariante, aniversariantes, aniversário, aniversário",
	},
};

const relatorioContratosPorDuracaoPt: NavModuleI18n = {
	[relatorioContratosPorDuracao.id]: {
		name: "Contratos por Duração",
		description: "Relatório de contratos por duração",
		searchTokens: "contrato, relatorio, duração",
	},
};

const relatorioGympassPt: NavModuleI18n = {
	[relatorioGympass.id]: {
		name: "Gympass",
		description: "Relatório de gympass",
		searchTokens: "relatorio, gympass",
	},
};

const relatorioSaldoCreditoPt: NavModuleI18n = {
	[relatorioSaldoCredito.id]: {
		name: "Saldo de Créditos",
		description: "Relatório de saldo de créditos",
		searchTokens: "relatorio, saldo, credito",
	},
};

const relatorioHistoricoPontoPt: NavModuleI18n = {
	[relatorioHistoricoPonto.id]: {
		name: "Histórico de Pontos",
		description: "Relatório de histórico de pontos",
		searchTokens: "relatorio, historico, pontos",
	},
};

const relatorioIndicadorAcessoPt: NavModuleI18n = {
	[relatorioIndicadorAcesso.id]: {
		name: "Indicador de Acesso",
		description: "Indicador de Acesso",
		searchTokens: "indicador, acesso",
	},
};

const relatorioOrcamentoPt: NavModuleI18n = {
	[relatorioOrcamento.id]: {
		name: "Orçamentos",
		description: "Relatório de orçamentos",
		searchTokens: "relatorio, orçamento",
	},
};

const relatorioConvidadoPt: NavModuleI18n = {
	[relatorioConvidado.id]: {
		name: "Convidados",
		description: "Relatório de convidados",
		searchTokens: "relatorio, convidados",
	},
};

const relatorioClienteComCobrancaAutomaticaBloqueadaPt: NavModuleI18n = {
	[relatorioClienteComCobrancaAutomaticaBloqueada.id]: {
		name: "Clientes com Cobrança Automática Bloqueada",
		description: "Relatório de clientes com cobrança automática bloqueada",
		searchTokens:
			"clientes, cobrança, automática, bloqueada, cobrança bloqueada",
	},
};

const relatorioFechamentoAcessoPt: NavModuleI18n = {
	[relatorioFechamentoAcesso.id]: {
		name: "Fechamento Acessos",
		description: "Relatório de fechamento de acessos",
		searchTokens: "relatorio, fechamento, acessos",
	},
};

const relatorioTotalizadorAcessoPt: NavModuleI18n = {
	[relatorioTotalizadorAcesso.id]: {
		name: "Totalizador de Acessos",
		description: "Totalizador de Acessos",
		searchTokens: "totalizador, acessos",
	},
};

const relatorioTotalizadorTicketsPt: NavModuleI18n = {
	[relatorioTotalizadorTickets.id]: {
		name: "Totalizador de Tickets",
		description: "Totalizador de tickets",
		searchTokens: "totalizador, tickets",
	},
};

const relatorioArmarioPt: NavModuleI18n = {
	[relatorioArmario.id]: {
		name: "Armários",
		description: "Relatório de armários",
		searchTokens: "relatorio, armario",
	},
};

const relatorioListaAcessoPt: NavModuleI18n = {
	[relatorioListaAcesso.id]: {
		name: "Lista de Acesso",
		description: "Lista de Acesso",
		searchTokens: "lista, acesso",
	},
};

const relatorioListaChamadaPt: NavModuleI18n = {
	[relatorioListaChamada.id]: {
		name: "Lista de Chamada",
		description: "Lista de chamada",
		searchTokens: "lista, chamada",
	},
};

const relatorioFrequenciaOcupacaoPt: NavModuleI18n = {
	[relatorioFrequenciaOcupacao.id]: {
		name: "Frequência e Ocupação",
		description: "Frequência e Ocupação",
		searchTokens: "frequência, ocupação",
	},
};

const relatorioFrequenciaTurmasPt: NavModuleI18n = {
	[relatorioFrequenciaTurmas.id]: {
		name: "Relatório de Frequência de Turmas",
		description: "Frequência",
		searchTokens:
			"frequência, frequencia, turmas, relatório, relatório de frequência de turmas",
	},
};

const relatorioDescontoOcupacaoPt: NavModuleI18n = {
	[relatorioDescontoOcupacao.id]: {
		name: "Desconto por Ocupação",
		description: "Desconto por Ocupação",
		searchTokens: "desconto, ocupação",
	},
};

const relatorioMapaTurmasPt: NavModuleI18n = {
	[relatorioMapaTurmas.id]: {
		name: "Mapa de Turmas",
		description: "Mapa de Turmas",
		searchTokens: "mapa, turma, turmas",
	},
};

const relatorioConsultaTurmaPt: NavModuleI18n = {
	[relatorioConsultaTurma.id]: {
		name: "Consulta de Turmas",
		description: "Consulta de Turmas",
		searchTokens: "consulta, turma, turmas",
	},
};

const relatorioFechamentoCaixaPt: NavModuleI18n = {
	[relatorioFechamentoCaixa.id]: {
		name: "Fechamento de Caixa",
		description: "Fechamento de caixa",
		searchTokens: "fechamento, caixa",
	},
};

const relatorioCompetenciaMensalPt: NavModuleI18n = {
	[relatorioCompetenciaMensal.id]: {
		name: "Competência Mensal",
		description: "Competência Mensal",
		searchTokens: "competência, mensal",
	},
};

const relatorioFaturamentoPt: NavModuleI18n = {
	[relatorioFaturamento.id]: {
		name: "Faturamento",
		description: "Relatório de faturamento",
		searchTokens: "relatorio, faturamento",
	},
};

const relatorioFaturamentoRecebidoPt: NavModuleI18n = {
	[relatorioFaturamentoRecebido.id]: {
		name: "Faturamento Recebido",
		description: "Faturamento Recebido",
		searchTokens: "faturamento, recebido",
	},
};

const relatorioReceitaPorPeriodoPt: NavModuleI18n = {
	[relatorioReceitaPorPeriodo.id]: {
		name: "Receita por Período",
		description: "Receita por período",
		searchTokens: "receita, período",
	},
};

const relatorioParcelasPt: NavModuleI18n = {
	[relatorioParcelas.id]: {
		name: "Parcelas",
		description: "Relatório de Parcelas",
		searchTokens: "relatorio, parcelas",
	},
};

const relatorioConsolidadoParcelasPt: NavModuleI18n = {
	[relatorioConsolidadoParcelas.id]: {
		name: "Consolidado de Parcelas",
		description: "Relatório Consolidado de Parcelas",
		searchTokens: "relatorio, consolidado, parcelas",
	},
};

const relatorioSaldoContaCorrentePt: NavModuleI18n = {
	[relatorioSaldoContaCorrente.id]: {
		name: "Saldo Conta Corrente",
		description: "Relatório de saldo de conta corrente",
		searchTokens: "relatorio, saldo, conta corrente, conta, corrente",
	},
};

const relatorioTransacoesPixPt: NavModuleI18n = {
	[relatorioTransacoesPix.id]: {
		name: "Transações PIX",
		description: "Relatório de Transações PIX",
		searchTokens: "relatório, transações, pix",
	},
};

const relatorioConsultaRecibosPt: NavModuleI18n = {
	[relatorioConsultaRecibos.id]: {
		name: "Consulta de Recibos",
		description: "Consulta de recibos",
		searchTokens: "consulta, recibos",
	},
};

const relatorioCupomFiscalPt: NavModuleI18n = {
	[relatorioCupomFiscal.id]: {
		name: "Cupom Fiscal",
		description: "Cupom Fiscal",
		searchTokens: "cupom, fiscal",
	},
};

const relatorioComissaoProfessorPt: NavModuleI18n = {
	[relatorioComissaoProfessor.id]: {
		name: "Comissão para Professor",
		description: "Comissão para professor",
		searchTokens: "comissão, professor",
	},
};

const relatorioComissaoConsultorPt: NavModuleI18n = {
	[relatorioComissaoConsultor.id]: {
		name: "Comissão para Consultor",
		description: "Comissão para consultor",
		searchTokens: "comissão, consultor",
	},
};

const relatorioPrevisaoRenovacaoPt: NavModuleI18n = {
	[relatorioPrevisaoRenovacao.id]: {
		name: "Previsão de Renovação",
		description: "Previsão de Renovação",
		searchTokens:
			"previsão, renovação, relatório de previsão de renovação por contrato",
	},
};

const relatorioBvsPt: NavModuleI18n = {
	[relatorioBvs.id]: {
		name: "BVs",
		description: "Relatório de BVs",
		searchTokens: "bv, bvs",
	},
};

const relatorioRepassePt: NavModuleI18n = {
	[relatorioRepasse.id]: {
		name: "Repasse",
		description: "Relatório de repasse",
		searchTokens: "repasse",
	},
};

const relatorioPesquisaPt: NavModuleI18n = {
	[relatorioPesquisa.id]: {
		name: "Pesquisas",
		description: "Relatório de Pesquisas",
		searchTokens: "pesquisas",
	},
};

const relatorioPersonalPt: NavModuleI18n = {
	[relatorioPersonal.id]: {
		name: "Personal",
		description: "Relatório de personal",
		searchTokens: "relatorio, personal",
	},
};

const relatorioProdutoComVigenciaPt: NavModuleI18n = {
	[relatorioProdutoComVigencia.id]: {
		name: "Produtos (com vigência)",
		description: "Relatório de Produto (com vigência)",
		searchTokens: "produto, com vigência, relatorio, vigência",
	},
};

const relatorioMovimentoProdutoPt: NavModuleI18n = {
	[relatorioMovimentoProduto.id]: {
		name: "Movimento de Produto",
		description: "Movimento de Produto",
		searchTokens: "movimento, produto",
	},
};

const relatorioCardexPt: NavModuleI18n = {
	[relatorioCardex.id]: {
		name: "Cardex",
		description: "Cardex",
		searchTokens: "cardex",
	},
};

const relatorioControleLogsPt: NavModuleI18n = {
	[relatorioControleLogs.id]: {
		name: "Controle de Logs",
		description: "Controle de Logs",
		searchTokens: "controle, logs",
	},
};

const relatorioComissaoPt: NavModuleI18n = {
	[relatorioComissao.id]: {
		name: "Comissão",
		description: "Comissão",
		searchTokens: "comissão",
	},
};

const relatorioDiarioPt: NavModuleI18n = {
	[relatorioDiario.id]: {
		name: "Diário",
		description: "Diário",
		searchTokens: "diario, diário",
	},
};

const relatorioAgendamentosPt: NavModuleI18n = {
	[relatorioAgendamentos.id]: {
		name: "Agendamentos",
		description: "Agendamentos",
		searchTokens: "agendamentos",
	},
};

const relatorioClientesSemSessaoPt: NavModuleI18n = {
	[relatorioClientesSemSessao.id]: {
		name: "Clientes sem Sessão",
		description: "Clientes sem Sessão",
		searchTokens: "clientes, sem sessão, sessão",
	},
};

const relatorioClientesComRestricoesPt: NavModuleI18n = {
	[relatorioClientesComRestricoes.id]: {
		name: "Clientes com restrições",
		description: "Clientes com restrições",
		searchTokens: "clientes, restrição, restrições",
	},
};

export const relatorioMenuPt: NavModuleI18n = {
	[relatorioMenuParentItem.id]: {
		name: "Relatórios",
		description: `Relatórios`,
		searchTokens: "relatorios, relatórios",
	},
	...relatorioAgendamentosPt,
	...relarioClientesAniversariantesPt,
	...relatorioArmarioPt,
	...relatorioBvsPt,
	...relatorioCardexPt,
	...relatorioClienteSimplificadoPt,
	...relatorioClientePt,
	...relatorioClientesCanceladosPt,
	...relatorioClientesTrancadosPt,
	...relatorioClientesComAtestadoPt,
	...relatorioClientesComBonusPt,
	...relatorioClienteComCobrancaAutomaticaBloqueadaPt,
	...relatorioClientesComRestricoesPt,
	...relatorioClientesSemSessaoPt,
	...relatorioComissaoPt,
	...relatorioComissaoConsultorPt,
	...relatorioComissaoProfessorPt,
	...relatorioCompetenciaMensalPt,
	...relatorioConsolidadoParcelasPt,
	...relatorioMapaTurmasPt,
	...relatorioConsultaTurmaPt,
	...relatorioConsultaRecibosPt,
	...relatorioContratosPorDuracaoPt,
	...relatorioControleLogsPt,
	...relatorioConvidadoPt,
	...relatorioCupomFiscalPt,
	...relatorioDescontoOcupacaoPt,
	...relatorioDiarioPt,
	...relatorioFaturamentoPt,
	...relatorioFaturamentoRecebidoPt,
	...relatorioFechamentoAcessoPt,
	...relatorioFechamentoCaixaPt,
	...relatorioFrequenciaOcupacaoPt,
	...relatorioFrequenciaTurmasPt,
	...relatorioGeralClientePt,
	...relatorioGympassPt,
	...relatorioHistoricoPontoPt,
	...relatorioIndicadorAcessoPt,
	...relatorioListaAcessoPt,
	...relatorioListaChamadaPt,
	...relatorioMapaTurmasPt,
	...relatorioMovimentoProdutoPt,
	...relatorioOrcamentoPt,
	...relatorioParcelasPt,
	...relatorioPersonalPt,
	...relatorioPesquisaPt,
	...relatorioPrevisaoRenovacaoPt,
	...relatorioProdutoComVigenciaPt,
	...relatorioReceitaPorPeriodoPt,
	...relatorioRepassePt,
	...relatorioSaldoContaCorrentePt,
	...relatorioSaldoCreditoPt,
	...relatorioTotalizadorAcessoPt,
	...relatorioTotalizadorTicketsPt,
	...relatorioTransacoesPixPt,
	...relatorioClientesVisitantesPt,
};
