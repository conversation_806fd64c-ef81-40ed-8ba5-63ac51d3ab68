import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	TreinoApiBiService,
	TreinoApiColaboradorService,
	UsuarioBase,
	TreinoApiBiCustomizadoService,
} from "treino-api";
import { FormControl } from "@angular/forms";
import { TreinoBiStateService } from "./treino-bi-state.service";

import { Observable, Subscription } from "rxjs";
import { map } from "rxjs/operators";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import {
	CatTabsTransparentComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalService } from "@base-core/modal/modal.service";
import { CustomViewModalComponent } from "../custom-view-modal/custom-view-modal.component";
import { SnotifyService } from "ng-snotify";
import { CustomBiPanelComponent } from "../custom-bi-panel/custom-bi-panel.component";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-treino-bi-home-v2",
	templateUrl: "./treino-bi-home-v2.component.html",
	styleUrls: ["./treino-bi-home-v2.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreinoBiHomeV2Component implements OnInit {
	@ViewChild("biTabs", { static: true }) biTabs: CatTabsTransparentComponent;
	@ViewChild(CustomBiPanelComponent, { static: false })
	biPanel: CustomBiPanelComponent;
	@ViewChild("xingling", { static: true }) xingling: TraducoesXinglingComponent;

	constructor(
		private modal: ModalService,
		private session: SessionService,
		private notify: SnotifyService,
		private treinoBiService: TreinoApiBiService,
		private treinoBiState: TreinoBiStateService,
		private colaboradorService: TreinoApiColaboradorService,
		private customBiService: TreinoApiBiCustomizadoService,
		private cd: ChangeDetectorRef,
		private router: Router
	) {}

	professorFiltroFC = new FormControl({ value: -1, disabled: true });
	professorData;
	biDataLoadSubscription: Subscription;
	professorSelected: UsuarioBase;
	professorIdSelected = 0;
	professorCodigoPessoa = -1;
	mostrarSelect = true;
	loading = false;
	public lastUpdate;

	ngOnInit() {
		this.loading = true;
		const funcionalidades = this.session.perfilUsuario.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.VER_BI_OUTROS_PROFESSORES
		);
		if (funcionalidades) {
			this.fetchProfessores().subscribe((professores) => {
				this.buildFilterOptions(professores);
				this.professorFiltroFC.enable({ emitEvent: false });
				this.cd.detectChanges();
			});
			this.setupEvents(this.professorIdSelected);
			this.professorFiltroFC.valueChanges.subscribe((professorId) => {
				this.loading = true;
				this.professorData = this.colaboradores.find(
					(v) => v.id == professorId
				);
				this.setupEvents(professorId);
				if (professorId === "-1") {
					this.professorCodigoPessoa = -1;
				}
			});
		} else {
			this.professorSelected = this.session.loggedUser;
			this.mostrarSelect = false;
			this.buildFilterOption(this.session.loggedUser.professorResponse);
			this.professorSelected = this.session.loggedUser.professorResponse;
			this.setupEvent(
				this.session.loggedUser.professorResponse.id,
				this.session.loggedUser.professorResponse.codigoPessoa
			);
		}
		this.session.notificarRecursoEmpresa(RecursoSistema.DASH_TREINO);

		const dashboard = this.router.url.match(/dashboard/);
		const personalizado = this.router.url.match(/personalizado/);
		if (dashboard && this.biTabs) {
			this.biTabs.tabIndex = 0;
		} else if (personalizado && this.biTabs) {
			this.biTabs.tabIndex = 1;
		}
	}

	get dash() {
		return this.biTabs.tabId === "dashboard";
	}

	get customBi() {
		return this.biTabs.tabId === "personalizado";
	}

	get colaboradores() {
		return this.treinoBiState.colaboradores;
	}

	private setupEvents(professorId) {
		if (this.biDataLoadSubscription) {
			this.biDataLoadSubscription.unsubscribe();
		}
		this.biDataLoadSubscription = this.treinoBiService
			.obterDashboard(professorId >= 0 ? professorId : 0, 0)
			.subscribe((result) => {
				const professor = professorId >= 0;
				if (professor) {
					this.professorSelected = this.treinoBiState.colaboradores.find(
						(item) => {
							return item.id === parseInt(professorId, 10);
						}
					);
					this.professorCodigoPessoa = this.professorSelected
						? this.professorSelected.codigoPessoa
						: -1;
				} else {
					this.professorSelected = null;
				}
				this.fetchDados(result);
			});
	}

	private setupEvent(professorId, pessoaId) {
		if (this.biDataLoadSubscription) {
			this.biDataLoadSubscription.unsubscribe();
		}
		this.biDataLoadSubscription = this.treinoBiService
			.obterDashboard(professorId >= 0 ? professorId : 0, pessoaId)
			.subscribe((result) => {
				this.fetchDados(result);
			});
	}

	public atualizarBi() {
		this.loading = true;
		if (this.mostrarSelect) {
			this.treinoBiService
				.gerarBI(this.professorFiltroFC.value, this.professorCodigoPessoa)
				.subscribe((result) => {
					this.fetchDados(result);
					this.loading = false;
					this.cd.detectChanges();
				});
		} else {
			this.professorSelected = this.session.loggedUser;
			this.mostrarSelect = false;
			this.buildFilterOption(this.session.loggedUser.professorResponse);
			this.professorSelected = this.session.loggedUser.professorResponse;
			this.treinoBiService
				.gerarBI(
					this.session.loggedUser.professorResponse.id,
					this.session.loggedUser.professorResponse.codigoPessoa
				)
				.subscribe((result) => {
					this.fetchDados(result);
					this.loading = false;
					this.cd.detectChanges();
				});
		}
	}

	criarViewHandler() {
		const modal = this.modal.open(
			"Adicionando Gráfico",
			CustomViewModalComponent
		);
		modal.result.then(
			(dto) => {
				this.customBiService.criarCustomView(dto).subscribe((newView) => {
					this.notify.success("Gráfico criado com sucesso.");
					this.biPanel.reloadDataAndSelectView(newView.id);
				});
			},
			() => {}
		);
	}

	public fetchDados(result) {
		this.lastUpdate = result.content.ultimaAtualizacao;
		this.treinoBiState.agenda = result.content.biAgenda;
		this.treinoBiState.treinamento = result.content.biTreinamento;
		this.treinoBiState.carteira = result.content.biCarteira;
		this.treinoBiState.avaliacaoTreino = result.content.biTreinoAvaliacaoTreino;
		this.treinoBiState.update$.next(true);
		this.loading = false;
		this.cd.detectChanges();
	}

	private fetchProfessores(): Observable<UsuarioBase[]> {
		return this.colaboradorService.obterProfessoresComVinculos().pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	private buildFilterOptions(colaboradores: UsuarioBase[]) {
		let result: UsuarioBase[] = [
			{ id: -1, nome: this.xingling.getLabel("todos") },
		];
		result = result.concat(colaboradores);
		this.treinoBiState.colaboradores = result;
	}

	private buildFilterOption(colaborador: UsuarioBase) {
		this.treinoBiState.colaboradores = [
			{ id: colaborador.id, nome: colaborador.nome },
		];
		this.professorFiltroFC = new FormControl({
			value: colaborador.id,
			disabled: true,
		});
		this.professorData = {
			id: this.session.loggedUser.professorResponse.id,
			username: this.session.loggedUser.professorResponse.username,
			nome: this.session.loggedUser.professorResponse.nome,
			codigoProfessorsintetico: this.session.loggedUser.professorResponse.id,
		};
		this.fetchProfessores().subscribe((professores) => {
			professores.forEach((aaa) => {
				if (aaa.codigoPessoa == this.session.loggedUser.professorResponse.codigoPessoa) {
					this.professorData = aaa;
				}
			});
		});
	}

	get getUsername() {
		return this.session.loggedUser && this.session.loggedUser.username;
	}
}
