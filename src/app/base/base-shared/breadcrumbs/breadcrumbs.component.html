<div class="breadcrumb-wrapper">
	<ng-container [ngSwitch]="breadcrumbConfig.categoryName">
		<span *ngSwitchCase="undefined" class="category-name">
			<ng-container
				*ngTemplateOutlet="
					breadCrumbName;
					context: { breadCrumbName: 'INICIO' }
				"></ng-container>
		</span>
		<span
			(click)="categoryClick()"
			*ngSwitchDefault
			[ngClass]="{ navigation: breadcrumbConfig.categoryLink }"
			class="category-name">
			<!-- <ng-container
				*ngTemplateOutlet="
					breadCrumbName;
					context: { breadCrumbName: breadcrumbConfig.categoryName }
				"></ng-container> -->
		</span>
	</ng-container>
	<div>
		<div
			[ngClass]="{ navigation: breadcrumbConfig.menuLink }"
			class="lower-menu">
			<span (click)="menuClick()" class="back">
				<i *ngIf="back" class="pct pct-arrow-left"></i>
				<ng-container
					*ngTemplateOutlet="
						breadCrumbName;
						context: { breadCrumbName: breadcrumbConfig.menu }
					"></ng-container>
			</span>
		</div>
	</div>
</div>

<ng-template #breadCrumbName let-breadCrumbName="breadCrumbName">
	<ng-container [ngSwitch]="breadCrumbName">
		<span *ngSwitchCase="'INICIO'" i18n>Início</span>
		<span *ngSwitchCase="'DASHBOARD'" i18n>Dashboard</span>
		<span *ngSwitchCase="'OPERACOES'" i18n>Operações</span>
		<span *ngSwitchCase="'RELATORIOS'" i18n>Relatórios</span>
		<span *ngSwitchCase="'AGENDA'" i18n>Agenda</span>
		<span *ngSwitchCase="'AVALIACAO_FISICA'" i18n>Avaliação Física</span>
		<span *ngSwitchCase="'CADASTRO'" i18n="@@navigation-breadcrumb:cadastros">
			Cadastros
		</span>
		<span
			*ngSwitchCase="'LISTA_ALUNOS'"
			i18n="@@perfil-aluno:breadcrumb:lista-alunos">
			Lista de alunos
		</span>
		<span
			*ngSwitchCase="'configuracoes'"
			i18n="@@configuration-sistema-root:configuracoes:breadcrumbs">
			Configurações
		</span>
		<span *ngSwitchCase="'HOME'" i18n="@@navigation-breadcrumb:home">Home</span>
		<span
			*ngSwitchCase="'TREINOS'"
			i18n="@@treino:montagem-treino:treinos:breadCrumbs">
			Treinos
		</span>
		<span *ngSwitchCase="'GESTAO_PERSONAL'" i18n>Gestão de Personal</span>
		<span *ngSwitchCase="'MUSCULO'" i18n="@@crud:musculos:breadcrumb">
			Músculos
		</span>
		<span
			*ngSwitchCase="'GRUPO_MUSCULAR'"
			i18n="@@crud:grupos-musculares:breadcrumb">
			Grupos Musculares
		</span>
		<span *ngSwitchCase="'CATEGORIA_ATIVIDADE'">Categoria de Atividades</span>
		<span *ngSwitchCase="'APARELHO'" i18n="@@crud:aparelhos:breadcrumb">
			Aparelho
		</span>
		<span *ngSwitchCase="'ALUNOS'" i18n="@@crud-alunos:breadcrumb">Alunos</span>
		<span *ngSwitchCase="'ANAMNESE'" i18n>Anamnese</span>
		<span *ngSwitchCase="'BENCHMARK'" i18n="@@crud:benchmark:breadcrumb">
			Benchmark
		</span>
		<span
			*ngSwitchCase="'TIPOS_BENCHMARK'"
			i18n="@@crud:tipos-benchmark:breadcrumb">
			Tipos Benchmark
		</span>
		<span *ngSwitchCase="'WOD'" i18n="@@crossfit:crud-wod:breadcrumb">Wod</span>
		<span *ngSwitchCase="'ATIVIDADE'" i18n="@@crud-atividade:breadcrumb">
			Atividade
		</span>
		<span *ngSwitchCase="'ATIVIDADE_IA'" i18n="@@crud-atividade:breadcrumb">
			Atividades Treino por IA
		</span>
		<span *ngSwitchCase="'COLABORADOR'" i18n="@@crud-colaborador:breadcrumb">
			Colaborador
		</span>
		<span *ngSwitchCase="'PERFIL'" i18n="@@crud-perfil-de-acesso:breadcrumb">
			Perfil de acesso
		</span>
		<span *ngSwitchCase="'NIVEIS'" i18n="@@crud-niveis:breadcrumb">Níveis</span>
		<span *ngSwitchCase="'MONITOR'" i18n="@@config-monitor:breadcrumb">
			Monitor
		</span>
		<span
			*ngSwitchCase="'TIPO-WOD'"
			i18n="@@crossfit:crud-tipos-wod:breadcrumb">
			Tipos Wod
		</span>
		<span *ngSwitchCase="'MODALIDADE'" i18n="@@crud-modalidade:breadcrumb">
			Modalidade
		</span>
		<span *ngSwitchCase="'AULAS'" i18n="@@crud-aulas:breadcrumb">Aulas</span>
		<span *ngSwitchCase="'AMBIENTES'" i18n="@@crud-ambientes:breadcrumb">
			Ambientes
		</span>
		<span
			*ngSwitchCase="'AULASEXCLUIDAS'"
			i18n="@@crud-aulas-excluidas:breadcrumb">
			Aulas Excluídas
		</span>
		<span
			*ngSwitchCase="'TIPO-AGENDAMENTO'"
			i18n="@@crud-tipo-agendamento:title">
			Tipos de Agendamentos
		</span>
		<span *ngSwitchDefault>{{ breadCrumbName }}</span>
	</ng-container>
</ng-template>
