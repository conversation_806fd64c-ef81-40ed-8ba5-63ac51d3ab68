import { EmpresaFinanceiro } from "./empresa-financeiro.model";

export enum PlataformaModulo {
	NTR = "NTR",
	NCR = "NCR",
	NAV = "NAV",
	GRD = "GRD",
	NZW = "NZW",
	CCL = "CANAL",
	AGN = "AGN",
	PAY = "PAY",
	SLC = "SLC",
	TR = "TR",
	CRM = "CRM",
	NCRM = "NCRM",
	FIN = "FIN",
	GOR = "GOR",
	ZW = "ZW",
	UCP = "UCP",
	NF = "NF",
	EST = "EST",
	GP = "GP",
	SBX = "SBX",
	ZAA = "ZAA",
	CE = "CE",
	NVO = "NVO",
	NMKT = "NMKT",
	// Agenda. foi definido vários para manter a compatibilidade com enuns legados do zw, que foram definidos assim.
	AGENDA = "AGENDA",
	AGE = "AGE",
	FAC = "FAC",
	CONFIG = "CONFIG",
	NBIS = "NBIS",
	IA = "IA",
}

export interface ClientDiscoveryData {
	modulosHabilitados?: PlataformaModulo[];
	empresas?: { codigo: number; id: number; nome: string }[];
	financeiroEmpresas?: EmpresaFinanceiro[];
	serviceUrls?: ServiceMap;
	redeEmpresas?: Rede[];
	utilizarMoviDesk?: boolean;
	utilizarChatMoviDesk?: boolean;
	grupoChatMovidesk?: string;
	utilizarOctadesk?: boolean;
	utilizarGymbot?: boolean;
}

export interface Rede {
	redeEmpresas: any;
	idRede: number;
	chaveRede: string;
	nomeFantasia: string;
	razaoSocial: string;
	chave: string;
	zwUrl: string;
	planoMsUrl: string;
	modulosHabilitados: PlataformaModulo[];
	empresaZw?: number;
}

export interface LoginDiscoveryRequestData {
	chave?: string;
	username?: string;
	senha?: string;
	idEmpresa?: number;
}

export interface LoginDiscoveryData {
	dados?: LoginDadosDiscoveryData;
	token?: string;
	validade?: number;
}

export interface LoginDadosDiscoveryData {
	codZW?: string;
	codTreino?: string;
	perfilZW?: string;
	perfilTR?: string;
	userName?: string;
	nome?: string;
	colaboradorId?: string;
	provider?: string;
	administrador?: boolean;
}

export interface ServiceMap {
	alunoMsUrl?: string;
	loginMsUrl?: string;
	colaboradorMsUrl?: string;
	graduacaoMsUrl?: string;
	treinoApiUrl?: string;
	treinoUrl?: string;
	loginAppUrl?: string;
	oamdUrl?: string;
	zwUrl?: string;
	personagemMsUrl?: string;
	autenticacaoUrl?: string;
	frontPersonal?: string;
	planoMsUrl?: string;
	produtoMsUrl?: string;
	cadastroAuxiliarUrl?: string;
	clubeVantagensMsUrl?: string;
	relatorioMsUrl?: string;
	acessoSistemaMsUrl?: string;
	biMsUrl?: string;
	relatorioFull?: string;
	zwFrontUrl?: string;
	treinoFrontUrl?: string;
	zwUrlFull?: string;
	apiZwUrl?: string;
	urlPlano?: string;
	admCoreUrl?: string;
	loginFrontUrl?: string;
	pactoPayDashUrl?: string;
	contatoMsUrl?: string;
	pactoPayMsUrl?: string;
	crmMsUrl?: string;
	admMsUrl?: string;
	urlMidiaSocialMs?: string;
	urlMarketingMs?: string;
	urlCrmFront?: string;
	notificacaoMs?: string;
	financeiroMsUrl?: string;
	pessoaMsUrl?: string;
	recursoMsUrl?: string;
	zwBack?: string;
}
