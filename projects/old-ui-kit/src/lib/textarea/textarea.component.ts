import { Component, OnInit, Input } from "@angular/core";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-textarea",
	templateUrl: "./textarea.component.html",
	styleUrls: ["./textarea.component.scss"],
})
export class TextareaComponent implements OnInit {
	@Input("id") id: string;
	@Input("name") name: string;
	@Input("label") label: string;
	@Input("rows") rows = 5;
	@Input("mensagem") mensagem: string;
	@Input("placeholder") placeholder: string;
	@Input("control") control: FormControl;

	constructor() {}

	ngOnInit() {}

	get showError() {
		if (this.control) {
			return !this.control.valid && this.control.touched;
		} else {
			return false;
		}
	}
}
