import { Injectable } from "@angular/core";
import {
	ApiResponseList,
	ApiResponseSingle,
	ApiRequestQueries,
} from "./base.model";
import { TreinoApiModule } from "./treino-api.module";

import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { TreinoApiBaseService } from "./treino-api-base.service";
import {
	UsuarioBase,
	UsuarioColaborador,
	UsuarioColaboradorCreateEdit,
	UsuarioColaboradorList,
} from "./user.model";

export interface UsuarioFiltro {
	filtroNome?: string;
	userName?: string;
}

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiUserService {
	constructor(private restService: TreinoApiBaseService) {}

	obterProfessores(
		incluirInativos: boolean = false
	): Observable<Array<UsuarioBase>> {
		const params: any = {};
		params.incluirInativos = incluirInativos ? "true" : "false";
		return this.restService.get(`professores`, { params }).pipe(
			map((response: ApiResponseList<UsuarioBase>) => {
				return response.content;
			})
		);
	}

	obterColaboradores(
		queries?: ApiRequestQueries,
		filtros?: UsuarioFiltro
	): Observable<ApiResponseList<UsuarioColaboradorList>> {
		const params = queries ? queries.getParamsObject() : {};
		for (const key in filtros) {
			if (filtros.hasOwnProperty(key) && filtros[key]) {
				params[key] = filtros[key];
				const element = filtros[key];
			}
		}
		return this.restService
			.get("usuarios/colaboradores", {
				params,
			})
			.pipe(
				map((response: ApiResponseList<UsuarioColaboradorList>) => {
					return response;
				})
			);
	}

	obterColaborador(id): Observable<UsuarioColaborador> {
		return this.restService.get(`usuarios/colaboradores/${id}`).pipe(
			map((response: ApiResponseSingle<UsuarioColaborador>) => {
				return response.content;
			})
		);
	}

	cadastrarColaborador(
		dados: UsuarioColaboradorCreateEdit
	): Observable<UsuarioColaborador> {
		return this.restService.post("usuarios/colaboradores", dados).pipe(
			map((response: ApiResponseSingle<UsuarioColaborador>) => {
				return response.content;
			}),
			catchError((error, caught) => {
				return new Observable((observer) => {
					observer.next("usuario_duplicado");
					observer.complete();
				});
			})
		);
	}

	atualizarColaborador(
		id: number,
		dados: UsuarioColaboradorCreateEdit
	): Observable<UsuarioColaborador> {
		return this.restService.put(`usuarios/colaboradores/${id}`, dados).pipe(
			map((response: ApiResponseSingle<UsuarioColaborador>) => {
				return response.content;
			}),
			catchError((error, caught) => {
				return new Observable((observer) => {
					observer.next("usuario_duplicado");
					observer.complete();
				});
			})
		);
	}

	solicitarAtendimento(): Observable<string> {
		return this.restService.get("usuarios/solicitar-atendimento").pipe(
			map((response: ApiResponseSingle<string>) => {
				return response.content;
			})
		);
	}

	solicitarTrocaEmail(idUsuario: number, email: string): Observable<any> {
		return this.restService.patch(
			`usuarios/codigo-verificacao-email/${idUsuario}`,
			{},
			{
				params: {
					email,
				},
			}
		);
	}

	validarCodigo(
		idUsuario: number,
		token: string,
		codigoVerificacao: string
	): Observable<any> {
		return this.restService.patch(
			`usuarios/validar-codigo-verificacao/${idUsuario}`,
			{
				codigoVerificacao,
				token,
			}
		);
	}

	recuperarSenha(idUsuario: number): Observable<any> {
		return this.restService.get(`usuarios/recuperar-senha/${idUsuario}`);
	}

	desvincularUsuario(idUsuario: number): Observable<any> {
		return this.restService.get(`usuarios/desvincular-usuario/${idUsuario}`);
	}
}
