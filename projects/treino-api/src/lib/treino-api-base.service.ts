import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable, Optional } from "@angular/core";
import { mergeMap } from "rxjs/operators";
import { Observable } from "rxjs";
import { TreinoApiModule } from "./treino-api.module";

import {
	TreinoApiConfigProviderBase,
	TreinoApiConfig,
} from "./treino-api-config-provider-base.service";

interface RequestOptions {
	[key: string]: any;

	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };
}

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiBaseService {
	private constructor(
		private httpClient: HttpClient,
		@Optional() private apiConfigProvider: TreinoApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para TreinoApiConfigProviderBase"
			);
		}
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: TreinoApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		const mergedHeaders = {
			...headers,
			Authorization: `Bearer ${apiConfig.authToken}`,
			empresaId: apiConfig.empresaId,
		};

		if (options) {
			options.headers = mergedHeaders;
			return options;
		} else {
			return { headers: mergedHeaders };
		}
	}

	public get(
		url: string,
		options: RequestOptions = {},
		unsecurePath: boolean = false
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url, unsecurePath);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get(fullUrl, mergedOptions);
			})
		);
	}

	public delete(
		url: string,
		options: RequestOptions = {},
		unsecurePath: boolean = false
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url, unsecurePath);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete(fullUrl, mergedOptions);
			})
		);
	}

	public put(
		url: string,
		body: any,
		options: RequestOptions = {},
		unsecurePath: boolean = false
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url, unsecurePath);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put(fullUrl, body, mergedOptions);
			})
		);
	}

	public post(
		url: string,
		body: any,
		options: RequestOptions = {},
		unsecurePath: boolean = false
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url, unsecurePath);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post(fullUrl, body, mergedOptions);
			})
		);
	}

	public patch(
		url: string,
		body: any,
		options: RequestOptions = {},
		unsecurePath: boolean = false
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url, unsecurePath);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.patch(fullUrl, body, mergedOptions);
			})
		);
	}

	private buildUrl(
		baseUrl: string,
		relativeUrl: string,
		unsecurePath: boolean = false
	) {
		if (unsecurePath) {
			return `${baseUrl}/${relativeUrl}`;
		} else {
			return `${baseUrl}/psec/${relativeUrl}`;
		}
	}
}
