import { Component, OnInit, ChangeDetectionStrategy } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { CclSessionService, SetupParams } from "../ccl-session.service";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "sdk";

@Component({
	selector: "ccl-load",
	templateUrl: "./load.component.html",
	styleUrls: ["./load.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoadComponent implements OnInit {
	loading = false;

	constructor(
		private cclSession: CclSessionService,
		private session: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		private router: Router,
		private route: ActivatedRoute
	) {}

	ngOnInit() {
		this.fecharMenu();
		const codFinanceiro: string = String(
			this.session.currentEmpresa.codigoFinanceiro
		);
		const params: SetupParams = {
			chave: this.session.chave,
			usuarioZwId: this.session.codUsuarioZW,
			usuarioOamd: this.session.usuarioOamd,
			empresaId: this.session.empresaId,
			zwUrl: this.session.pathUrlZw,
			oamdUrl: this.session.oamdUrl,
			codigoFinanceiro: codFinanceiro,
			paginaDirecionar: null,
			marketplace: null,
			treinoIndependente: false,
			treinoUrl: this.session.treinoUrl,
			zwBootUrl: this.clientDiscoveryService.getUrlMap().zwBack
		};
		this.cclSession.load(params).subscribe((success) => {
			if (success) {
				this.router.navigate(["canal-cliente", "cc", "atendimento"]);
			} else {
				console.info(
					"Erro ao carregar recursos do Canal do Cliente. Verifique o Network em develop tools para mais detalhes."
				);
			}
		});
	}

	fecharMenu() {
		try {
			const menu = document.getElementById("sidebar-menu-toggle");
			if (menu.classList.contains("opened")) {
				menu.click();
			}
		} catch (e) {}
	}
}
