import { NavModuleI18n } from "ui-kit";

const relatorioGeralClienteEs: NavModuleI18n = {
	relatorioGeralCliente: {
		name: "Geral de Clientes",
		description: "Relatório geral de clientes",
		searchTokens:
			"relatorio, relatório, clientes, geral, relatorio geral de clientes",
	},
};

const relatorioClienteEs: NavModuleI18n = {
	relatorioCliente: {
		name: "Relatório de Clientes",
		description: "Relatório de Clientes",
		searchTokens: "relatorio, clientes",
	},
};

const clienteEs: NavModuleI18n = {
	cliente: {
		name: "Cliente",
		description: "Cliente",
		searchTokens: "cliente, clientes",
	},
};

const relatorioClienteSimplificadoEs: NavModuleI18n = {
	relatorioClienteSimplificado: {
		name: "Lista de Clientes Simplificada",
		description: "Lista de Clientes Simplificada",
		searchTokens: "cliente, simplificado",
	},
};

const relatorioClientesVisitantesEs: NavModuleI18n = {
	relatorioClientesVisitantes: {
		name: "Visitantes",
		description: "Relatório de Visitantes",
		searchTokens: "relatorio, visitantes",
	},
};

const relatorioClientesCanceladosEs: NavModuleI18n = {
	relatorioClientesCancelados: {
		name: "Clientes Cancelados",
		description: "Relatório de clientes cancelados",
		searchTokens: "relatorio, clientes, cancelados",
	},
};

const relatorioClientesTrancadosEs: NavModuleI18n = {
	relatorioClientesTrancados: {
		name: "Clientes Trancados",
		description: "relatorio, clientes, trancados",
		searchTokens: "",
	},
};

const relatorioClientesComBonusEs: NavModuleI18n = {
	relatorioClientesComBonus: {
		name: "Clientes com Bônus",
		description: "Relatório de clientes com bônus",
		searchTokens: "relatorio, clientes, bonus",
	},
};

const relatorioClientesComAtestadoEs: NavModuleI18n = {
	relatorioClientesComAtestado: {
		name: "Clientes com Atestado",
		description: "Relatório de clientes com atestado",
		searchTokens: "relatorio, clientes, atestado",
	},
};

const relarioClientesAniversariantesEs: NavModuleI18n = {
	relatorioClientesAniversariantes: {
		name: "Aniversariantes",
		description: "Relatório de aniversariantes",
		searchTokens:
			"relatorio, aviversariante, aniversariantes, aniversário, aniversário",
	},
};

const relatorioContratosPorDuracaoEs: NavModuleI18n = {
	relatorioContratosPorDuracao: {
		name: "Contratos por Duração",
		description: "Relatório de contratos por duração",
		searchTokens: "contrato, relatorio, duração",
	},
};

const relatorioGympassEs: NavModuleI18n = {
	relatorioGympass: {
		name: "Gympass",
		description: "Gympass",
		searchTokens: "relatorio, gympass",
	},
};

const relatorioSaldoCreditoEs: NavModuleI18n = {
	relatorioSaldoCredito: {
		name: "Saldo de Créditos",
		description: "Relatório de saldo de créditos",
		searchTokens: "relatorio, saldo, credito",
	},
};

const relatorioHistoricoPontoEs: NavModuleI18n = {
	relatorioHistoricoPonto: {
		name: "Histórico de Pontos",
		description: "Relatório de histórico de pontos",
		searchTokens: "relatorio, historico, pontos",
	},
};

const relatorioIndicadorAcessoEs: NavModuleI18n = {
	relatorioIndicadorAcesso: {
		name: "Indicador de Acesso",
		description: "Indicador de Acesso",
		searchTokens: "indicador, acesso",
	},
};

const relatorioOrcamentoEs: NavModuleI18n = {
	relatorioOrcamento: {
		name: "Orçamentos",
		description: "Orçamentos",
		searchTokens: "relatorio, orçamento",
	},
};

const relatorioConvidadoEs: NavModuleI18n = {
	relatorioConvidado: {
		name: "Convidados",
		description: "Convidados",
		searchTokens: "relatorio, convidados",
	},
};

const relatorioClienteComCobrancaAutomaticaBloqueadaEs: NavModuleI18n = {
	relatorioClienteComCobrancaAutomaticaBloqueada: {
		name: "Cobranças Automáticas Bloqueadas",
		description: "Cobranças Automáticas Bloqueadas",
		searchTokens:
			"clientes, cobrança, automática, bloqueada, cobrança bloqueada",
	},
};

const relatorioFechamentoAcessoEs: NavModuleI18n = {
	relatorioFechamentoAcesso: {
		name: "Fechamento Acessos",
		description: "Relatório de fechamento de acessos",
		searchTokens: "relatorio, fechamento, acessos",
	},
};

const relatorioTotalizadorAcessoEs: NavModuleI18n = {
	relatorioTotalizadorAcesso: {
		name: "Totalizador de Acessos",
		description: "Totalizador de Acessos",
		searchTokens: "totalizador, acessos",
	},
};

const relatorioTotalizadorTicketsEs: NavModuleI18n = {
	relatorioTotalizadorTickets: {
		name: "Totalizador de Tickets",
		description: "Totalizador de tickets",
		searchTokens: "totalizador, tickets",
	},
};

const relatorioArmarioEs: NavModuleI18n = {
	relatorioArmario: {
		name: "Armários",
		description: "Armários",
		searchTokens: "relatorio, armario",
	},
};

const relatorioListaAcessoEs: NavModuleI18n = {
	relatorioListaAcesso: {
		name: "Lista de Acessos",
		description: "Lista de Acessos",
		searchTokens: "lista, acesso",
	},
};

const relatorioListaChamadaEs: NavModuleI18n = {
	relatorioListaChamada: {
		name: "Lista de Chamada",
		description: "Lista de chamada",
		searchTokens: "lista, chamada",
	},
};

const relatorioFrequenciaOcupacaoEs: NavModuleI18n = {
	relatorioFrequenciaOcupacao: {
		name: "Frequência e Ocupação",
		description: "Frequência e Ocupação",
		searchTokens: "frequência, ocupação",
	},
};

const relatorioFrequenciaTurmasEs: NavModuleI18n = {
	relatorioFrequenciaTurmas: {
		name: "Relatório de Frequência de Turmas",
		description: "Frequência",
		searchTokens:
			"frequência, frequencia, turmas, relatório, relatório de frequência de turmas",
	},
};

const relatorioDescontoOcupacaoEs: NavModuleI18n = {
	relatorioDescontoOcupacao: {
		name: "Desconto por Ocupação",
		description: "Desconto por Ocupação",
		searchTokens: "desconto, ocupação",
	},
};

const relatorioMapaTurmasEs: NavModuleI18n = {
	relatorioMapaTurma: {
		name: "Mapa de Turmas",
		description: "Mapa de Turmas",
		searchTokens: "mapa, turma, turmas",
	},
};

const relatorioConsultaTurmaEs: NavModuleI18n = {
	relatorioConsultaTurma: {
		name: "Consulta de Turmas",
		description: "Consulta de Turmas",
		searchTokens: "consulta, turma, turmas",
	},
};

const relatorioFechamentoCaixaEs: NavModuleI18n = {
	relatorioFechamentoCaixa: {
		name: "Fechamento de Caixa por Operador",
		description: "Fechamento de Caixa por Operador",
		searchTokens: "fechamento, caixa, operador",
	},
};

const relatorioCompetenciaMensalEs: NavModuleI18n = {
	relatorioCompetenciaMensal: {
		name: "Competência Mensal",
		description: "Competência Mensal",
		searchTokens: "competência, mensal",
	},
};

const relatorioFaturamentoEs: NavModuleI18n = {
	relatorioFaturamento: {
		name: "Faturamento",
		description: "Relatório de faturamento",
		searchTokens: "relatorio, faturamento",
	},
};

const relatorioFaturamentoRecebidoEs: NavModuleI18n = {
	relatorioFaturamentoRecebido: {
		name: "Faturamento Recebido",
		description: "Faturamento Recebido",
		searchTokens: "faturamento, recebido",
	},
};

const relatorioReceitaPorPeriodoEs: NavModuleI18n = {
	relatorioReceitaPorPeriodo: {
		name: "Receita por Período",
		description: "Receita por período",
		searchTokens: "receita, período",
	},
};

const relatorioParcelasEs: NavModuleI18n = {
	relatorioParcelas: {
		name: "Parcelas",
		description: "Relatório de Parcelas",
		searchTokens: "relatorio, parcelas",
	},
};

const pedidosPinPadEs: NavModuleI18n = {
	pedidosPinPad: {
		name: "Pedidos Pinpad",
		description: "Pedidos Pinpad",
		searchTokens: "pinpad, pedidos, pedidos pinpad",
	},
};

const relatorioSaldoContaCorrenteEs: NavModuleI18n = {
	relatorioSaldoContaCorrente: {
		name: "Saldo Conta Corrente",
		description: "Relatório de saldo de conta corrente",
		searchTokens: "relatorio, saldo, conta corrente, conta, corrente",
	},
};

const relatorioTransacoesPixEs: NavModuleI18n = {
	relatorioTransacoesPix: {
		name: "Transações PIX",
		description: "Relatório de Transações PIX",
		searchTokens: "relatório, transações, pix",
	},
};

const relatorioConsultaRecibosEs: NavModuleI18n = {
	relatorioConsultaRecibos: {
		name: "Consulta de Recibos",
		description: "Consulta de recibos",
		searchTokens: "consulta, recibos",
	},
};

const relatorioCupomFiscalEs: NavModuleI18n = {
	relatorioCupomFiscal: {
		name: "Cupom Fiscal",
		description: "Cupom Fiscal",
		searchTokens: "cupom, fiscal",
	},
};

const relatorioComissaoProfessorEs: NavModuleI18n = {
	relatorioComissaoProfessor: {
		name: "Comissão para Professor",
		description: "Comissão para professor",
		searchTokens: "comissão, professor",
	},
};

const relatorioComissaoConsultorEs: NavModuleI18n = {
	relatorioComissaoConsultor: {
		name: "Comissão para Consultor",
		description: "Comissão para consultor",
		searchTokens: "comissão, consultor",
	},
};

const relatorioPrevisaoRenovacaoEs: NavModuleI18n = {
	relatorioPrevisaoRenovacao: {
		name: "Previsão de Renovação",
		description: "Previsão de Renovação",
		searchTokens:
			"previsão, renovação, relatório de previsão de renovação por contrato",
	},
};

const relatorioBvsEs: NavModuleI18n = {
	relatorioBvs: {
		name: "BVs",
		description: "Relatório de BVs",
		searchTokens: "bv, bvs",
	},
};

const relatorioRepasseEs: NavModuleI18n = {
	relatorioRepasse: {
		name: "Repasse",
		description: "Repasse",
		searchTokens: "repasse",
	},
};

const relatorioPesquisaEs: NavModuleI18n = {
	relatorioPesquisa: {
		name: "Pesquisas",
		description: "Pesquisas",
		searchTokens: "pesquisas",
	},
};

const transacoesPixEs: NavModuleI18n = {
	transacoesPix: {
		name: "Transações Pix",
		description: "Transações Pix",
		searchTokens: "transacoes pix, pix, transações",
	},
};

const conviteAulaExperimentalOcupacaoEs: NavModuleI18n = {
	conviteAulaExperimentalOcupacao: {
		name: "Convite Aula Experimental",
		description: "Convite Aula Experimental",
		searchTokens: "convites, aula, experimental",
	},
};

const relatorioPersonalEs: NavModuleI18n = {
	relatorioPersonal: {
		name: "Personal",
		description: "Personal",
		searchTokens: "relatorio, personal",
	},
};

const relatorioProdutoComVigenciaEs: NavModuleI18n = {
	relatorioProdutoComVigencia: {
		name: "Produtos (com vigência)",
		description: "Produtos (com vigência)",
		searchTokens: "produto, com vigência, relatorio, vigência",
	},
};

const relatorioMovimentoProdutoEs: NavModuleI18n = {
	relatorioMovimentoProduto: {
		name: "Movimento de Produto",
		description: "Movimento de Produto",
		searchTokens: "movimento, produto",
	},
};

const relatorioCardexEs: NavModuleI18n = {
	relatorioCardex: {
		name: "Cardex",
		description: "Cardex",
		searchTokens: "cardex",
	},
};

const relatorioControleLogsEs: NavModuleI18n = {
	relatorioControleLogs: {
		name: "Controle de Logs",
		description: "Controle de Logs",
		searchTokens: "controle, logs",
	},
};

const relatorioComissaoEs: NavModuleI18n = {
	relatorioComissao: {
		name: "Comissão",
		description: "Comissão",
		searchTokens: "comissão",
	},
};

const relatorioDiarioEs: NavModuleI18n = {
	relatorioDiario: {
		name: "Diário",
		description: "Diário",
		searchTokens: "diario, diário",
	},
};

const relatorioAgendamentosEs: NavModuleI18n = {
	relatorioAgendamentos: {
		name: "Agendamentos",
		description: "Agendamentos",
		searchTokens: "agendamentos",
	},
};

const relatorioClientesSemSessaoEs: NavModuleI18n = {
	relatorioClientesSemSessao: {
		name: "Clientes sem Sessão",
		description: "Clientes sem Sessão",
		searchTokens: "clientes, sem sessão, sessão",
	},
};

export const relatorioSgpModalidadesSemTurmaMenuEs: NavModuleI18n = {
	relatorioSgpModalidadesSemTurma: {
		name: "SGP Modalidades sem Turma",
	},
};

export const relatorioSgpModalidadesComTurmaMenuEs: NavModuleI18n = {
	relatorioSgpModalidadesComTurma: {
		name: "SGP Modalidades com Turma",
	},
};

export const relatorioSgpTurmaMenuEs: NavModuleI18n = {
	relatorioSgpTurma: {
		name: "SGP Mapa Estatístico",
	},
};

export const relatorioSgpAvaliacoesFisicasMenuEs: NavModuleI18n = {
	relatorioSgpAvaliacoesFisicas: {
		name: "SGP Avaliações Fisicas",
	},
};

export const relatorioAcessoParentMenuEs: NavModuleI18n = {
	"relatorio-acesso": {
		name: "Relatórios de Acessos",
	},
};

export const relatorioClienteParentMenuEs: NavModuleI18n = {
	"relatorio-cliente": {
		name: "Relatórios de Clientes",
	},
};

export const relatorioComissaoParentMenuEs: NavModuleI18n = {
	"relatorio-comissao": {
		name: "Relatórios de Comissão",
	},
};

export const relatorioEstatisticoParentMenuEs: NavModuleI18n = {
	"relatorio-estatistico": {
		name: "Relatório Estatístico",
	},
};

export const relatorioFinanceiroParentMenuEs: NavModuleI18n = {
	"relatorio-financeiro": {
		name: "Relatório Financeiro",
	},
};

export const relatorioTurmaParentMenuEs: NavModuleI18n = {
	"relatorio-turma": {
		name: "Relatório de Turmas",
	},
};

export const relatorioOutrosParentMenuEs: NavModuleI18n = {
	"relatorio-outros": {
		name: "Outros relatórios",
	},
};

const relatorioClientesComRestricoesEs: NavModuleI18n = {
	relatorioClientesComRestricoes: {
		name: "Clientes com restrições",
	},
};

export const relatorioMenuEs: NavModuleI18n = {
	relatorios: {
		name: "Relatórios",
		description: `Relatórios`,
		searchTokens: "relatorios, relatórios",
	},
	...relatorioAcessoParentMenuEs,
	...relatorioClienteParentMenuEs,
	...relatorioComissaoParentMenuEs,
	...relatorioEstatisticoParentMenuEs,
	...relatorioFinanceiroParentMenuEs,
	...relatorioTurmaParentMenuEs,
	...relatorioOutrosParentMenuEs,
	...relatorioAgendamentosEs,
	...relarioClientesAniversariantesEs,
	...relatorioArmarioEs,
	...relatorioBvsEs,
	...relatorioCardexEs,
	...relatorioClienteSimplificadoEs,
	...relatorioClienteEs,
	...relatorioClientesCanceladosEs,
	...relatorioClientesTrancadosEs,
	...relatorioClientesComAtestadoEs,
	...relatorioClientesComBonusEs,
	...relatorioClienteComCobrancaAutomaticaBloqueadaEs,
	...relatorioClientesSemSessaoEs,
	...relatorioComissaoEs,
	...relatorioComissaoConsultorEs,
	...relatorioComissaoProfessorEs,
	...relatorioCompetenciaMensalEs,
	...relatorioMapaTurmasEs,
	...relatorioConsultaTurmaEs,
	...relatorioConsultaRecibosEs,
	...relatorioContratosPorDuracaoEs,
	...relatorioControleLogsEs,
	...relatorioConvidadoEs,
	...relatorioCupomFiscalEs,
	...relatorioDescontoOcupacaoEs,
	...relatorioDiarioEs,
	...relatorioFaturamentoEs,
	...relatorioFaturamentoRecebidoEs,
	...relatorioFechamentoAcessoEs,
	...relatorioFechamentoCaixaEs,
	...relatorioFrequenciaOcupacaoEs,
	...relatorioFrequenciaTurmasEs,
	...relatorioGeralClienteEs,
	...relatorioGympassEs,
	...relatorioHistoricoPontoEs,
	...relatorioIndicadorAcessoEs,
	...relatorioListaAcessoEs,
	...relatorioListaChamadaEs,
	...relatorioMapaTurmasEs,
	...relatorioMovimentoProdutoEs,
	...relatorioOrcamentoEs,
	...relatorioParcelasEs,
	...relatorioPersonalEs,
	...relatorioPesquisaEs,
	...relatorioPrevisaoRenovacaoEs,
	...relatorioProdutoComVigenciaEs,
	...relatorioReceitaPorPeriodoEs,
	...relatorioRepasseEs,
	...relatorioSaldoContaCorrenteEs,
	...relatorioSaldoCreditoEs,
	...relatorioTotalizadorAcessoEs,
	...relatorioTotalizadorTicketsEs,
	...relatorioTransacoesPixEs,
	...relatorioClientesVisitantesEs,
	...relatorioSgpModalidadesComTurmaMenuEs,
	...relatorioSgpTurmaMenuEs,
	...relatorioSgpModalidadesSemTurmaMenuEs,
	...relatorioSgpAvaliacoesFisicasMenuEs,
	...clienteEs,
	...pedidosPinPadEs,
	...transacoesPixEs,
	...conviteAulaExperimentalOcupacaoEs,
	...relatorioClientesComRestricoesEs,
};
