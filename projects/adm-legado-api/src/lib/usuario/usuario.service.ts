import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmLegadoApiBaseService } from "../base/adm-legado-api-base.service";
import { ApiValidateTokenAdmResponse } from "../models/usuario.model";
import { Observable } from "rxjs";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class UsuarioService {
	constructor(private admLegadoApiBaseService: AdmLegadoApiBaseService) {}

	permissoes(
		chave: string,
		usuario: number,
		empresa: number
	): Observable<ApiValidateTokenAdmResponse> {
		return this.admLegadoApiBaseService.get<ApiValidateTokenAdmResponse>(
			`/insec/validateToken?chave=${chave}&usuario=${usuario}&empresaId=${empresa}`
		);
	}
}
