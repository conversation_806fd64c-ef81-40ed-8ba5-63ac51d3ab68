export interface OpcoesContrato {
	bonus: boolean;
	manutencaoModalidade: boolean;
	alterarHorario: boolean;
	alteracaoContrato: boolean;
	atestado: boolean;
	carencia: boolean;
	retornoAtestado: boolean;
	retornoCarencia: boolean;
	cancelamento: boolean;
	trancamento: boolean;
	retornoTrancamento: boolean;
	estorno: boolean;
	transferenciaEvo: boolean;
	transferenciaDireitoUso: boolean;
	recuperarDireitoUso: boolean;
	alterarVencimento: boolean;
	alterarPlano: boolean;
}

export enum TipoContrato {
	ESPONTANEO = 1,
	AGENDADO = 2,
}
