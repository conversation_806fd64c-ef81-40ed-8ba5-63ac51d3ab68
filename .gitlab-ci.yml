stages:
    - build
    - deploy
    - test
    - finish

variables:
    IMAGE: 'registry.gitlab.com/plataformazw/zw_ui:$CI_COMMIT_REF_SLUG'

build-libs:
    image: node:12
    tags:
        - docker
        - large
    variables:
        ENABLE_LOGIN_CAPTCHA: 'false'
    interruptible: true
    only:
        - merge_requests
        - develop
        - release/RC
        - master
    except:
        variables:
            - $RELEASE_TAG
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
    artifacts:
        paths:
            - dist/
            - src/environments/environment.ts
            - projects/sdk/src/lib/environments/environment.ts
            - projects/crm/src/environments/environment.ts
            - projects/login/src/environments/environment.ts
            - projects/adm/src/environments/environment.ts
            - projects/marketing/src/environments/environment.ts
    script:
        - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
        - npm rebuild node-sass
        - yarn run config -du {DISCOVERY_URL} -enl \'{ENABLE_NEW_LOGIN}\' -elc \'{ENABLE_LOGIN_CAPTCHA}\'
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-libs 2> >(grep -v warning 1>&2)

build-treino:
    image: node:12
    tags:
        - docker
        - large
    variables:
        ENABLE_LOGIN_CAPTCHA: 'false'
    interruptible: true
    only:
        - merge_requests
        - develop
        - release/RC
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
    needs:
        - build-libs
    dependencies:
        - build-libs
    artifacts:
        paths:
            - dist/treino
    script:
        - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
        - npm rebuild node-sass
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-treino 2> >(grep -v warning 1>&2)


.docker-build: &docker-build-template
    variables:
        DOCKER_TLS_CERTDIR: ''
        CONTEXT: ''
    tags:
        - locaweb
        - swarm
    only:
        - merge_requests
        - develop
        - release/RC
        - master
    script:
        - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
        - docker build -t $IMAGE --build-arg app_dir=$APP_DIR --build-arg context=$CONTEXT .
        - docker push $IMAGE

docker-build-treino:
    extends: .docker-build
    interruptible: true
    dependencies:
        - build-treino
    needs:
        - build-treino
    artifacts:
        paths:
            - dist/treino
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
    variables:
        IMAGE: 'registry.gitlab.com/plataformazw/zw_ui/novo-treino:$CI_COMMIT_REF_SLUG'
        APP_DIR: 'treino'

zeta-stage_merge-develop:
    image: registry.gitlab.com/plataformazw/tag-versions:master
    interruptible: true
    tags:
        - docker
    stage: finish
    allow_failure: true
    only:
        - master
        - $ZETA_STAGE
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    script:
        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
        - git reset --hard
        - git checkout develop && git pull
        - PULL_RESULT=$(git pull origin master)
        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=develop&title=AutoMerge\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: develop*\\nDevelop Guardian, salve-nos de todos os conflitos?\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"
omega-stage_merge-release:
    image: registry.gitlab.com/plataformazw/tag-versions:master
    interruptible: true
    tags:
        - docker
    stage: finish
    allow_failure: true
    only:
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    script:
        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - git branch -D "release/RC"
        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
        - git reset --hard
        - git checkout "release/RC" && git pull
        - PULL_RESULT=$(git pull origin master)
        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2FRC&title=ZW_UI-AutoMerge-RC\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/RC*\\nRelease Guardian, sabemos que é só front, mas precisamos muito de você!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

psi-stage_merge-edc:
    image: registry.gitlab.com/plataformazw/tag-versions:master
    interruptible: true
    tags:
        - docker
    stage: finish
    allow_failure: true
    only:
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    script:
        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
        - git reset --hard
        - git checkout "release/RC-EDC" && git pull
        - PULL_RESULT=$(git pull origin master)
        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2FRC-EDC&title=ZW_UI-AutoMerge-RC-EDC\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/RC-EDC*\\nRelease EDC (Clesão) Guardian, não deixe o front morrer!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

#fi-stage_merge-pratique:
#    image: registry.gitlab.com/plataformazw/tag-versions:master
#    interruptible: true
#    tags:
#        - docker
#    stage: finish
#    allow_failure: true
#    only:
#        - master
#    except:
#        variables:
#            - $DEPLOY_LOGIN_PROD
#            - $RELEASE_TAG
#    script:
#        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
#        - eval $(ssh-agent -s)
#        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#        - git config --global user.name "Pipeline"
#        - git config --global user.email "<EMAIL>"
#        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
#        - git reset --hard
#        - git checkout "release/RC-PRATIQUE" && git pull
#        - PULL_RESULT=$(git pull origin master)
#        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2FRC-PRATIQUE&title=ZW_UI-AutoMerge-RC-PRATIQUE\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/RC-PRATIQUE*\\nRelease PRATIQUE (Clesão) Guardian, não deixe o front morrer!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

chi-stage_merge-sesc:
    image: registry.gitlab.com/plataformazw/tag-versions:master
    interruptible: true
    tags:
        - docker
    stage: finish
    allow_failure: true
    only:
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    script:
        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
        - git reset --hard
        - git checkout "release/RC-SESC" && git pull
        - PULL_RESULT=$(git pull origin master)
        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2FRC-SESC&title=ZW_UI-AutoMerge-RC-SESC\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/RC-SESC*\\nRelease SESC (Clesão) Guardian, não deixe o front morrer!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

upsilon-stage_merge-manaus:
    image: registry.gitlab.com/plataformazw/tag-versions:master
    interruptible: true
    tags:
        - docker
    stage: finish
    allow_failure: true
    only:
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    script:
        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
        - git reset --hard
        - git checkout "release/rc-manaus" && git pull
        - PULL_RESULT=$(git pull origin master)
        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2Frc-manaus&title=ZW_UI-AutoMerge-RC-MANAUS\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/rc-manaus*\\nRelease MANAUS (Cassimiro) Guardian, não deixe o front morrer!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

tau-stage_merge-corpo-saude:
    image: registry.gitlab.com/plataformazw/tag-versions:master
    interruptible: true
    tags:
        - docker
    stage: finish
    allow_failure: true
    only:
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    script:
        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
        - git reset --hard
        - git checkout "release/rc-corpo-saude" && git pull
        - PULL_RESULT=$(git pull origin master)
        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2Frc-corpo-saude&title=ZW_UI-AutoMerge-rc-corpo-saude\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/rc-corpo-saude*\\nRelease Corpo-Saude (Cassimiro) Guardian, não deixe o front morrer!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

#qui-stage_merge-live:
#    image: registry.gitlab.com/plataformazw/tag-versions:master
#    interruptible: true
#    tags:
#        - docker
#    stage: finish
#    allow_failure: true
#    only:
#        - master
#    except:
#        variables:
#            - $DEPLOY_LOGIN_PROD
#            - $RELEASE_TAG
#    script:
#        - set +e #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
#        - eval $(ssh-agent -s)
#        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#        - git config --global user.name "Pipeline"
#        - git config --global user.email "<EMAIL>"
#        - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
#        - git reset --hard
#        - git checkout "release/RC-LIVE" && git pull
#        - PULL_RESULT=$(git pull origin master)
#        - "if echo \"$PULL_RESULT\" | grep -qE \"Automatic merge failed|would be overwritten\"; then\n  echo \"Contém conflitos!!\";\n  curl -s --request POST --header \"PRIVATE-TOKEN: $API_TOKEN\" --data \"source_branch=master&target_branch=release%2FRC-LIVE&title=ZW_UI-AutoMerge-RC-LIVE\" \"$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests\";\n  curl -s --request POST --header \"Content-Type: application/json\" --data '{\"text\": \"*Conflitos no ZW_UI!!*\\n*Branch: release/RC-LIVE*\\nRelease LIVE (Clesão) Guardian, não deixe o front morrer!\"}' \"https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y\"\n  exit 1\nelse \n  echo $CI_COMMIT_REF_NAME;\n  echo \"Não contém conflito\";\n  GIT_SSH_COMMAND=\"ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no\" git push\nfi\n  #magic___^_^___line\n"

tag-version:
    image: keviocastro/node-build
    interruptible: true
    when: on_success
    tags:
        - docker
        - large
    needs:
        - build-treino
    except:
        variables:
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    only:
        - master
    script:
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - npm version patch -m 'Bump version:%s'
        - git tag -f latest -m "Deploy tag"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags

tag-version-2:
    image: keviocastro/node-build
    interruptible: true
    when: on_success
    tags:
        - docker
        - large
    only:
        variables:
            - $RELEASE_TAG
    script:
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - npm version patch -m 'Bump version:%s'
        - git tag -f latest -m "Deploy tag"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags

deploy-login-prod-on-master:
    image: registry.gitlab.com/plataformazw/docker-pacto/deploy-node-aws:master
    interruptible: true
    only:
        variables:
            - $DEPLOY_LOGIN_PROD
    variables:
        DISCOVERY_URL: 'https://discovery.ms.pactosolucoes.com.br'
        ENABLE_LOGIN_CAPTCHA: 'true'
        ENABLE_NEW_LOGIN: 'true'
    tags:
        - docker
        - large
    stage: deploy
    artifacts:
        paths:
            - dist/
    script:
        - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
        - npm rebuild node-sass
        - node ./setup-env.js -du ${DISCOVERY_URL} -enl ${ENABLE_NEW_LOGIN} -elc ${ENABLE_LOGIN_CAPTCHA}
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-libs-login 2> >(grep -v warning 1>&2)
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-login-all 2> >(grep -v warning 1>&2)
        - aws s3 sync ./dist/login s3://$BUCKET_LOGIN_PROD/ --delete
        - aws cloudfront create-invalidation --distribution-id E1BXNJ1V5YN3W0 --paths "/*"

deploy-zw-ui-prod:
    image: registry.gitlab.com/plataformazw/docker-pacto/deploy-node-aws:master
    interruptible: true
    only:
        variables:
            - $DEPLOY_UI_PROD
    tags:
        - docker
        - large
    stage: deploy
    script:
        - aws ecs update-service --cluster pacto-ui-prod --service pacto-ui-prod-service --load-balancers targetGroupArn=arn:aws:elasticloadbalancing:sa-east-1:335687144049:targetgroup/tg-pacto-zw-ui-prod/85cbaa4eaafe0e28,containerName=zw-ui-prod,containerPort=80 --force-new-deployment --region sa-east-1 --task-definition zw-ui-prod

