import { EmpresaAcesso } from "./empresa-acesso.model";
import { EmpresaFinanceiro } from "./empresa.model";

export class TrocaEmpresa {
	public atual: boolean;
	public novo: boolean;
	public chave: string;
	public empresa: number;
	public nomeEmpresa: string;
	public empresaAcesso?: EmpresaAcesso;
	public codigoEmpresaRede: string;
	public nomeApresentar: string;

	constructor(
		empresaAcesso: EmpresaAcesso,
		empresaFinanceiro: EmpresaFinanceiro,
		chave: string
	) {
		this.atual = false;
		if (empresaAcesso) {
			this.novo = true;
			this.chave = empresaAcesso.key;
			this.empresa = empresaAcesso.emp;
			this.nomeEmpresa = empresaAcesso.nomeEmp;
			this.codigoEmpresaRede = empresaAcesso.empRede;
			this.empresaAcesso = empresaAcesso;
		}
		if (empresaFinanceiro) {
			this.novo = false;
			this.chave = chave;
			this.empresa = empresaFinanceiro.codigo;
			this.nomeEmpresa = empresaFinanceiro.nome;
			this.empresaAcesso = null;
		}

		if (this.codigoEmpresaRede && this.codigoEmpresaRede.length > 0) {
			this.nomeApresentar = this.nomeEmpresa + " - " + this.codigoEmpresaRede;
		} else {
			this.nomeApresentar = this.nomeEmpresa;
		}
	}
}
