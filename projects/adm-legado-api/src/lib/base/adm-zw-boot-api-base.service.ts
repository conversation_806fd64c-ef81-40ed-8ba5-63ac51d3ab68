import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable, Optional } from "@angular/core";
import { Observable } from "rxjs";
import { mergeMap } from "rxjs/operators";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import {
	AdmLegadoApiConfig,
	AdmLegadoApiConfigProviderBase,
} from "./adm-legado-api-config-provider-base.service";

interface RequestOptions {
	[key: string]: any;

	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };
}

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class AdmZwBootApiBaseService {
	private constructor(
		private httpClient: HttpClient,
		@Optional() private apiConfigProvider: AdmLegadoApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para AdmApiConfigProviderBase"
			);
		}
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: AdmLegadoApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		options.headers = {
			...headers,
			empresaId: apiConfig.empresaId,
		};

		const params = options.params ? options.params : {};
		options.params = params;
		return options;
	}

	public get<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfigZWBoot().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get<T | any>(fullUrl, mergedOptions);
			})
		);
	}

	public delete<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfigZWBoot().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete<T>(fullUrl, mergedOptions);
			})
		);
	}

	public put<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfigZWBoot().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	public post<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfigZWBoot().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	private buildUrl(baseUrl: string, relativeUrl: string) {
		return `${baseUrl}/${relativeUrl}`;
	}
}
