import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { TicketService } from "./ticket.service";
import { Ticket } from "./ticket.model";
import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import { CclModalService } from "canal-cliente/app/servicos/ccl-modal.service";
import { fadeIn } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { AberturaChamadoModalComponent } from "../abertura-chamado-modal/abertura-chamado-modal.component";
import { AdicionarAcaoModalComponent } from "../adicionar-acao-modal/adicionar-acao-modal.component";
import { CclSessionService } from "canal-cliente/app/ccl-session.service";

@Component({
	selector: "ccl-cliente-solicitacoes",
	templateUrl: "./solicitacoes.component.html",
	styleUrls: ["./solicitacoes.component.scss"],
	animations: [
		trigger("ticketExpandido", [
			state(
				"recolher",
				style({ height: "0px", minHeight: "0", display: "none" })
			),
			state("expandir", style({ height: "*" })),
			transition(
				"expandir <=> recolher",
				animate("200ms cubic-bezier(0.4, 0.0, 0.2, 1)")
			),
		]),
		trigger("fadeIn", fadeIn(":enter")),
	],
})
export class SolicitacoesComponent implements OnInit {
	@Input() titulo: string;
	@Input() filtro: string;
	@Input() filtrarAcoesPublicas: boolean;
	@Input() limite: number;
	@Input() exibirSolicitaocesFirestore: boolean;
	@Input() mensagemNenhuma: string;
	@ViewChild("mensagemFinalizarTicker", { static: true })
	mensagemFinalizarTicketRef: TemplateRef<any>;
	@Input() mostrarIconeVerMais: boolean;
	@Output() clickIconeVerMais: EventEmitter<boolean> = new EventEmitter();
	@ViewChild("linkVerMais", { static: true }) linkVerMais: TemplateRef<any>;
	@Input() carregarMais: boolean;

	public ticketSelecionado: Ticket;
	public tickets: Ticket[];
	public ticketsMovidesk: Ticket[];
	public ticketsFirestore: Ticket[];
	public movDeskCodigoRef: string;
	public offsetTickets: number;

	constructor(
		private ticketService: TicketService,
		private cclModal: CclModalService,
		private notificationService: SnotifyService,
		private cclSession: CclSessionService,
		private cd: ChangeDetectorRef
	) {}

	loading = false;
	label = "Carregar mais...";
	private carregarMaisStr = "Carregar mais...";
	private carregandoStr = "Carregando...";

	ngOnInit(): void {
		const defaultLimit = 8;
		this.offsetTickets = 0;
		this.limite = this.limite ? this.limite : defaultLimit;
		this.tickets = new Array(this.limite);

		if (!this.mensagemNenhuma) {
			this.mensagemNenhuma = "Não existem solicitações";
		}
		this.load(true);
	}

	carregarMaisHandler(): void {
		if (!this.loading) {
			this.offsetTickets += 10;
			this.loading = true;
			this.label = this.carregandoStr;
			this.load(false);
		}
	}

	private load(isLoad: boolean) {
		if (this.movDeskCodigoRef) {
			this.obterTicketsMovidesk(isLoad);
		} else {
			this.ticketService
				.obterCodigoRefMovidesk(this.cnpjEmpresaLogadaComPontuacaoSemPontuacao)
				.subscribe((codigoRef: string) => {
					this.movDeskCodigoRef = codigoRef;
					this.obterTicketsMovidesk(isLoad);
				});
		}
	}

	get cnpjEmpresaLogadaComPontuacaoSemPontuacao() {
		return this.cclSession.empresaFinanceiro.cnpj
			.replace(".", "")
			.replace(".", "")
			.replace("/", "")
			.replace("-", "");
	}

	get qtdeTicketsParaLimite() {
		if (this.tickets.length > 0 && this.tickets[0] === undefined) {
			return this.limite;
		} else {
			return this.limite - this.tickets.length;
		}
	}

	private addSolicitacoesFirestore() {
		const limiteRestante = this.qtdeTicketsParaLimite;
		if (
			this.qtdeTicketsParaLimite > 0 &&
			this.ticketsFirestore &&
			this.exibirSolicitaocesFirestore
		) {
			if (this.tickets.length > 0 && this.tickets[0] === undefined) {
				// Por causa do ghost
				this.tickets = this.ticketsFirestore;
			}

			// Uni os tickets do movidesk com tickets do firesotre até o parametro limit
			// Tickets do Movidesk primeiro na lista
			let i = 0;
			this.ticketsFirestore.every((ticket) => {
				if (i <= limiteRestante) {
					this.tickets.push(ticket);
					i++;
					return true;
				} else {
					return false;
				}
			});
		}
	}

	obterSolicitacoesFirestore() {
		this.loading = true;
		if (this.exibirSolicitaocesFirestore && this.qtdeTicketsParaLimite > 0) {
			this.ticketService
				.obterTicketsFirestore(
					this.cclSession.empresaFinanceiro.cnpj,
					this.qtdeTicketsParaLimite
				)
				.subscribe((tickets) => {
					this.ticketsFirestore = tickets;
					this.addSolicitacoesFirestore();
					this.loading = false;
					this.label = this.carregarMaisStr;
					this.cd.detectChanges();
				});
		}
	}

	private obterTicketsMovidesk(isLoad: boolean) {
		let codigo;
		this.loading = true;
		// tslint:disable-next-line: max-line-length
		if (
			this.cclSession.empresaFinanceiro.codigoFinanceiro == null ||
			this.cclSession.empresaFinanceiro.codigoFinanceiro === 0
		) {
			codigo = this.movDeskCodigoRef;
			this.notificationService.info(
				"Código da Empresa Financeiro não foi localizado, entre em contato com a PACTO para realizar" +
					" os ajustes"
			);
		} else {
			codigo = this.cclSession.empresaFinanceiro.codigoFinanceiro;
		}
		this.ticketService
			.obterTicketsMovideskPorEmpresa(
				codigo,
				this.filtro,
				this.limite,
				this.offsetTickets,
				this.filtrarAcoesPublicas
			)
			.subscribe((tickets) => {
				if (this.exibirSolicitaocesFirestore && tickets.length === 0) {
					// Para continuar o gshot durante a consulta do firesotre
					this.tickets = this.tickets;
				} else {
					if (isLoad) {
						this.tickets = tickets;
					} else {
						this.tickets.push.apply(this.tickets, tickets);
					}
				}
				this.obterSolicitacoesFirestore();
				this.loading = false;
				this.label = this.carregarMaisStr;
				this.cd.detectChanges();
			});
	}

	addTickets(ticketList: Ticket[]) {
		if (this.tickets.length > 0) {
			this.tickets.concat(ticketList);
		} else {
			this.tickets = ticketList;
		}
	}

	expandirTicket(ticket: Ticket) {
		this.ticketSelecionado = ticket;
		ticket.expanded = !ticket.expanded;
	}

	confirmarFechamentoTicket(ticket: Ticket) {
		this.ticketSelecionado = ticket;
		const labelBotaoResolvido = "Chamado resolvido";
		const labelBotaoNaoResolvido = "Não resolvido";
		this.cclModal
			.confirmBodyRef(
				"Finalizar chamado",
				this.mensagemFinalizarTicketRef,
				labelBotaoResolvido,
				labelBotaoNaoResolvido
			)
			.result.then(() => {
				this.ticketService.finalizarTicketMovidesk(ticket).subscribe(() => {
					this.load(true);
				});
			})
			.catch((reason) => {
				if (reason === labelBotaoNaoResolvido) {
					this.ticketService.reabrirTickerMovidesk(ticket).subscribe(() => {
						this.load(true);
					});
				}
			});
	}

	confirmarReaberturaTicket(ticket: Ticket) {
		this.ticketSelecionado = ticket;
		const modal = this.cclModal.open(
			"Reabrir o chamado",
			AberturaChamadoModalComponent
		);
		const modalInstance: AberturaChamadoModalComponent =
			modal.componentInstance;
		modalInstance.loadTicket(ticket);

		modal.result.then(
			(justificativa) => {
				if (justificativa) {
					ticket.justification = justificativa;
				}

				this.ticketService.reabrirTickerMovidesk(ticket).subscribe(() => {
					this.load(true);
				});
			},
			() => {}
		);
	}

	adiconarNovaAcao(ticket: Ticket) {
		this.ticketSelecionado = ticket;
		const modal = this.cclModal.open(
			"Comentar o chamado 2",
			AdicionarAcaoModalComponent
		);
		const modalInstance: AdicionarAcaoModalComponent = modal.componentInstance;
		modalInstance.loadTicket(ticket);

		modal.result.then(
			(justificativa) => {
				if (justificativa) {
					ticket.justification = justificativa;
				}

				this.ticketService.adicionarAcaoTickerMovidesk(ticket).subscribe(() => {
					this.load(true);
				});
			},
			() => {}
		);
	}

	permiteConcluir(ticket: Ticket) {
		// FIXME: Se alterar o nome do status no movdesk aqui não vai funcionar. Isso deve ser testado.
		// FIXME: Se for alterado o processo de adequação de serviços no movdesk, aqui vai parar de funcionar. Isso deve ser testado.
		if (ticket.baseStatus === "Resolved") {
			if (
				ticket.serviceFirstLevelId === 125854 || // carteira RC> Atendimento a cliente
				ticket.serviceFirstLevelId === 128506 || // Implantação>Atendimento a cliente
				ticket.serviceFirstLevelId === 128505 // Consolidação>Atendimento a cliente
			) {
				return true;
			} else {
				return true;
			}
		} else {
			return false;
		}
	}

	permiteComentar(ticket: Ticket) {
		return !this.permiteConcluir(ticket) && ticket.baseStatus !== "Closed";
	}

	verMais() {
		this.clickIconeVerMais.emit(true);
	}
}
