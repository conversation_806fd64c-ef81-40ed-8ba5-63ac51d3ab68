import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { FormControl, FormGroup } from "@angular/forms";

import {
	PactoDataGridConfig,
	PactoModalRef,
	RelatorioComponent,
	TraducoesXinglingComponent,
	DataFiltro,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import {
	Api,
	ClientDiscoveryService,
	PerfilAcessoRecurso,
	SessionService,
} from "sdk";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-relatorio-saldo-credito",
	templateUrl: "./relatorio-saldo-credito.component.html",
	styleUrls: ["./relatorio-saldo-credito.component.scss"],
})
export class RelatorioSaldoCreditoComponent implements OnInit, AfterViewInit {
	@ViewChild("tableSaldoCreditoComponent", { static: false })
	tableSaldoCreditoComponent: RelatorioComponent;
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnMatricula", { static: true })
	columnMatricula: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnSaldoCreditos", { static: true })
	columnSaldoCreditos: TemplateRef<any>;
	@ViewChild("columnDataVigenciaAte", { static: true })
	columnDataVigenciaAte: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("columnTelefone", { static: true })
	columnTelefone: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: false })
	columnEmpresa: TemplateRef<any>;

	permissaoConsultarInfoTodasEmpresas =
		this.sessionService.perfilUsuario.funcionalidades.get(
			"consultarinfotodasempresas"
		);
	tableSaldoCredito: PactoDataGridConfig;
	modalDetalhamentoRef: PactoModalRef;
	saldoInicial: any;
	saldoFinal: any;
	ativo: boolean;
	inativo: boolean;
	mostrarTableSaldoCredito: boolean;
	filters: any;
	recurso: PerfilAcessoRecurso;
	baseFilter: DataFiltro = {};

	quickSearchControl: FormControl = new FormControl();

	formGroup: FormGroup = new FormGroup({
		empresa: new FormControl(),
		valorInicialCreditos: new FormControl(0),
		valorFinalCreditos: new FormControl(999),
		ativo: new FormControl(false),
		inativo: new FormControl(false),
	});
	empresaLogada: any;
	showWarning: boolean;

	constructor(
		private router: Router,
		private admRest: AdmRestService,
		private sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		private cd: ChangeDetectorRef
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.EMPRESA
		);
	}

	ngOnInit() {
		this.initForm();
	}

	ngAfterViewInit() {
		this.createTableSaldoCredito();
		this.cd.detectChanges();
	}

	initForm() {
		this.formGroup.get("empresa").setValue({
			codigo: this.sessionService.empresaId,
			nome: this.sessionService.currentEmpresa.nome,
		});
		if (this.permissaoConsultarInfoTodasEmpresas) {
			this.formGroup.get("empresa").valueChanges.subscribe((value) => {
				this.formGroup.get("valorInicialCreditos").setValue(0);
				this.formGroup.get("valorFinalCreditos").setValue(999);
				this.formGroup.get("ativo").setValue(this.ativo);
				this.formGroup.get("inativo").setValue(this.inativo);
			});
		} else {
			this.formGroup.get("empresa").disable();
		}
	}

	voltarAdm() {
		this.router.navigate(["adm"]);
	}

	private createTableSaldoCredito() {
		this.tableSaldoCredito = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrl(
				"/relatorio-saldo-credito",
				false,
				Api.MSRELATORIO
			),
			quickSearch: true,
			ghostLoad: false,
			ghostAmount: 5,
			showFilters: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					visible: true,
					buscaRapida: true,
					ordenavel: true,
				},
				{
					nome: "saldoCreditos",
					titulo: this.columnSaldoCreditos,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataVigenciaAte",
					titulo: this.columnDataVigenciaAte,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "situacao",
					titulo: this.columnSituacao,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					// todo: criar metodo separado
					valueTransform: (v: any) => {
						if (v === "AT") {
							return "ATIVO";
						}
						if (v === "IN") {
							return "INATIVO";
						}
						if (v === "VI") {
							return "VIGENTE";
						}
					},
				},
				{
					nome: "telefone",
					titulo: this.columnTelefone,
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "empresa",
					titulo: this.columnEmpresa,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
				},
			],
		});
	}

	consultar() {
		try {
			this.showWarning = false;
			this.mostrarTableSaldoCredito = true;
			this.filters = {
				empresa: this.formGroup.get("empresa").value.codigo,
				valorInicialCreditos: this.formGroup.get("valorInicialCreditos").value,
				valorFinalCreditos: this.formGroup.get("valorFinalCreditos").value,
				ativo: this.formGroup.get("ativo").value,
				inativo: this.formGroup.get("inativo").value,
			};
			this.baseFilter.filters = this.filters;
		} catch (e) {
			this.showWarning = true;
			this.mostrarTableSaldoCredito = false;
		}
		setTimeout(() => {
			this.tableSaldoCreditoComponent.reloadData();
			this.scrollToComponent("tableSaldoCreditoComponent");
		});
	}

	cleanFilters() {
		this.formGroup.get("ativo").setValue(false);
		this.formGroup.get("inativo").setValue(false);
		this.formGroup.get("empresa").setValue({
			codigo: this.sessionService.empresaId,
			nome: this.sessionService.currentEmpresa.nome,
		});
		this.formGroup.get("valorInicialCreditos").setValue(undefined);
		this.formGroup.get("valorFinalCreditos").setValue(undefined);
		this.cd.detectChanges();
	}

	scrollToComponent(idComponent) {
		const el = document.getElementById(idComponent);
		el.scrollIntoView();
	}

	getUrlEmpresas(): string {
		return this.admRest.buildFullUrl(
			"empresas/find-all-actives",
			false,
			Api.MSRELATORIO
		);
	}
}
