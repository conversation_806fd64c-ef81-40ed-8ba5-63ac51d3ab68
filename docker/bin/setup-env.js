#!/usr/bin/env node
const replace = require('replace-in-file');
const storageFilePath = `${process.cwd() + '/store.json'}`;
const store = require('data-store')({ path: storageFilePath });
const figlet = require('figlet');

// Local debug
// process.env.DISCOVERY_URL = 'http://discovery.brd'
// process.env.ENABLE_NEW_LOGIN = 'true'
// process.env.ENABLE_LOGIN_CAPTCHA = 'false'

figlet(process.env.PROJECT_NAME, function(err, data) {
	if (err) {
		console.dir(err);
		return;
	}
	console.log(data);

	const initial_environment = [
		'https://discovery.ms.pactosolucoes.com.br',
		'{DISCOVERY_URL}',
		'{ENABLE_NEW_LOGIN}',
		'{ENABLE_LOGIN_CAPTCHA}',
		'{HABILITA_MARKETING}'
	];

	const new_environment = [
		process.env.DISCOVERY_URL,
		process.env.DISCOVERY_URL,
		process.env.ENABLE_NEW_LOGIN,
		process.env.ENABLE_LOGIN_CAPTCHA,
		process.env.HABILITA_MARKETING
	];

	if (
		!store.get('environment') ||
		!Array.isArray(store.get('environment')) ||
		store
			.get('environment')
			.filter(env => env !== null && env !== undefined).length === 0
	) {
		store.set('environment', initial_environment);
	}

	const last_environment = store.get('environment');
	console.log('Current configs', last_environment);
	console.log('New configs', new_environment);
	if (last_environment.toString() !== new_environment.toString()) {
		console.log('CONFIG FROM');
		console.log(last_environment);
		console.log('TO');
		console.log(new_environment);
		const options = {
			files: '/usr/share/nginx/html/**/*js*',
			from: last_environment.map(value => new RegExp(value, 'g')),
			to: new_environment,
			ignore: storageFilePath
		};

		replace(options)
			.then(results => {
				filesChanged = results.filter(file => file.hasChanged);
				if (filesChanged.length > 0) {
					store.set('environment', new_environment);
				}
				console.log('files changed during config setup', filesChanged);
			})
			.catch(err => {
				console.log(
					'Copy your compiled production files to /usr/share/nginx/html/'
				);
				console.log('Error on setup environment', err);
			});
	} else {
		console.log('No need to apply configs');
	}
});
