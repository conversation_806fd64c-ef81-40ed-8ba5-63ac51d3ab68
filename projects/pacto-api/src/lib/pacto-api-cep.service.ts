import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { ApiResponseList } from "./base.model";
import { PactoApiBaseService } from "./pacto-api-base.service";
import { PactoApiModule } from "./pacto-api.module";
import { VendaRapida } from "./venda-rapida.model";

@Injectable({
	providedIn: PactoApiModule,
})
export class PactoApiCepService {
	constructor(private restService: PactoApiBaseService) {}

	public buscaCEP(chave: string, cep: string): Observable<any> {
		return this.restService.get(`v1/vendarapida/${chave}/cep/${cep}`).pipe(
			map((result: any) => {
				return result;
			})
		);
	}

	public consultarCEP(cep: string): Observable<any> {
		return this.restService.get(`cep/consultar?cep=${cep}`);
	}

	public buscaUF(chave: string, pais: number | string): Observable<any> {
		return this.restService.get(`v1/vendarapida/${chave}/estado/${pais}`).pipe(
			map((result: any) => {
				return result;
			})
		);
	}

	public buscaCidade(
		chave: string,
		codEstado: number | string
	): Observable<any> {
		return this.restService
			.get(`v1/vendarapida/${chave}/cidade/${codEstado}`)
			.pipe(
				map((result: any) => {
					return result;
				})
			);
	}

	public buscaPais(chave: string): Observable<any> {
		return this.restService.get(`v1/vendarapida/${chave}/pais`).pipe(
			map((result: any) => {
				return result;
			})
		);
	}
}
