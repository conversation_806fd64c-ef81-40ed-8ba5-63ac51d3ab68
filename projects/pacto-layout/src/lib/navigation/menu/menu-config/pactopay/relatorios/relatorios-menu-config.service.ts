import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PlatformMenuItem } from "../../../../models";
import { MenuConfigService } from "../../menu-config.service";

@Injectable({
	providedIn: "root",
})
export class RelatoriosMenuConfigService extends MenuConfigService {
	constructor(protected layoutNavigationService: LayoutNavigationService) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([
			this.cartaoCreditoOnlineMenuItem,
			this.parcelasEmAbertoMenuItem,
			this.relatorioParentItem,
		]);
	}

	get relatorioParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorios",
			configMenuExplorar: {
				submenus: [
					this.cartaoCreditoOnlineMenuItem,
					this.parcelasEmAbertoMenuItem,
				],
			},
		};

		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cartaoCreditoOnlineMenuItem(): PlatformMenuItem {
		return {
			id: "credito-online",
			permitido: true,
			favoriteIdentifier: "PACTOPAY_CARTAO_CREDITO_ONLINE",
			route: {
				internalLink: "/pactopay/transacoes/credito-online",
			},
		};
	}

	get parcelasEmAbertoMenuItem(): PlatformMenuItem {
		return {
			id: "parcelas",
			permitido: true,
			favoriteIdentifier: "PACTOPAY_PARCELAS_EM_ABERTO",
			route: {
				internalLink: "/pactopay/parcelas",
			},
		};
	}
}
