import { PlataformaModuleItem } from "ui-kit";
import { relatorioMenuParentItem } from "./relatorio-menu-parents-nav-config.service";
import {
	relarioClientesAniversariantes,
	relatorioAgendamentos,
	relatorioBvs,
	relatorioCardex,
	relatorioCliente,
	relatorioClienteComCobrancaAutomaticaBloqueada,
	relatorioClientesCancelados,
	relatorioClientesComAtestado,
	relatorioClientesComBonus,
	relatorioClienteSimplificado,
	relatorioClientesSemSessao,
	relatorioClientesTrancados,
	relatorioComissao,
	relatorioComissaoConsultor,
	relatorioComissaoProfessor,
	relatorioCompetenciaMensal,
	relatorioConsultaRecibos,
	relatorioConsultaTurma,
	relatorioControleLogs,
	relatorioDescontoOcupacao,
	relatorioDiario,
	relatorioFaturamento,
	relatorioFaturamentoRecebido,
	relatorioFechamentoAcesso,
	relatorioFechamentoCaixa,
	relatorioFrequenciaOcupacao,
	relatorioFrequenciaTurmas,
	relatorioGeralCliente,
	relatorioHistoricoPonto,
	relatorioListaChamada,
	relatorioMapaTurmas,
	relatorioMovimentoProduto,
	relatorioOrcamento,
	relatorioParcelas,
	relatorioPesquisa,
	relatorioPrevisaoRenovacao,
	relatorioReceitaPorPeriodo,
	relatorioRepasse,
	relatorioSaldoContaCorrente,
	relatorioTotalizadorTickets,
	relatorioTransacoesPix,
} from "./relatorio-menu-nav-legado-config.service";

export const relatorioClientesVisitantes: PlataformaModuleItem = {
	id: "relatorioClientesVisitantes",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-visitantes",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("relatoriodevisitantes");
		}
	},
};

export const relatorioContratosPorDuracao: PlataformaModuleItem = {
	id: "relatorioContratosPorDuracao",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/contratos-por-duracao",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("clienteporduracaorel");
		}
	},
};

export const relatorioGympass: PlataformaModuleItem = {
	id: "relatorioGympass",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-gympass",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("permitevisualizargympassperiodo");
		}
	},
};

export const relatorioSaldoCredito: PlataformaModuleItem = {
	id: "relatorioSaldoCredito",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-saldo-credito",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("saldocreditosclientes");
		}
	},
};

export const relatorioIndicadorAcesso: PlataformaModuleItem = {
	id: "relatorioIndicadorAcesso",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorios-acessos/indicador-acessos",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("relatorioindicadordeacessos");
		}
	},
};

export const relatorioConvidado: PlataformaModuleItem = {
	id: "relatorioConvidado",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-convidados",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("permitevisualizaconvitesperiodo");
		}
	},
};

export const relatorioTotalizadorAcesso: PlataformaModuleItem = {
	id: "relatorioTotalizadorAcesso",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorios-acessos/totalizador-acessos",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("relatoriodetotalizadordeacessos");
		}
	},
};

export const relatorioListaAcesso: PlataformaModuleItem = {
	id: "relatorioListaAcesso",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorios-acessos/lista-acessos",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("totalizadorfrequenciarel");
		}
	},
};

export const relatorioArmario: PlataformaModuleItem = {
	id: "relatorioArmario",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-armario",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("relatoriodearmarios");
		}
	},
};

export const relatorioCupomFiscal: PlataformaModuleItem = {
	id: "relatorioCupomFiscal",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-cupomfiscal",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("cupomfiscal");
		}
	},
};

export const relatorioProdutoComVigencia: PlataformaModuleItem = {
	id: "relatorioProdutoComVigencia",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-produtos-vigencia",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("relatoriodeprodutoscomvigencia");
		}
	},
};

export const relatorioPersonal: PlataformaModuleItem = {
	id: "relatorioPersonal",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-personal",
};

export const relatorioClientesComRestricoes: PlataformaModuleItem = {
	id: "relatorioClientesComRestricoes",
	parent: relatorioMenuParentItem.id,
	link: "/adm/relatorios/relatorio-clientes-com-restricao",
	showWhen: (permissoes?: Array<string>, modules?: Array<string>): boolean => {
		if (permissoes) {
			return permissoes.includes("utilizaGestaoClientesComRestricoes");
		}
	},
};

export const relatorioMenus: PlataformaModuleItem[] = [
	relatorioMenuParentItem,
	relatorioAgendamentos,
	relarioClientesAniversariantes,
	relatorioArmario,
	relatorioBvs,
	relatorioCardex,
	relatorioClienteSimplificado,
	relatorioCliente,
	relatorioClientesCancelados,
	relatorioClientesTrancados,
	relatorioClientesComAtestado,
	relatorioClientesComBonus,
	relatorioClienteComCobrancaAutomaticaBloqueada,
	relatorioClientesComRestricoes,
	relatorioClientesSemSessao,
	relatorioComissao,
	relatorioComissaoConsultor,
	relatorioComissaoProfessor,
	relatorioCompetenciaMensal,
	relatorioConsultaRecibos,
	relatorioConsultaTurma,
	relatorioContratosPorDuracao,
	relatorioControleLogs,
	relatorioConvidado,
	relatorioCupomFiscal,
	relatorioDescontoOcupacao,
	relatorioDiario,
	relatorioFaturamento,
	relatorioFaturamentoRecebido,
	relatorioFechamentoAcesso,
	relatorioFechamentoCaixa,
	relatorioFrequenciaOcupacao,
	relatorioFrequenciaTurmas,
	relatorioGeralCliente,
	relatorioGympass,
	relatorioHistoricoPonto,
	relatorioIndicadorAcesso,
	relatorioListaAcesso,
	relatorioListaChamada,
	relatorioMapaTurmas,
	relatorioMovimentoProduto,
	relatorioOrcamento,
	relatorioParcelas,
	relatorioPersonal,
	relatorioPesquisa,
	relatorioPrevisaoRenovacao,
	relatorioProdutoComVigencia,
	relatorioReceitaPorPeriodo,
	relatorioRepasse,
	relatorioSaldoContaCorrente,
	relatorioSaldoCredito,
	relatorioTotalizadorAcesso,
	relatorioTotalizadorTickets,
	relatorioTransacoesPix,
	relatorioClientesVisitantes,
];
