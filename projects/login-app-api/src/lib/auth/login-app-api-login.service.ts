import { HttpClient } from "@angular/common/http";
import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";

import { Observable, of } from "rxjs";
import { map, switchMap } from "rxjs/operators";

import { CryptoService } from "sdk";
import { LoginAppApiBaseService } from "../base/login-app-api-base.service";
import { ApiResponseList, ApiResponseSingle } from "../base/model/base.model";
import { LoginAppApiModule } from "../login-app-api.module";
import {
	AuthDTO,
	ConfiguracoesGeraisADMDTO,
	DadoAcesso,
	LoginUserData,
	UsuarioGeral,
} from "./auth.model";
import { ItemCampanha } from "./campanha.model";
import { EmpresaAcesso } from "./empresa-acesso.model";
import { ItemBanner } from "./item-banner.model";
import { LoginAppApiDataUserService } from "./login-app-api-data-user.service";
import { TrocaEmpresa } from "./troca-empresa.model";

@Injectable({
	providedIn: LoginAppApiModule,
})
export class LoginAppApiLoginService {
	constructor(
		private loginAppApiBaseService: LoginAppApiBaseService,
		private loginAppDataUser: LoginAppApiDataUserService,
		private criptoService: CryptoService,
		private sanitizer: DomSanitizer,
		private httpClient: HttpClient,
		@Inject(LOCALE_ID) private locale,
	) {
	}

	login(authDto: AuthDTO): Observable<{ result: boolean }> {
		const captcha = authDto.recaptchaToken;
		delete authDto.recaptchaToken;
		delete authDto.ip;
		const headers: any = {};
		if (captcha) {
			headers["g-token"] = captcha;
		}
		return this.loginAppApiBaseService
			.post<ApiResponseSingle<LoginUserData>>("prest/login/v2/login", authDto, {
				headers,
				params: {
					ip: localStorage.getItem("ip-login"),
				},
			})
			.pipe(
				map((response) => {
					localStorage.setItem("apiToken", response.content.token);
					this.loginAppDataUser.userData = response.content.dados;
					return { result: true };
				}),
			);
	}

	loginAzAd(token: string): Observable<{ result: boolean }> {
		const headers: any = {};
		return this.loginAppApiBaseService
			.post<ApiResponseSingle<LoginUserData>>(
				"prest/login/v2/loginAzAd",
				token,
				{
					headers,
				},
			)
			.pipe(
				map((response) => {
					localStorage.setItem("apiToken", response.content.token);
					this.loginAppDataUser.userData = response.content.dados;
					return { result: true };
				}),
			);
	}

	getUrlAzAd(chave: string): Observable<{ result: string }> {
		const headers: any = {};
		return this.loginAppApiBaseService
			.post<ApiResponseSingle<ConfiguracoesGeraisADMDTO>>(
				"prest/login/v2/apresentarAzAd",
				"",
				{
					headers,
					params: {
						chave: chave,
					},
				},
			)
			.pipe(
				map((response) => {
					if (response.content) {
						return {
							result:
								"https://launcher.myapps.microsoft.com/api/signin/" +
								response.content.azureadclientid +
								"?tenantId=" +
								response.content.azureadtenatid,
						};
					} else {
						return { result: null };
					}
				}),
			);
	}

	obterUrls(
		idUserGeral: string,
		dadoAcesso: DadoAcesso,
	): Observable<ApiResponseSingle<DadoAcesso>> {
		dadoAcesso.token = localStorage.getItem("apiToken");
		return this.loginAppApiBaseService.post<ApiResponseSingle<DadoAcesso>>(
			`prest/login/v2/urls/${idUserGeral}`,
			dadoAcesso,
			{
				Authorization: `Bearer ${localStorage.getItem("apiToken")}`,
			},
		);
	}

	resetPassword(
		email: string,
		gToken,
		usuarioGeral,
	): Observable<ApiResponseSingle<any>> {
		const headers: any = {};
		if (gToken) {
			headers["g-token"] = gToken;
		}
		return this.loginAppApiBaseService.post<ApiResponseSingle<any>>(
			"prest/login/v2/senha/nova",
			{
				email,
				usuarioGeral,
				novoLogin: true,
				ip: localStorage.getItem("ip-login"),
			},
			{
				headers,
			},
		);
	}

	campanhas(): Observable<ApiResponseList<ItemCampanha>> {
		return this.httpClient
			.get(`https://ipwho.is/${localStorage.getItem("ip-login")}`)
			.pipe(
				switchMap((response: any) => {
					return this.loginAppApiBaseService.get<ApiResponseList<ItemCampanha>>(
						"prest/login/v2/campanha",
						{
							params: {
								linguagem: this.locale,
								pais: response.country_code,
								estado: response.region_code,
							},
						},
					);
				}),
			);
	}

	bannerLogin(
		chaveEmpresa: string,
		codigoEmpresa: number,
	): Observable<ApiResponseList<ItemBanner>> {
		let url = "prest/banner?banner=BANNER_LOGIN";
		if (chaveEmpresa) {
			url += `&chave=${chaveEmpresa}`;
		}
		if (codigoEmpresa) {
			url += `&empresa=${codigoEmpresa}`;
		}
		return this.loginAppApiBaseService
			.get<ApiResponseList<ItemBanner>>(url)
			.pipe(
				switchMap((bannersLogin) => {
					if (bannersLogin.content && bannersLogin.content.length > 0) {
						bannersLogin.content.forEach(
							(item) =>
								(item.safeImagem = this.sanitizer.bypassSecurityTrustStyle(
									`url(${item.imagem})`,
								)),
						);
						return of(bannersLogin);
					}
					url = "prest/banner?banner=BANNER_ZW_TELA1";
					if (chaveEmpresa) {
						url += `&chave=${chaveEmpresa}`;
					}
					if (codigoEmpresa) {
						url += `&empresa=${codigoEmpresa}`;
					}
					return this.loginAppApiBaseService
						.get<ApiResponseList<ItemBanner>>(url)
						.pipe(
							map((bannerZw) => {
								bannerZw.content.forEach(
									(item) =>
										(item.safeImagem = this.sanitizer.bypassSecurityTrustStyle(
											`url(${item.imagem})`,
										)),
								);
								return bannerZw;
							}),
						);
				}),
			);
	}

	ativarConta(token: string): Observable<any> {
		return this.executeTokenAction({
			token,
		});
	}

	trocarEmailVinculaDados(token: string, novaSenha?: string): Observable<any> {
		return this.executeTokenAction({
			token,
			novaSenha,
			trocaEmailVincularDados: true,
		});
	}

	validateToken(token: string): Observable<any> {
		return this.loginAppApiBaseService.get(`prest/login/v2/token/${token}`);
	}

	changePassword(token: string, newPassword: string): Observable<any> {
		return this.executeTokenAction({
			token,
			novaSenha: newPassword,
		});
	}

	alterarEmail(
		token: string,
		email: string,
	): Observable<ApiResponseSingle<any>> {
		return this.executeTokenAction({
			token,
			email,
		});
	}

	private executeTokenAction(body) {
		if (!body) {
			throw Error("Cannot proced because the body is undefined");
		}
		body.ip = localStorage.getItem("ip-login");
		body.origem = "NOVO_LOGIN";
		return this.loginAppApiBaseService.post("prest/login/v2/token", body);
	}

	getUsuarioGeralSimples(
		authDto: AuthDTO,
	): Observable<ApiResponseSingle<UsuarioGeral>> {
		const captcha = authDto.recaptchaToken;
		const ip = authDto.ip;
		delete authDto.recaptchaToken;
		delete authDto.ip;
		const headers: any = {};
		if (captcha) {
			headers["g-token"] = captcha;
		}
		const bodyCrip = this.criptoService.encrypt(JSON.stringify(authDto));
		const url = "prest/usuario-geral/v1/simples";
		return this.loginAppApiBaseService.post<ApiResponseSingle<UsuarioGeral>>(
			url,
			{ data: bodyCrip },
			{
				headers,
				params: {
					ip,
				},
			},
		);
	}

	public obterUsuarioGeral(
		chave,
		codUsuarioZW,
		loggedUser,
	): Observable<ApiResponseSingle<string>> {
		const url = "prest/usuario-geral/v1/obter";
		return this.loginAppApiBaseService.get<ApiResponseSingle<string>>(
			url,
		);
	}

	obterUsuarioGeralPorEmail(
		email,
	): Observable<ApiResponseSingle<UsuarioGeral>> {
		const body = { username: email };
		const bodyCrip = this.criptoService.encrypt(JSON.stringify(body));
		const url = "prest/usuario-geral/v1/obter-email";
		return this.loginAppApiBaseService.post<ApiResponseSingle<UsuarioGeral>>(
			url,
			{ data: bodyCrip },
		);
	}

	public obterEmpresas(
		usuarioGeral: string,
	): Observable<ApiResponseList<EmpresaAcesso>> {
		let url = `prest/usuario-geral/v1/empresas`;
		if (usuarioGeral) {
			url = `prest/usuario-geral/v1/empresas/${usuarioGeral}`;
		}
		return this.loginAppApiBaseService.get<ApiResponseList<EmpresaAcesso>>(
			url,
		);
	}

	public validarTrocaEmpresa(
		usuarioGeral: string,
		troca: TrocaEmpresa,
	): Observable<ApiResponseSingle<string>> {
		const params = { md: "NTR" };

		const url = `prest/login/v2/empresas/validar/${usuarioGeral}/${troca.empresaAcesso.tk}`;
		return this.loginAppApiBaseService.get<ApiResponseSingle<string>>(url, {
			params,
		});
	}

	public createSession(
		usuarioGeralId: string,
		dadosAcesso: DadoAcesso,
	): Observable<ApiResponseSingle<DadoAcesso>> {
		const headers = {
			"User-Agent": navigator.userAgent,
		};
		const body = {
			usuarioGeralId,
			dadosAcesso,
		};
		const bodyCrip = this.criptoService.encrypt(JSON.stringify(body));
		const url = `prest/usuario-geral/v1/session/create`;
		return this.loginAppApiBaseService.post<ApiResponseSingle<DadoAcesso>>(
			url,
			{ data: bodyCrip },
			headers,
		);
	}

	public updateDadoAcesso(dadoAcesso: DadoAcesso): void {
		let userData: UsuarioGeral = this.loginAppDataUser.userDataLocal;
		const existingDadoAcesso = userData.dadosAcesso.find(
			(d) =>
				d.chave === dadoAcesso.chave &&
				d.codigoEmpresa === dadoAcesso.codigoEmpresa,
		);
		if (existingDadoAcesso) {
			Object.assign(existingDadoAcesso, dadoAcesso);
		}
		this.loginAppDataUser.userData = userData;
	}
}
