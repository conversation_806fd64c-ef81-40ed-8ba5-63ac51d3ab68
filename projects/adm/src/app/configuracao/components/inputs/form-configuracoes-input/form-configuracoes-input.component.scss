.config-title {
	font-size: 14px;
	font-style: normal;
	font-weight: 700;
	line-height: 200%;
	letter-spacing: 0.25px;
	color: #55585e;
}

.config-description {
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
	text-align: justify;
	color: #797d86;
}

.row-config-no-description {
	padding: 0;

	.row-grid-actions {
		display: flex;
		justify-content: flex-end;
		padding-right: 15px;
	}
}

.config-title-no-description {
	font-size: 14px;
	font-style: normal;
	font-weight: 700;
	line-height: 525%;
	letter-spacing: 0.25px;
	color: #55585e;
}

.row-config {
	padding: 16px 0px 16px 0px;
	border-bottom: 1px solid #c9cbcf;

	.row-grid-actions {
		display: flex;
		justify-content: flex-end;
		padding-right: 5px;
	}

	.row-grid-title,
	.row-grid,
	.row-grid-actions {
		padding-top: 32px;
	}

	.row-grid-title {
		display: flex;
		flex-direction: column;

		.config-title {
			font-size: 14px;
			font-style: normal;
			font-weight: 600;
			line-height: 200%;
			letter-spacing: 0.25px;
		}
	}

	.row-grid {
		display: grid;

		.grid-container {
			border-radius: 8px;
			border: 1px solid var(--support-gray-3, #c9cbcf);
			padding: 16px;
		}
	}

	::ng-deep.table-content {
		overflow: auto;
		padding: 0px;
		width: 100%;

		.table {
			width: 100%;
		}
	}
}

.row-grid-actions {
	display: flex;
	justify-content: flex-end;
}

.col-text {
	padding-left: 0px;
}

.config-children {
	.row-config {
		background-color: #fafafa;
		padding-left: 40px;
		border-bottom: none;
	}
}

.divider {
	width: 10px;
	height: auto;
	display: inline-block;
}

.input-periodo {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;

	pacto-base-input-form-configuracoes {
		width: 48.2%;
		font-size: 14px;

		::ng-deep pacto-cat-form-input {
			.nome {
				visibility: initial !important;
				font-weight: 600;
				font-size: 14px;
				color: #51555a;
			}
		}

		.nome {
			visibility: initial !important;
			font-weight: 600;
			color: #51555a;
			font-size: 14px;
		}
	}
}

pacto-base-input-form-configuracoes {
	::ng-deep pacto-cat-datepicker {
		.nome {
			font-weight: 600;
			color: #51555a;
			font-size: 14px;
		}
	}

	::ng-deep pacto-cat-button {
		.full {
			width: auto !important;
			float: right;
			padding: 0 20px !important;
		}
	}
}

pacto-base-input-form-configuracoes[ng-reflect-label="Reprocessar"],
pacto-base-input-form-configuracoes[label="Reprocessar"] {
	width: 100% !important;
}

pacto-base-input-form-configuracoes {
	::ng-deep
		pacto-cat-datepicker[ng-reflect-label="Iniciar processo a partir de"]
		.nome,
	::ng-deep pacto-cat-datepicker[label="Iniciar processo a partir de"] .nome {
		visibility: hidden;
	}

	::ng-deep pacto-cat-datepicker[ng-reflect-label="Data de pagamento"] .nome,
	::ng-deep pacto-cat-datepicker[label="Data de pagamento"] .nome {
		visibility: hidden;
	}
}

span.nome {
	font-weight: bolder;
	color: #55585e;
	font-size: 15px;
	padding-bottom: 2px;
}

::ng-deep pacto-cat-form-input {
	.nome {
		visibility: hidden !important;
		height: 15px;
	}
}

::ng-deep pacto-base-input-form-configuracoes.isVisible {
	.nome {
		visibility: visible !important;
		height: auto !important;
		font-weight: 600;
		color: #55585e;
	}
}

.link-class {
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
	color: #797d86;
	text-decoration: none; /* opcional, remove sublinhado */
	white-space: nowrap; /* impede quebra de linha se desejar */
	display: inline-block; /* permite aplicar padding/margem */
}

.link-class:hover {
	color: #4a90e2;
	text-decoration: underline;
	cursor: pointer;
}
