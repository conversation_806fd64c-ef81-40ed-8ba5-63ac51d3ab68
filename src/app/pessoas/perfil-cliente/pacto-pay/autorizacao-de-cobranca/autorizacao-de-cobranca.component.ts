import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import * as moment from "moment";
import { AlunoPactoPay } from "ms-pactopay-api";
import { PactoDataGridConfig, PactoModalRef, PactoModalSize } from "ui-kit";
import { RelatorioCobrancaComponent } from "../../../../cobranca/components/relatorio-cobranca/relatorio-cobranca.component";
import { FormaDeCobrancaComponent } from "./forma-de-cobranca/forma-de-cobranca.component";
import { ModalCobrancaAutomaticaComponent } from "./modal-cobranca-automatica/modal-cobranca-automatica.component";
import { ClienteDadosPessoais } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { PerfilRecursoPermissoTipo } from "../../../../../../projects/sdk/src/lib/services/models/perfil-acesso-recurso.model";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-autorizacao-de-cobranca",
	templateUrl: "./autorizacao-de-cobranca.component.html",
	styleUrls: ["./autorizacao-de-cobranca.component.scss"],
})
export class AutorizacaoDeCobrancaComponent implements OnInit, AfterViewInit {
	@Input() aluno: AlunoPactoPay;
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() autorizada: boolean;
	@Output() hasDataCobranca = new EventEmitter();
	@ViewChild("relatorio", { static: true })
	relatorio: RelatorioCobrancaComponent;
	@ViewChild("tipoAutorizacao", { static: true })
	tipoAutorizacao: TemplateRef<any>;
	@ViewChild("tipoDeParcela", { static: true }) tipoDeParcela: TemplateRef<any>;
	public relatorioGridConfig: PactoDataGridConfig;
	dataCobranca = false;
	permissaoCliente5_73: any;

	constructor(
		private rest: RestService,
		private route: ActivatedRoute,
		private modalService: ModalService,
		private cd: ChangeDetectorRef,
		private readonly session: SessionService,
		private readonly notify: SnotifyService
	) {}

	ngOnInit() {
		this.initGridConfigRelatorio();
	}

	ngAfterViewInit(): void {
		this.permissaoCliente5_73 =
			this.session.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "5.73"
			);
	}

	private initGridConfigRelatorio() {
		this.relatorioGridConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay(
				`aluno/autorizacoes?matricula=${this.route.snapshot.params["aluno-matricula"]}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.dataCobranca = serveData.totalElements > 0 ? true : false;
				if (this.dataCobranca) {
					this.hasDataCobranca.emit(true);
				}
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "tipo",
					titulo: "Forma de cobrança",
					celula: this.tipoAutorizacao,
					visible: true,
					ordenavel: false,
					width: "30%",
				},
				{
					nome: "tipoCobrar",
					titulo: "Tipo de parcela",
					celula: this.tipoDeParcela,
					visible: true,
					ordenavel: false,
					width: "25%",
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
					width: "25%",
				},
				{
					nome: "empresa",
					titulo: "Empresa",
					visible: true,
					ordenavel: false,
					width: "15%",
				},
			],
		});
	}

	public reiniciarRelatorio(res) {
		if (
			res.status === "Salvo com sucesso!" ||
			res.status === "Excluído com sucesso!"
		) {
			this.relatorio.reloadData();
			this.cd.detectChanges();
		}
	}

	public openModalNovaFormaDeCobranca() {
		if (!this.permiteExcluirAutorizacao()) {
			this.notify.error(
				'Você não possui a permissão "5.73 - Cadastro de autorização de cobrança cliente (Incluir)"',
				{
					timeout: undefined,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const modal: PactoModalRef = this.modalService.open(
			"Nova forma de cobrança",
			FormaDeCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.editar = false;
		modal.componentInstance.pessoa = this.aluno.pessoa;
		modal.componentInstance.empresa = this.dadosPessoais.empresa.codigo;
		modal.componentInstance.possuiIdVindi = this.dadosPessoais.possuiIdVindi;
		modal.componentInstance.idVindi = this.dadosPessoais.idVindi;
		modal.componentInstance.sendModificacao.subscribe((res) =>
			this.reiniciarRelatorio(res)
		);
	}

	permiteExcluirAutorizacao(): any {
		const permition = this.permissaoCliente5_73;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	public openModalCobrancaAutomatica() {
		const modal: PactoModalRef = this.modalService.open(
			this.autorizada
				? "Bloquear cobrança automática"
				: "Autorizar cobrança automática",
			ModalCobrancaAutomaticaComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.autorizada = this.autorizada;
		modal.componentInstance.pessoa = this.aluno.pessoa;
		modal.componentInstance.cobrancaAutomatica.subscribe((res) => {
			if (res.status === "bloqueada") {
				this.aluno.data_bloqueio_cobranca_automatica = res.data
					? moment(res.data).format("DD/MM/YYYY")
					: moment(new Date()).format("DD/MM/YYYY");
				this.autorizada = false;
			} else {
				this.autorizada = true;
			}
			this.cd.detectChanges();
		});
	}
}
