import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	CadastroAuxApiCidadeService,
	CadastroAuxApiEstadoService,
	CadastroAuxApiPaisService,
	Cidade,
	Estado,
	Pais,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AddPaisComponent } from "../add-pais/add-pais.component";

@Component({
	selector: "adm-cidade-form",
	templateUrl: "./cidade-form.component.html",
	styleUrls: ["./cidade-form.component.scss"],
})
export class CidadeFormComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	page = 0;
	codigoControl: FormControl = new FormControl();
	cidade: Cidade = new Cidade();
	paisArray: Array<Pais> = new Array<Pais>();
	estadoArray: Array<Estado> = new Array<Estado>();
	form: FormGroup;
	id;

	constructor(
		private router: Router,
		private cidadeService: CadastroAuxApiCidadeService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private estadoService: CadastroAuxApiEstadoService,
		private paisService: CadastroAuxApiPaisService,
		private dialogService: DialogService,
		private notifyService: SnotifyService
	) {}

	paisSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	estadoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	ngOnInit() {
		this.codigoControl.disable();
		this.createForm();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.loadPais();

		this.form.get("pais").valueChanges.subscribe((value) => {
			this.loadEstados(value.codigo);
		});

		if (this.id) {
			this.cidadeService.find(this.id).subscribe((response) => {
				this.cidade = response.content;
				this.codigoControl.setValue(this.cidade.codigo);
				this.form.patchValue({
					nome: this.cidade.nome,
					pais: this.cidade.pais,
					estado: this.cidade.estado,
					codigoMunicipio: this.cidade.codigoMunicipio,
					homologada: this.cidade.homologada,
				});
			});
		}
	}

	createForm() {
		this.form = new FormGroup({
			nome: new FormControl(),
			pais: new FormControl(),
			estado: new FormControl(),
			codigoMunicipio: new FormControl(),
			homologada: new FormControl(),
		});
	}

	salvarCidade() {
		Object.keys(this.form.getRawValue()).forEach((key) => {
			this.cidade[key] = this.form.getRawValue()[key];
		});
		this.cidadeService.save(this.cidade).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("SAVE_MSG"));
				this.voltarListagem();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	loadEstados(paisCodigo) {
		if (paisCodigo) {
			this.estadoService.findAllCodName(paisCodigo).subscribe(
				(response) => {
					this.estadoArray = response.content;
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notifyService.error(this.traducao.getLabel("PAIS_ERROR_MSG"));
		}
	}

	loadPais() {
		this.paisService.findAllCodName().subscribe(
			(response) => {
				this.paisArray = response.content;
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	addPais() {
		const dialogRef = this.dialogService.open(
			this.traducao.getLabel("NOVO_PAIS"),
			AddPaisComponent,
			PactoModalSize.LARGE
		);
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {
					this.form.get("pais").setValue(value);
				})
				.catch((error) => {});
		}
	}

	voltarListagem() {
		this.router.navigate(["adm", "cad-aux", "cidade"]);
	}
}
