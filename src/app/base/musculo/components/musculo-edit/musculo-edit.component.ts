import { Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { ListaInsertSelectFilterComponent } from "old-ui-kit";
import { FormGroup, Validators } from "@angular/forms";
import {
	TreinoApiAtividadeService,
	AtividadeBase,
	GrupoMuscular,
	Musculo,
	TreinoApiGrupoMuscularService,
	MusculoEdit,
	TreinoApiMusculoService,
} from "treino-api";

@Component({
	selector: "pacto-musculo-edit",
	templateUrl: "./musculo-edit.component.html",
	styleUrls: ["./musculo-edit.component.scss"],
})
export class MusculoEditComponent implements OnInit {
	@ViewChild("atividadesComp", { static: true })
	atividadesComp: ListaInsertSelectFilterComponent;
	@ViewChild("gruposMusc", { static: true })
	gruposMusc: ListaInsertSelectFilterComponent;
	@ViewChild("mensagemSucesso", { static: true }) mensagemSucesso;
	@ViewChild("nomeError", { static: true }) nomeError;

	formGroup: FormGroup = new FormGroup({});
	operation: string;
	entity: Musculo;

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private snotifyService: SnotifyService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private atividadesService: TreinoApiAtividadeService,
		private musculoService: TreinoApiMusculoService
	) {}

	gruposMusculares: Array<GrupoMuscular> = [];
	atividades: Array<AtividadeBase> = [];

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}
		});
	}

	private loadEntities(id) {
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.gruposMusculares = dados;
			});
		this.atividadesService.obterTodasAtividades("false").subscribe((dados) => {
			this.atividades = dados;
			this.atividades.map((atividade: any, index: number) => {
				atividade.index = index;
			});
		});
		if (id) {
			this.musculoService.obterMusculo(id).subscribe((dado) => {
				this.entity = dado;
				this.loadForm();
			});
		}
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.entity.nome);
		this.atividadesComp.setSelectedItens(this.entity.atividades);
		this.gruposMusc.setSelectedItens(this.entity.grupoMuscular);
	}

	cancelHandler() {
		this.router.navigate(["treino", "cadastros", "musculo"], {
			queryParamsHandling: "merge",
		});
	}

	private createHandler() {
		const mensagem = this.mensagemSucesso.nativeElement.innerHTML;
		if (this.formGroup.valid) {
			const data = this.fetchFormData();
			this.musculoService.criarMusculo(data).subscribe(() => {
				this.snotifyService.success(mensagem);
				this.router.navigate(["treino", "cadastros", "musculo"], {
					queryParamsHandling: "merge",
				});
			});
		} else {
			const nomeError = this.nomeError.nativeElement.innerHTML;
			this.snotifyService.error(nomeError);
		}
	}

	private udpateHandler() {
		const mensagem = this.mensagemSucesso.nativeElement.innerHTML;
		if (this.formGroup.valid) {
			const data = this.fetchFormData();
			this.musculoService.atualizarMusculo(data).subscribe(() => {
				this.snotifyService.success(mensagem);
				this.router.navigate(["treino", "cadastros", "musculo"], {
					queryParamsHandling: "merge",
				});
			});
		} else {
			const nomeError = this.nomeError.nativeElement.innerHTML;
			this.snotifyService.error(nomeError);
		}
	}

	private fetchFormData() {
		const formData: MusculoEdit = {
			nome: this.formGroup.get("nome").value,
			atividadeIds: this.atividadesComp.getSelectedIds(),
			grupoMuscularIds: this.gruposMusc.getSelectedIds(),
		};

		if (this.entity) {
			formData.id = this.entity.id;
		}

		return formData;
	}

	submitHandler() {
		this.markAsTouched();
		if (this.entity) {
			this.udpateHandler();
		} else {
			this.createHandler();
		}
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
	}

	get Validators() {
		return Validators;
	}
}
