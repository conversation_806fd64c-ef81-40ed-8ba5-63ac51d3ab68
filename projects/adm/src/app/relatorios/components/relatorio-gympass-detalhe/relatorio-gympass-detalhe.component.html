<div class="content-wrapper">
	<div class="card-table" style="margin-top: 32px">
		<span class="title" i18n="@@relatorio-gympass:dados-cliente">
			Dados do cliente
		</span>
		<div class="divider"></div>
		<table>
			<tbody>
				<tr>
					<td>
						<div class="conteudo-td">
							<span
								class="title"
								i18n="@@relatorio-gympass-detalhe:column-matricula">
								Matrícula:
							</span>
							<p>{{ relatorioGymPass.matricula }}</p>
						</div>
					</td>
					<td class="col-2">
						<span
							class="title"
							i18n="@@relatorio-gympass-detalhe:column-nome-cliente">
							Nome cliente:
						</span>
						<p>{{ relatorioGymPass.nome }}</p>
					</td>
					<td>
						<span
							class="title"
							i18n="@@relatorio-gympass-detalhe:column-nascimento">
							Nascimento:
						</span>
						<p>{{ relatorioGymPass.dataNasc }}</p>
					</td>
					<td>
						<span class="title" i18n="@@relatorio-gympass-detalhe:column-email">
							E-mail:
						</span>
						<p>{{ relatorioGymPass.email }}</p>
					</td>
				</tr>
				<tr>
					<td class="telefone">
						<span
							class="title"
							i18n="@@relatorio-gympass-detalhe:column-telefone">
							Telefone:
						</span>
						<p>{{ relatorioGymPass.telefone }}</p>
					</td>
					<td class="col-2">
						<span
							class="title"
							i18n="@@relatorio-gympass-detalhe:column-empresa">
							Empresa:
						</span>
						<p>{{ relatorioGymPass.empresa }}</p>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>

<div class="card-historico">
	<div class="historico">
		<span
			class="title"
			i18n="@@relatorio-gympass-detalhe:column-historico-acesso-gympass">
			Histórico de acesso Gympass
		</span>
		<div class="divider"></div>
	</div>
	<div class="table-historico">
		<pacto-relatorio
			#tableHistoricoAcessoGymComponent
			[itensPerPage]="itensPerPage"
			[showShare]="false"
			[table]="tableHistoricoAcessoGym"></pacto-relatorio>
	</div>
</div>

<ng-template #columnInicioAcesso>
	<span i18n="@@relatorio-gympass-detalhe:column-inicio-acesso">
		Início do acesso
	</span>
</ng-template>
<ng-template #columnFinalAcesso>
	<span i18n="@@relatorio-gympass-detalhe:column-final-acesso">
		Final do acesso
	</span>
</ng-template>
<ng-template #columnTipoAcesso>
	<span i18n="@@relatorio-gympass-detalhe:column-tipo-acesso">
		Tipo de acesso
	</span>
</ng-template>
