<pacto-integration-item-checkbox
	*ngIf="!integracao.configMQV"
	[config]="integracao"></pacto-integration-item-checkbox>

<ng-container *ngIf="integracao.config">
	<div class="block-table">
		<span class="text-table">
			Configure abaixo o código Gym ID de cada unidade
		</span>
		<table class="table">
			<thead>
				<tr>
					<th scope="col">UNIDADE</th>
					<th scope="col">
						<div class="col-gym">GYM ID</div>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr
					*ngFor="
						let integracao of items
							| slice : (page - 1) * pageSize : page * pageSize;
						index as id
					">
					<td>
						<div class="unidade">
							{{ integracao.nome }}
						</div>
					</td>
					<td>
						<input
							[(ngModel)]="integracao.codigoGymPass"
							[id]="integracao.codigo"
							class="input-gymId"
							type="text" />
					</td>
				</tr>
			</tbody>
		</table>
		<div class="line"></div>

		<div class="footer-pagination">
			<div class="results">
				Mostrando {{ pageSize > items.length ? items.length : pageSize }} de
				{{ items.length }}
			</div>
			<pacto-cat-select
				[control]="pageSizeControl"
				[items]="[
					{ id: 10, label: '10' },
					{ id: 20, label: '20' },
					{ id: 30, label: '30' }
				]"
				[size]="'SMALL'"
				class="select-pageSize"></pacto-cat-select>
			<!--
        <input class="input-pageSize"
        type="number"
        min="1"
        max="10"
        [(ngModel)]="pageSize"
        onkeydown="return false">
      -->
			<ngb-pagination
				[(page)]="page"
				[collectionSize]="listId.length"
				[pageSize]="pageSize"></ngb-pagination>
		</div>
		<!--<div class="footer-info">
      <div class="footer">
        {{listAulasSpivi.length}} {{ listAulasSpivi.length === 1 ? 'resultado' : 'resultados'}} encontrados
      </div>
    </div>-->
	</div>
</ng-container>

<ng-container *ngIf="integracao.configMGB">
	<div class="block-table">
		<span class="text-table">Configure abaixo o token de cada unidade</span>
		<table class="table">
			<thead>
				<tr>
					<th scope="col">UNIDADE</th>
					<th scope="col">
						<div class="col-gym">TOKEN</div>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr
					*ngFor="
						let integracao of items
							| slice : (page - 1) * pageSize : page * pageSize;
						index as id
					">
					<td>
						<div class="unidade">
							{{ integracao.nome }}
						</div>
					</td>
					<td>
						<input
							[(ngModel)]="integracao.token"
							[id]="integracao.codigo"
							class="input-gymId"
							type="text" />
					</td>
				</tr>
			</tbody>
		</table>
		<div class="line"></div>

		<div class="footer-pagination">
			<div class="results">
				Mostrando {{ pageSize > items.length ? items.length : pageSize }} de
				{{ items.length }}
			</div>
			<pacto-cat-select
				[control]="pageSizeControl"
				[items]="[
					{ id: 10, label: '10' },
					{ id: 20, label: '20' },
					{ id: 30, label: '30' }
				]"
				[size]="'SMALL'"
				class="select-pageSize"></pacto-cat-select>
			<!--
        <input class="input-pageSize"
        type="number"
        min="1"
        max="10"
        [(ngModel)]="pageSize"
        onkeydown="return false">
      -->
			<ngb-pagination
				[(page)]="page"
				[collectionSize]="listId.length"
				[pageSize]="pageSize"></ngb-pagination>
		</div>
		<!--<div class="footer-info">
      <div class="footer">
        {{listAulasSpivi.length}} {{ listAulasSpivi.length === 1 ? 'resultado' : 'resultados'}} encontrados
      </div>
    </div>-->
	</div>
</ng-container>

<ng-container *ngIf="integracao.configMQV">
	<div class="block-table-mqv">
		<span class="text-table">Configure abaixo o token de cada unidade</span>
		<table class="table">
			<thead>
				<tr>
					<th scope="col">UNIDADE</th>
					<th scope="col">
						<div class="col-gym">TOKEN</div>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr
					*ngFor="
						let integracao of items
							| slice : (page - 1) * pageSize : page * pageSize;
						index as id
					">
					<td>
						<div class="unidade">
							{{ integracao.nome }}
						</div>
					</td>
					<td>
						<input
							[(ngModel)]="integracao.token"
							[id]="integracao.codigo"
							class="input-gymId"
							type="text" />
					</td>
				</tr>
			</tbody>
		</table>
		<div class="line"></div>

		<div class="footer-pagination">
			<div class="results">
				Mostrando {{ pageSize > items.length ? items.length : pageSize }} de
				{{ items.length }}
			</div>
			<pacto-cat-select
				[control]="pageSizeControl"
				[items]="[
					{ id: 10, label: '10' },
					{ id: 20, label: '20' },
					{ id: 30, label: '30' }
				]"
				[size]="'SMALL'"
				class="select-pageSize"></pacto-cat-select>

			<ngb-pagination
				[(page)]="page"
				[collectionSize]="listId.length"
				[pageSize]="pageSize"></ngb-pagination>
		</div>
	</div>
</ng-container>

<div class="block-info">
	<pacto-cat-button
		(click)="cancelHandler()"
		[full]="true"
		[label]="'Cancelar'"
		[type]="'OUTLINE'"></pacto-cat-button>

	<pacto-cat-button
		(click)="saveHandler()"
		[full]="true"
		[label]="'Salvar'"></pacto-cat-button>
</div>

<span
	#configSuccess
	[hidden]="true"
	i18n="@@configuration-sistema-treino:config-salva">
	Configurações salvas com sucesso
</span>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="erro_informe_gymid">Informe o Gym ID.</span>
</pacto-traducoes-xingling>
