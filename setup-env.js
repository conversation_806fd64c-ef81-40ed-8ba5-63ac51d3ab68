const inquirer = require('inquirer');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.addArgument(['-du', '--discovery_url']);
parser.addArgument(['-enl', '--enable_new_login']);
parser.addArgument(['-elc', '--enable_login_captcha']);
parser.addArgument(['-em', '--enable_marketing']);

const args = parser.parseArgs();
const qtdArgs = Object.keys(args).length;
const argsNotInformed = Object.keys(args).filter(k => args[k] === null);

const environmentFilePaths = [
  path.resolve('src', 'environments', 'environment.ts'),
  path.resolve('projects', 'adm', 'src', 'environments', 'environment.ts'),
  path.resolve('projects', 'crm', 'src', 'environments', 'environment.ts'),
  path.resolve('projects', 'login', 'src', 'environments', 'environment.ts'),
  path.resolve('projects', 'marketing', 'src', 'environments', 'environment.ts'),
  path.resolve('projects', 'sdk', 'src', 'lib' , 'environments', 'environment.ts')
];

if (argsNotInformed.length === qtdArgs) {

  const discoveryUrls = [
    {
      value:'Swarm Squad Migração',
      key: 'http://squad-migracao.pactosolucoes.com.br:8101'
    },
    {
      value:'host.docker.internal',
      key: 'http://host.docker.internal:8101'
    },
    {
      value:'Docker local',
	  key: 'http://localhost:8101'
    },
	{
		value:'Produção',
		key: 'https://discovery.ms.pactosolucoes.com.br'
	  }
  ];

	const languages = [
		{
			value: 'Português',
			key: 'pt'
		},
		{
			value: 'Espanhol',
			key: 'es'
		},
		{
			value: 'Inglês',
			key: 'en'
		}
	];

	inquirer
		.prompt([
			{
				type: 'list',
				name: 'discovery',
				message: 'Qual a url do discovery você quer utilizar?',
				choices: discoveryUrls
			}
		])
		.then(async answers => {
			const discovery = discoveryUrls.find(
				env => env.value === answers.discovery
			);
			setDiscovery(`'${discovery.key}'`);
		});
} else {
	if (argsNotInformed.length > 0) {
		console.log(
			`Params not informed: ${argsNotInformed.map((v, i) =>
				argsNotInformed.length - 1 === i ? v : `${v}, `
			)}`
		);
	}
	setDiscovery(
		`'${args.discovery_url}'`,
		args.enable_new_login,
		args.enable_login_captcha,
		args.enable_marketing
	);
}

function setDiscovery(
	discoveryURl,
	enableLogin,
	enableCaptcha,
	enableMarketing
) {
	environmentFilePaths.forEach(environmentFilePath => {
		fs.readFile(environmentFilePath, 'utf8', (err, data) => {
			if (err) {
				return console.error(
					chalk.red(`Falha ao ler o arquivo ${environmentFilePath}`)
				);
			}
			let replaced = data;
			if (discoveryURl !== undefined) {
				replaced = replaced.replace(
					/discoveryMsUrl:.*|'discoveryMsUrl'.*/,
					`discoveryMsUrl: ${discoveryURl},`
				);
			}

			if (enableLogin !== undefined) {
				replaced = replaced.replace(
					/newLoginEnabled:.*|'newLoginEnabled'.*/,
					`newLoginEnabled: ${enableLogin},`
				);
			}

			if (enableCaptcha !== undefined) {
				replaced = replaced.replace(
					/enableCaptcha:.*|'enableCaptcha'.*/,
					`enableCaptcha: ${enableCaptcha},`
				);
			}

			if (enableMarketing !== undefined) {
				replaced = replaced.replace(
					/enableMarketing:.*|'enableMarketing'.*/,
					`enableMarketing: ${enableMarketing},`
				);
			}

			fs.writeFile(environmentFilePath, replaced, 'utf8', err => {
				if (err) {
					return console.error(
						chalk.red(
							`Falha ao alterar o arquivo ${environmentFilePath}`
						)
					);
				}

				console.info(
					chalk.green(
						`Configurações aplicadas para o arquivo: ${environmentFilePath}`
					)
				);
			});
		});
	});
}
