import { Injectable } from "@angular/core";
import { MenuConfigService } from "../../menu-config.service";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { Observable, of } from "rxjs";
import { PlatformMenuItem } from "../../../../models";
import { PermissaoService } from "../../../../permissao/permissao.service";

@Injectable({
	providedIn: "root",
})
export class FinBiService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.biFinMenuItem, this.biParent]);
	}

	get biParent(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "bi",
			configMenuExplorar: {
				submenus: [this.biFinMenuItem],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get biFinMenuItem(): PlatformMenuItem {
		return {
			id: "biFin",
			permissaoAdm: "9.27 - BI Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.27"),
			favoriteIdentifier: "FIN_BI",
			route: {
				queryParams: {
					funcionalidadeNome: "FIN_BI",
				},
			},
		};
	}
}
