<div class="edicao-parcelas">
	<div>
		<table>
			<tr
				*ngFor="let row of parcelas; let even = even; let idxColuna = index"
				[ngClass]="{ 'zebra-row': even }">
				<td *ngFor="let item of row; let idxLinha = index">
					<div [ngClass]="{ dig2: item.nrParcela > 9 }" class="parcela">
						<pacto-cat-form-input-number
							[decimal]="true"
							[formControl]="convertToFormControl(form.get(item.descricao))"
							decimalPrecision="2"></pacto-cat-form-input-number>
						<div class="prefix">{{ item.nrParcela }}ª - R$</div>
					</div>
				</td>
			</tr>
		</table>
	</div>

	<div class="atencao">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Atenção! Ao editar os valores das parcelas, evite adicionar ou remover
			modalidades, alterar a frequência semanal ou escolher outra forma de
			pagamento. Qualquer mudança desse tipo resultará na perda da negociação
			dos valores das parcelas, exigindo uma nova configuração.
		</span>
	</div>
	<div class="rodape-edicao-parcela">
		<div class="inner-div left">
			<span class="total-residuo total">
				<span>Total</span>
				<span class="valor verde">R$</span>
				<span class="valor residuo verde">
					{{ total | currency : "" : "" : "1.2-2" }}
				</span>
			</span>

			<span class="total-residuo total">
				<span>Resíduo</span>
				<span class="valor">R$</span>
				<span class="valor residuo">
					{{ residuo | currency : "" : "" : "1.2-2" }}
				</span>
			</span>
		</div>
		<div class="inner-div right">
			<pacto-cat-button
				(click)="limparEdicao()"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-caixa-aberto"
				label="Limpar alteração"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="confirmarEdicao()"
				[disabled]="residuo !== 0.0"
				i18n-label="@@label-confirmar-btn"
				id="btn-receber"
				label="Confirmar"
				size="LARGE"
				type="PRIMARY"
				width="100px"></pacto-cat-button>
		</div>
	</div>
</div>
