import { Directive, TemplateRef, OnInit, Input } from "@angular/core";

@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "ng-template[pactoTabTransparent]",
})
export class TabTransparentDirective implements OnInit {
	@Input() pactoTabTransparent;
	@Input() label;
	@Input() tabTotal;
	@Input() tabIcone;
	@Input() tabImage;
	@Input() showTabIcon: boolean = true;
	@Input() id: string;

	constructor(private templateRef: TemplateRef<any>) {}

	ngOnInit() {}

	get ref(): TemplateRef<any> {
		return this.templateRef;
	}
}
