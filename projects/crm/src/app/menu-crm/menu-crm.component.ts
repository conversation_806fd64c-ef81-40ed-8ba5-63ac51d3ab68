import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	LocalStorageSessionService,
	LoginUrlQueries,
	SessionService,
} from "sdk";
import { LoginAppApiLoginService, TrocaEmpresa } from "login-app-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "crm-menu-crm",
	templateUrl: "./menu-crm.component.html",
	styleUrls: ["./menu-crm.component.scss"],
})
export class MenuCrmComponent implements OnInit {
	@ViewChild("mudarEmpresa", { static: true })
	public mudarEmpresa: TemplateRef<any>;
	public fc: FormControl = new FormControl();

	public listaTrocaEmpresas: TrocaEmpresa[] = [];
	public usuarioGeral: string;

	constructor(
		private readonly sessionService: SessionService,
		private readonly localStorage: LocalStorageSessionService,
		private readonly loginAppApiLogin: LoginAppApiLoginService,
		private readonly modal: NgbModal,
		private readonly notify: SnotifyService
	) {}

	ngOnInit() {
		this.obterUsuarioGeral();
	}

	public get trocaEmpresa(): TrocaEmpresa[] {
		if (this.listaTrocaEmpresas && this.listaTrocaEmpresas.length > 0) {
			return this.listaTrocaEmpresas;
		}
		const mapa = new Map<string, TrocaEmpresa>();
		for (const empresa of this.sessionService.empresasAcesso) {
			if (empresa.key !== this.sessionService.chave) {
				mapa.set(
					empresa.key + "-" + empresa.emp,
					new TrocaEmpresa(empresa, null, null)
				);
			}
		}
		for (const empresa of this.sessionService.empresas) {
			const troca = new TrocaEmpresa(null, empresa, this.sessionService.chave);
			troca.atual =
				troca.empresa === parseInt(this.sessionService.empresaId, 10);
			mapa.set(troca.chave + "-" + troca.empresa, troca);
		}
		const lista = [];
		for (const id of mapa.keys()) {
			lista.push(mapa.get(id));
		}
		this.listaTrocaEmpresas = lista;
		return lista;
	}

	public get trocaEmpresaFiltradas() {
		const filter = this.fc.value;
		const empresasUnicas = new Set();
		let empresas;

		if (filter) {
			empresas = this.trocaEmpresa.filter((i) => {
				const nameN = i.nomeApresentar.toLowerCase();
				const filterN = filter.toLowerCase();
				return nameN.includes(filterN);
			});
		} else {
			empresas = this.trocaEmpresa;
		}

		const listEmpresas = [];

		const indexDaEmpresaLogada = empresas.findIndex((empresa) => empresa.atual);

		listEmpresas.push(empresas[indexDaEmpresaLogada]);

		for (const empresa of empresas) {
			if (!empresa.atual) {
				listEmpresas.push(empresa);
			}
		}

		listEmpresas.forEach((item) => {
			empresasUnicas.add(item);
		});

		return empresasUnicas;
	}

	public logoutHandler(): void {
		this.sessionService.logOut();
	}

	public trocaClickHandler(troca: TrocaEmpresa): void {
		if (troca.novo) {
			this.loginAppApiLogin
				.validarTrocaEmpresa(this.usuarioGeral, troca)
				.subscribe((res) => {
					if (res.content) {
						this.logoutHandler();
						window.location.href = res.content;
					} else {
						this.notify.error(res.meta.message);
					}
				});
		} else if (troca.empresa.toString() !== this.sessionService.empresaId) {
			this.sessionService.empresaId = troca.empresa.toString();
			this.updateLocalStorage();
			this.modal.dismissAll();
		}
	}

	public fecharHandler(): void {
		this.modal.dismissAll();
	}

	private updateLocalStorage(): void {
		const userLoged: LoginUrlQueries =
			this.localStorage.getLocalStorageParams();
		userLoged.empresaId = this.sessionService.empresaId;
		this.localStorage.setLocalStorageParams(userLoged);
		window.location.href = "./adm";
	}

	public changeUnitHandler(): void {
		this.modal.open(this.mudarEmpresa, {
			centered: true,
			windowClass: "larguraModal",
		});
	}

	private obterUsuarioGeral(): void {
		const { chave, codUsuarioZW, loggedUser } = this.sessionService;
		this.loginAppApiLogin
			.obterUsuarioGeral(chave, codUsuarioZW, loggedUser)
			.subscribe(
				(res) => {
					if (res && res.content) {
						this.usuarioGeral = res.content;
						localStorage.setItem("global-user", this.usuarioGeral);
						this.preencherEmpresasAcesso(this.usuarioGeral);
					}
				},
				(error) => {
					let email = localStorage.getItem("email");
					if (!email || email.includes("null")) {
						const pactoZObject = JSON.parse(localStorage.getItem("pactoZw"));
						if (pactoZObject && pactoZObject.emailusuario) {
							email = pactoZObject.emailusuario;
						}
					}
					if (email) {
						this.loginAppApiLogin.obterUsuarioGeralPorEmail(email).subscribe(
							(res) => {
								if (res && res.content && res.content.id) {
									this.usuarioGeral = res.content.id;
									localStorage.setItem("global-user", this.usuarioGeral);
									this.preencherEmpresasAcesso(this.usuarioGeral);
								}
								if (res && res.content && res.content.email) {
									localStorage.setItem("email", res.content.email);
								}
							},
							() =>
								console.error(
									"Erro ao obter usuário geral por e-mail menu adm-v2"
								)
						);
					}
				}
			);
	}

	private preencherEmpresasAcesso(usuarioGeral: string): void {
		this.loginAppApiLogin.obterEmpresas(usuarioGeral).subscribe(
			(res) => {
				if (res && res.content) {
					this.sessionService.empresasAcesso = res.content;
				}
			},
			(error) => console.error(error)
		);
	}

	isUsuarioPacto() {
		return this.sessionService.isUsuarioPacto;
	}

	isMenuV2() {
		return true;
		// return this.isUsuarioPacto() && !this.isChaveTeste();
	}
}
