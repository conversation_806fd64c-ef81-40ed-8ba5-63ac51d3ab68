import { LoggedinGuard } from "@adm/guards/logged-in.guard";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ModuleName } from "@base-core/modulo/modulo.model";
import { PerfilAcessoRecurso } from "sdk";
import { CaixaEmAbertoComponent } from "@adm/caixa-em-aberto/caixa-em-aberto.component";
import { ConvenioDescontoFormComponent } from "@adm/convenio-desconto/components/convenio-desconto-form/convenio-desconto-form.component";
import { ConvenioDescontoComponent } from "@adm/convenio-desconto/components/convenio-desconto/convenio-desconto.component";
import { DiariasComponent } from "@adm/diarias/components/diarias/diarias.component";
import { PerfilAcessoGuard } from "@adm/guards/perfil-acesso.guard";
import { HomeComponent } from "@adm/home/<USER>";
import { AddEditModalidadeComponent } from "@adm/modalidade/add-edit-modalidade/add-edit-modalidade.component";
import { ListModalidadeComponent } from "@adm/modalidade/list-modalidade/list-modalidade.component";
import { PacoteFormComponent } from "@adm/pacotes/components/form-pacote/form-pacote.component";
import { ListaPacoteComponent } from "@adm/pacotes/components/lista-pacote/lista-pacote.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "@adm/perfil-acesso/perfil-acesso-recurso.model";
import { CadastrarPlanoComponent } from "@adm/planos/components/cadastrar-plano/cadastrar-plano.component";
import { CondicaoPagamentoFormComponent } from "@adm/planos/components/condicao-pagamento-form/condicao-pagamento-form.component";
import { CondicaoPagamentoComponent } from "@adm/planos/components/condicao-pagamento/condicao-pagamento.component";
import { PcCadastroComponent } from "@adm/planos/components/credito/pc-cadastro/pc-cadastro.component";
import { DescontoFormComponent } from "@adm/planos/components/desconto-form/desconto-form.component";
import { DescontoComponent } from "@adm/planos/components/desconto/desconto.component";
import { EditFormPlanoComponent } from "@adm/planos/components/edit-form-plano/edit-form-plano.component";
import { HorarioFormComponent } from "@adm/planos/components/horario-form/horario-form.component";
import { HorarioComponent } from "@adm/planos/components/horario/horario.component";
import { PlanosComponent } from "@adm/planos/components/lista-planos/planos.component";
import { PpCadastroComponent } from "@adm/planos/components/personal/pp-cadastro/pp-cadastro.component";
import { PrCadastroComponent } from "@adm/planos/components/recorrencia/pr-cadastro/pr-cadastro.component";
import { TipoPlanoFormComponent } from "@adm/planos/components/tipo-plano-form/tipo-plano-form.component";
import { TipoPlanoComponent } from "@adm/planos/components/tipo-plano/tipo-plano.component";

const routes: Routes = [
	{
		path: "",
		redirectTo: "home",
		data: { module: ModuleName.ADM },
	},
	{
		path: "home",
		component: HomeComponent,
		data: { module: ModuleName.ADM },
	},
	{
		path: "",
		component: HomeComponent,
	},
	{
		path: "diarias",
		component: DiariasComponent,
	},
	{
		path: "planos",
		component: PlanosComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PLANO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "plano/:id",
		component: EditFormPlanoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PLANO, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "planos/novo-plano",
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PLANO, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
		children: [
			{
				path: "",
				component: CadastrarPlanoComponent,
				children: [
					{
						path: "avancado",
						component: CadastrarPlanoComponent,
					},
				],
			},
			{
				path: "recorrencia",
				component: PrCadastroComponent,
			},
			{
				path: "credito",
				component: PcCadastroComponent,
			},
			{
				path: "personal",
				component: PpCadastroComponent,
			},
		],
	},
	{
		path: "caixa-em-aberto",
		component: CaixaEmAbertoComponent,
	},
	{
		path: "planos/tipo-plano",
		component: TipoPlanoComponent,
	},
	{
		path: "planos/novo-tipo-plano",
		component: TipoPlanoFormComponent,
	},
	{
		path: "planos/tipo-plano/:id",
		component: TipoPlanoFormComponent,
	},
	{
		path: "planos/pacotes",
		component: ListaPacoteComponent,
	},
	{
		path: "planos/novo-pacote",
		component: PacoteFormComponent,
	},
	{
		path: "planos/pacotes/:id",
		component: PacoteFormComponent,
	},
	{
		path: "planos/desconto",
		component: DescontoComponent,
	},
	{
		path: "planos/novo-desconto",
		component: DescontoFormComponent,
	},
	{
		path: "planos/desconto/:id",
		component: DescontoFormComponent,
	},
	{
		path: "planos/horarios",
		component: HorarioComponent,
	},
	{
		path: "planos/novo-horario",
		component: HorarioFormComponent,
	},
	{
		path: "planos/horarios/:id",
		component: HorarioFormComponent,
	},
	{
		path: "planos/convenio-desconto",
		component: ConvenioDescontoComponent,
	},
	{
		path: "planos/novo-convenio-desconto",
		component: ConvenioDescontoFormComponent,
	},
	{
		path: "planos/convenio-desconto/:id",
		component: ConvenioDescontoFormComponent,
	},

	{
		path: "modalidade",
		component: ListModalidadeComponent,
	},

	{
		path: "modalidade/incluir",
		component: AddEditModalidadeComponent,
	},
	{
		path: "modalidade/editar",
		component: AddEditModalidadeComponent,
	},
	{
		path: "modalidade/visualizar",
		component: AddEditModalidadeComponent,
	},
	{
		path: "solicitacao-compra",
		loadChildren: () =>
			import("@adm/solicitacao-compra/solicitacaoCompra.module").then(
				(m) => m.SolicitacaoCompraModule
			),
	},
	{
		path: "cad-aux",
		loadChildren: () =>
			import("@adm/cadastro-auxliar/cadastro-auxliar.module").then(
				(m) => m.CadastroAuxliarModule
			),
	},
	{
		path: "produtos",
		loadChildren: () =>
			import("@adm/produtos/produtos.module").then((m) => m.ProdutosModule),
	},
	{
		path: "configuracao",
		canActivate: [LoggedinGuard],
		loadChildren: () =>
			import("@adm/configuracao/configuracao.module").then(
				(m) => m.ConfiguracaoModule
			),
	},
	{
		path: "planos/condicao-pagamento",
		component: CondicaoPagamentoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO,
				[
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.EDITAR,
					PerfilRecursoPermissoTipo.INCLUIR,
					PerfilRecursoPermissoTipo.EXCLUIR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
				]
			),
		},
	},
	{
		path: "planos/nova-condicao-pagamento",
		component: CondicaoPagamentoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO,
				[
					PerfilRecursoPermissoTipo.INCLUIR,
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
				]
			),
		},
	},
	{
		path: "planos/condicao-pagamento/:id",
		component: CondicaoPagamentoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO,
				[
					PerfilRecursoPermissoTipo.EDITAR,
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
				]
			),
		},
	},
	{
		path: "relatorios",
		loadChildren: () =>
			import("@adm/relatorios/relatorios.module").then(
				(m) => m.RelatoriosModule
			),
	},
	{
		path: "clube-vantagens",
		loadChildren: () =>
			import("@adm/clube-vantagens/clube-vantagens.module").then(
				(m) => m.ClubeVantagensModule
			),
	},
	{
		path: "acesso-sistema",
		loadChildren: () =>
			import("@adm/acesso-sitema/acesso-sitema.module").then(
				(m) => m.AcessoSitemaModule
			),
	},
	{
		path: "config-financeiras",
		loadChildren: () =>
			import("@adm/config-financeiras/config-financeiras.module").then(
				(m) => m.ConfigFinanceirasModule
			),
	},
	{
		path: "config-contrato",
		loadChildren: () =>
			import("@adm/config-contrato/config-contrato.module").then(
				(m) => m.ConfigContratoModule
			),
	},
	{
		path: "venda-rapida",
		loadChildren: () =>
			import("@adm/venda-rapida/venda-rapida.module").then(
				(m) => m.VendaRapidaModule
			),
	},
	{
		path: "outras-opcoes",
		loadChildren: () =>
			import("@adm/outras-opcoes/outras-opcoes.module").then(
				(m) => m.OutrasOpcoesModule
			),
	},
	{
		path: "freepass",
		loadChildren: () =>
			import("@adm/freepass/freepass.module").then((m) => m.FreepassModule),
	},
	{
		path: "negociacao",
		loadChildren: () =>
			import("@adm/negociacao/negociacao.module").then(
				(m) => m.NegociacaoModule
			),
	},
	{
		path: "venda-avulsa",
		loadChildren: () =>
			import("@adm/venda-avulsa/venda-avulsa.module").then(
				(m) => m.VendaAvulsaModule
			),
	},
	{
		path: "perfil-acesso-unificado",
		loadChildren: () =>
			import("@adm/perfil-acesso/perfil-acesso.module").then(
				(m) => m.PerfilAcessoModule
			),
	},
	{
		path: "bi",
		loadChildren: () =>
			import("@adm/modules/bi/bi.module").then((m) => m.BiModule),
	},
	{
		path: "posicao-estoque",
		loadChildren: () =>
			import("@adm/posicao-estoque/posicao-estoque.module").then(
				(m) => m.PosicaoEstoqueModule
			),
	},
	{
		path: "autorizacao-acesso",
		loadChildren: () =>
			import("@adm/autorizacao-acesso/autorizacao-acesso.module").then(
				(m) => m.AutorizacaoAcessoModule
			),
	},
];

@NgModule({
	imports: [CommonModule, RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class AdmRoutingModule {}
