import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostBinding,
	Input,
	OnDestroy,
	OnInit,
	Output,
	Renderer2,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiFornecedorService,
	ClienteDadosPessoais,
} from "adm-core-api";
import { Recurso } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { PactoApiCepService } from "pacto-api";
import { PermissaoService } from "pacto-layout";
import {
	AcessoCatracaService,
	AlunoColaboradorUsuarioService,
	DadosBasicosService,
} from "pessoa-ms-api";
import { BehaviorSubject, combineLatest, Observable, of, Subject } from "rxjs";
import { catchError, pairwise, takeUntil } from "rxjs/operators";
import { PerfilRecursoPermissoTipo } from "sdk";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalCadastroStatusComponent } from "../../perfil-cliente-header-v2/modal-cadastro-status/modal-cadastro-status.component";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";
import { TirarFotoComponent } from "../tirar-foto/tirar-foto.component";

declare var moment;

@Component({
	selector: "pacto-dados-basicos",
	templateUrl: "./dados-basicos.component.html",
	styleUrls: ["./dados-basicos.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class DadosBasicosComponent implements OnInit, AfterViewInit, OnDestroy {
	@HostBinding("class.pessoa-config-dados-basicos")
	styleEncapsulation = true;

	@Input() form: FormGroup;
	@Input() codigoPessoa: number;
	@Input() permiteEditar: boolean;
	@Output() saveConfig = new EventEmitter<void>();
	DISABLED = "disabled";
	APP_DISABLED = "app-disabled";
	TAB_INDEX = "tabindex";
	TAG_ANCHOR = "a";

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	@ViewChild("file", { static: true }) file: any;

	matricula: string;
	codigoCliente: string;
	dadosPessoais$: Observable<ClienteDadosPessoais | null>;
	LinkEnderecoAlternativo: boolean = true;
	visibleEnderecoAlternativo: boolean = false;
	fileControl: FormGroup = new FormGroup({
		file: new FormControl(),
		fileName: new FormControl(),
	});
	private _destroy: Subject<void> = new Subject();

	public cpfMask = [
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];
	public cnpjMask = [
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"/",
		/\d/,
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];
	public rgMask = [
		/[0-9]/,
		/[0-9]/,
		".",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		".",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-9]/,
	];
	public cepMask = [
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];

	numberMask = function (rawValue) {
		let maskToReturn = [
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
		];
		return maskToReturn;
	};
	foneMask = function (rawValue) {
		// número internacional
		if (rawValue.trim().startsWith("+")) {
			return [
				"+",
				/\d/,
				/\d/,
				"(",
				/\d/,
				/\d/,
				")",
				/\d/,
				" ",
				/\d/,
				/\d/,
				/\d/,
				/\d/,
				"-",
				/\d/,
				/\d/,
				/\d/,
				/\d/,
			];
		} else {
			// Formatação brasileira padrão
			const valorLimpo = rawValue.replace(/\D/g, "");
			if (valorLimpo.length > 10) {
				return [
					"(",
					/\d/,
					/\d/,
					")",
					/\d/,
					" ",
					/\d/,
					/\d/,
					/\d/,
					/\d/,
					"-",
					/\d/,
					/\d/,
					/\d/,
					/\d/,
				];
			} else {
				return [
					"(",
					/\d/,
					/\d/,
					")",
					/\d/,
					/\d/,
					/\d/,
					/\d/,
					"-",
					/\d/,
					/\d/,
					/\d/,
					/\d/,
				];
			}
		}
	};

	listaSexo$ = new BehaviorSubject<any[]>([]);
	listaPessoa = [
		{
			codigo: 1,
			sigla: "CI",
			descricao: "Cliente",
		},
		{
			codigo: 2,
			sigla: "CO",
			descricao: "Colaborador",
		},
	];
	listaTipoEndereco = [
		{
			codigo: "RE",
			descricao: "Residencial",
		},
		{
			codigo: "CO",
			descricao: "Comercial",
		},
	];
	listaCidade = [
		{ codigo: "1", descricao: "Rio de Janeiro" },
		{ codigo: "2", descricao: "São Paulo" },
		{ codigo: "3", descricao: "Minas Gerais" },
	];
	listaEstado = [
		{ codigo: "1", descricao: "RJ" },
		{ codigo: "2", descricao: "SP" },
		{ codigo: "3", descricao: "MG" },
	];
	estadoCivil$ = new BehaviorSubject<any[]>([]);
	tipoPessoa$ = new BehaviorSubject<any[]>([]);
	phoneTypes$ = new BehaviorSubject<any[]>([]);
	gender$ = new BehaviorSubject<any[]>([]);
	clientGroupsUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/gruposCliente"
	);

	responsavelGroupsUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/responsavel/0"
	);

	clientClassificacaoUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/classificacoesClientes"
	);
	clientGroups$: Observable<any> = this.dadosBasicosService
		.getGrupos()
		.pipe(catchError(() => of([])));

	clientClassificaoes$: Observable<any> = this.dadosBasicosService
		.getClassificacoes()
		.pipe(catchError(() => of([])));

	grausInstrucao$: Observable<any> = this.dadosBasicosService
		.getGrauInstucao()
		.pipe(catchError(() => of([])));

	profissoesUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/profissoes"
	);
	grauInstrucaoUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/grausInstrucao"
	);
	listaEmpresaSesiUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/listaEmpresaSesi"
	);
	cidadeUrl;
	estadoUrl;
	paisUrl = this.restService.buildFullUrlPessoaMs("pais");
	ArrayEmailsCarregados: any[] = [];
	ArrayTelefonesCarregados: any[] = [];
	arrayEnderecosCarregados: any[] = [];
	formDosEmails: FormArray = new FormArray([]);
	formDosTelefones: FormArray = new FormArray([]);
	formDosEnderecos: FormArray = new FormArray([]);
	recursoEmail: Recurso = undefined;
	recursoEndereco: Recurso = undefined;
	recursoTelefone: Recurso = undefined;
	isConfigSesiCe: boolean = false;
	isConfigSesiSc: boolean = false;
	isCadastroEmpresaHabilitado: boolean = false;
	isApresentarCnpjClienteSesi: boolean = false;
	isValidarCPFResponsaveis: boolean = false;

	opcoesNecessidadesEspeciais = [
		{ id: "Altas Habilidades", label: "Altas Habilidades" },
		{ id: "Auditiva", label: "Auditiva" },
		{ id: "Física", label: "Física" },
		{ id: "Intelectual (Mental)", label: "Intelectual (Mental)" },
		{ id: "Não Declarada", label: "Não Declarada" },
		{ id: "Visual", label: "Visual" },
		{ id: "Múltiplas", label: "Múltiplas" },
	];
	opcoesStatusMatricula = [
		{ id: "Cancelado", label: "Cancelado" },
		{ id: "Concluído", label: "Concluído" },
		{ id: "Evadido", label: "Evadido" },
		{ id: "Matriculado", label: "Matriculado" },
	];
	opcoesTipoPessoa = [
		{ id: "Fisica", label: "Fisica" },
		{ id: "Juridica", label: "Juridica" },
		{ id: "Estrangeiro", label: "Estrangeiro" },
	];
	estadosUf = [
		{ id: "BA", label: "BA" },
		{ id: "RS", label: "RS" },
		{ id: "RR", label: "RR" },
		{ id: "RO", label: "RO" },
		{ id: "RN", label: "RN" },
		{ id: "RJ", label: "RJ" },
		{ id: "CE", label: "CE" },
		{ id: "AP", label: "AP" },
		{ id: "MT", label: "MT" },
		{ id: "MS", label: "MS" },
		{ id: "PR", label: "PR" },
		{ id: "GO", label: "GO" },
		{ id: "AM", label: "AM" },
		{ id: "AL", label: "AL" },
		{ id: "SP", label: "SP" },
		{ id: "DF", label: "DF" },
		{ id: "PI", label: "PI" },
		{ id: "AC", label: "AC" },
		{ id: "MG", label: "MG" },
		{ id: "ES", label: "ES" },
		{ id: "PE", label: "PE" },
		{ id: "SE", label: "SE" },
		{ id: "SC", label: "SC" },
		{ id: "MA", label: "MA" },
		{ id: "PB", label: "PB" },
		{ id: "PA", label: "PA" },
		{ id: "TO", label: "TO" },
	];
	empresasFornecedorSesi = [];
	categories$ = this.acessoCatracaService
		.getCategorias()
		.pipe(catchError(() => of([])));

	get imageUrl(): string {
		return this.form.get("fotoBase64OrUrl").value || null;
	}

	responseParser = (result) => {
		if (result && result.content && Array.isArray(result.content)) {
			return result.content;
		}
		return [];
	};

	constructor(
		private eleRef: ElementRef,
		private renderer: Renderer2,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private dadosBasicosService: DadosBasicosService,
		private sessionService: SessionService,
		private restService: RestService,
		private dialogService: DialogService,
		private enderecoService: PactoApiCepService,
		private pessoaService: AlunoColaboradorUsuarioService,
		private modalService: ModalService,
		public configCliFormValidation: ConfigCliFormValidationService,
		private acessoCatracaService: AcessoCatracaService,
		private permissaoService: PermissaoService,
		private admCoreApiFornecedorService: AdmCoreApiFornecedorService
	) {}

	ngOnInit() {
		if (
			this.sessionService &&
			this.sessionService.perfilUsuarioAdm &&
			this.sessionService.perfilUsuarioAdm.perfilUsuario &&
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos
		) {
			this.recursoEmail =
				this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
					(r) => r.referenciaRecurso === "2.09"
				);
			this.recursoEndereco =
				this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
					(r) => r.referenciaRecurso === "2.11"
				);
			this.recursoTelefone =
				this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
					(r) => r.referenciaRecurso === "2.28"
				);
		}

		this.isConfigSesiCe =
			this.permissaoService.temConfiguracaoSistemaAdm("sesice");
		this.isConfigSesiSc = this.permissaoService.temConfiguracaoSistemaAdm(
			"utilizarservicosesisc"
		);
		this.isCadastroEmpresaHabilitado =
			this.permissaoService.temConfiguracaoEmpresaAdm(
				"habilitarCadastroEmpresaSesi"
			);
		this.loadConfigsSistema();
		this.form.get("tipoDePessoa").disable();
		this.form.get("dataCadastro").disable();

		console.log(
			"isCadastroEmpresaHabilitado: ",
			this.isCadastroEmpresaHabilitado
		);

		this.populateEnderecoForm();
		this.populateEmailForm();
		this.populateTelefoneForm();
		this.verificarCamposApresentar();
		this.configCliFormValidation.updated$.subscribe((v) => {
			if (v) {
				this.loadConfigsSistema();
				this.populateEnderecoForm();
				this.populateEmailForm();
				this.populateTelefoneForm();
				this.verificarCamposApresentar();
				this.verificarCPFObrigatorioMenor();
				this.cd.detectChanges();
			}
		});

		this.form.get("classificacaoCliente").valueChanges.subscribe((v) => {
			if (!v || v.length === 0) {
				return;
			}
			this.form.get("classificacaoCliente").markAsDirty();
		});

		this.form.get("tipoDePessoa").valueChanges.subscribe((v) => {
			if (!v || v.length === 0) {
				return;
			}
			this.form.get("tipoDePessoa").markAsDirty();
		});

		this.form.get("grupoClientes").valueChanges.subscribe((v) => {
			if (!v || v.length === 0) {
				return;
			}
			this.form.get("grupoClientes").markAsDirty();
		});

		this.form.valueChanges.subscribe((v) => {
			this.cd.detectChanges();
		});

		this.form.get("fotoBase64OrUrl").valueChanges.subscribe(() => {
			this.cd.detectChanges();
		});

		this.populateEstadoCidadeUrl();
		this.populateEmpresaFornecedorSesi();
		this.verifyIsEstrangeira();
		this.onChangePais();
		this.onChangeEstado();
		this.verificarCPFObrigatorioMenor();
		this.configuracaoSistemaLoaded();
	}

	ngAfterViewInit(): void {
		this.estadoCivil$.next([
			{
				codigo: "A",
				descricao: this.traducoes.getLabel("amasiado"),
			},
			{
				codigo: "C",
				descricao: this.traducoes.getLabel("casado"),
			},
			{
				codigo: "D",
				descricao: this.traducoes.getLabel("divorciado"),
			},
			{
				codigo: "P",
				descricao: this.traducoes.getLabel("separado"),
			},
			{
				codigo: "S",
				descricao: this.traducoes.getLabel("solteiro"),
			},
			{
				codigo: "U",
				descricao: this.traducoes.getLabel("uniao-estavel"),
			},
			{
				codigo: "V",
				descricao: this.traducoes.getLabel("viuvo"),
			},
		]);

		this.tipoPessoa$.next([
			{
				codigo: 0,
				descricao: this.traducoes.getLabel("pessoa-fisica"),
			},
			{
				codigo: 1,
				descricao: this.traducoes.getLabel("pessoa-juridica"),
			},
			{
				codigo: 2,
				descricao: this.traducoes.getLabel("estrangeira"),
			},
		]);

		this.listaSexo$.next([
			{
				codigo: "F",
				descricao: this.traducoes.getLabel("sexo-feminino"),
			},
			{
				codigo: "M",
				descricao: this.traducoes.getLabel("sexo-masculino"),
			},
		]);

		this.gender$.next([
			{
				codigo: "AG",
				descricao: this.traducoes.getLabel("agenero"),
			},
			{
				codigo: "FE",
				descricao: this.traducoes.getLabel("genero-feminino"),
			},
			{
				codigo: "MA",
				descricao: this.traducoes.getLabel("genero-masculino"),
			},
			{
				codigo: "NB",
				descricao: this.traducoes.getLabel("nao-binario"),
			},
		]);

		this.phoneTypes$.next([
			{
				codigo: "CE",
				descricao: this.traducoes.getLabel("telefone-celular"),
			},
			{
				codigo: "CO",
				descricao: this.traducoes.getLabel("telefone-comercial"),
			},
			{
				codigo: "EM",
				descricao: this.traducoes.getLabel("telefone-emergencia"),
			},
			{
				codigo: "FA",
				descricao: this.traducoes.getLabel("fax"),
			},
			{
				codigo: "RC",
				descricao: this.traducoes.getLabel("recado"),
			},
			{
				codigo: "RE",
				descricao: this.traducoes.getLabel("residencial"),
			},
		]);
		this.obterEmpresasFornecedorSesi();
		this.disableElement(this.eleRef.nativeElement);
		this.cd.detectChanges();
	}

	ngOnDestroy() {
		this._destroy.next();
	}

	private configuracaoSistemaLoaded() {
		this.permissaoService.configuracaoSistemaLoaded$
			.pipe(takeUntil(this._destroy))
			.subscribe(() => {
				this.isConfigSesiSc = this.permissaoService.temConfiguracaoSistemaAdm(
					"utilizarservicosesisc"
				);
			});
	}

	obterEmpresasFornecedorSesi() {
		if (this.isCadastroEmpresaHabilitado) {
			this.admCoreApiFornecedorService
				.getEmpresasFornecedorSesi()
				.subscribe((response) => {
					this.empresasFornecedorSesi = response;
					this.cd.detectChanges();
				});
		}
	}

	private verifyIsEstrangeira() {
		// combineLatest([
		//  this.form.get('estrangeira').valueChanges,
		// 	this.form.get('tipoPessoa').valueChanges
		// ]).subscribe(
		// 	([tipoPessoa]) => {
		this.form.get("tipoPessoa").valueChanges.subscribe((tipoPessoa) => {
			if (tipoPessoa == 0 || tipoPessoa == 2) {
				// 0 PF 1 PJ 2 ESTRANGEIRA
				this.form.get("cnpj").clearValidators();
				if (tipoPessoa == 2) {
					this.form.get("cpf").clearValidators();
					this.form.get("rg").clearValidators();
				} else {
					this.configCliFormValidation.verifyForm(this.form, "cpf");
					this.configCliFormValidation.verifyForm(this.form, "rg");
				}
			} else {
				this.configCliFormValidation.verifyForm(this.form, "cnpj");
				this.form.get("cpf").clearValidators();
				this.form.get("rg").clearValidators();
			}
			this.form.get("cpf").markAsTouched();
			this.form.get("cnpj").markAsTouched();
			this.form.get("rg").markAsTouched();
			this.form.get("cpf").updateValueAndValidity();
			this.form.get("cnpj").updateValueAndValidity();
			this.form.get("rg").updateValueAndValidity();
		});
	}

	private populateEnderecoForm() {
		const enderecos = this.form.get("enderecos") as FormArray;
		enderecos.controls.forEach((form: FormGroup) => {
			this.configCliFormValidation.verifyFormsToBeLoaded(form, this.form);
		});
		this.formDosEnderecos = this.form.get("enderecos") as FormArray;
		if (
			!this.recursoEndereco ||
			(this.recursoEndereco.tipoPermissoes &&
				!this.recursoEndereco.tipoPermissoes.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			this.formDosEnderecos.disable({ emitEvent: false });
		}
	}

	private populateTelefoneForm() {
		this.populateFormDosTelefones(this.form.get("telefone").value);
	}

	private populateFormDosTelefones(value) {
		this.ArrayTelefonesCarregados = value && Array.isArray(value) ? value : [];

		if (this.ArrayTelefonesCarregados.length > 0) {
			value.forEach((element, index) => {
				let controlTelefone = this.createTelefoneFormGroup(element);
				this.configCliFormValidation.verifyFormsToBeLoaded(
					controlTelefone,
					this.form
				);
				this.formDosTelefones.push(controlTelefone);
			});

			this.formDosTelefones.controls.forEach((_control: FormGroup) => {
				_control.markAsTouched();
			});
			if (
				!this.recursoTelefone ||
				(this.recursoTelefone.tipoPermissoes &&
					!this.recursoTelefone.tipoPermissoes.find(
						(tp) =>
							tp === PerfilRecursoPermissoTipo.EDITAR ||
							tp === PerfilRecursoPermissoTipo.TOTAL ||
							tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
							tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
					))
			) {
				this.formDosTelefones.disable({ emitEvent: false });
			}
		}
	}

	private populateEmailForm() {
		this.populateFormDosEmails(this.form.get("email").value);
	}

	private populateFormDosEmails(value) {
		this.ArrayEmailsCarregados = value && Array.isArray(value) ? value : [];

		if (this.ArrayEmailsCarregados.length > 0) {
			this.ArrayEmailsCarregados = new Array<any>();
			value.forEach((element, index) => {
				let controlMail = this.createEmailFormGroup(element);
				this.configCliFormValidation.verifyFormsToBeLoaded(
					controlMail,
					this.form
				);
				this.formDosEmails.push(controlMail);
			});

			this.formDosEmails.controls.forEach((_control: FormGroup) => {
				_control.markAsTouched();
			});
			if (
				!this.recursoEmail ||
				(this.recursoEmail.tipoPermissoes &&
					!this.recursoEmail.tipoPermissoes.find(
						(tp) =>
							tp === PerfilRecursoPermissoTipo.EDITAR ||
							tp === PerfilRecursoPermissoTipo.TOTAL ||
							tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
							tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
					))
			) {
				this.formDosEmails.disable({ emitEvent: false });
			}
		}
	}

	private populateEstadoCidadeUrl() {
		this.form.get("pais").valueChanges.subscribe((pais) => {
			if (pais) {
				this.estadoUrl = this.restService.buildFullUrlPessoaMs(
					`estado/${pais.codigo}`
				);
				if (this.form.get("estado").disabled) {
					this.form.get("estado").enable();
				}
			}
		});
		this.form.get("estado").valueChanges.subscribe((estado) => {
			if (estado) {
				this.cidadeUrl = this.restService.buildFullUrlPessoaMs(
					`cidade/${estado.codigo}`
				);
				if (this.form.get("cidade").disabled) {
					this.form.get("cidade").enable();
				}
			}
		});
		this.form.get("pessoaResponsavel").valueChanges.subscribe((resp) => {
			if (resp && resp.codigo) {
				this.responsavelGroupsUrl = this.restService.buildFullUrlPessoaMs(
					`alunoColaboradorUsuario/responsavel/${resp.codigo}`
				);
			}
		});
	}

	private populateEmpresaFornecedorSesi() {
		this.form.get("empresaFornecedor").valueChanges.subscribe((resp) => {
			if (resp && resp.codigo) {
				this.listaEmpresaSesiUrl = this.restService.buildFullUrlPessoaMs(
					`alunoColaboradorUsuario/listaEmpresaSesi`
				);
			}
		});
	}

	selectBuilderResp: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	paisParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	estadoParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	cidadeParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	empresaSesiFornecedorParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["razaoSocial"],
			}),
		};
	};

	clickVisible() {
		this.LinkEnderecoAlternativo = !this.LinkEnderecoAlternativo;
		this.visibleEnderecoAlternativo = !this.visibleEnderecoAlternativo;
	}

	takePicture(): void {
		const dialogRef = this.dialogService.open(
			"Capturar foto",
			TirarFotoComponent,
			PactoModalSize.LARGE
		);

		dialogRef.componentInstance.control = this.form.get("fotoBase64OrUrl");
	}

	uploadFile(): void {
		this.file.fileInput.nativeElement.click();
		// const input = this.file.elementRef.nativeElement.querySelector('input');
		// console.log(input)
	}

	checkData(): void {
		this.form.markAllAsTouched();
		this.goToFormInvalid();
	}

	goToFormInvalid() {
		let form = document.getElementById("idFormulario");
		let firstInvalidControl = form.getElementsByClassName("ng-invalid")[0];
		firstInvalidControl.scrollIntoView();
		(firstInvalidControl as HTMLElement).focus();
	}

	addEmail() {
		if (
			!this.recursoEmail ||
			(this.recursoEmail.tipoPermissoes &&
				!this.recursoEmail.tipoPermissoes.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			this.notificationService.error(
				"Usuário não possui a permissão 2.09 - E-mails ou não possui permissão para inclusão. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		let todosEmailsValidos = true;
		this.formDosEmails.controls.forEach((control) => {
			const emailControl = control.get("email");
			if (emailControl.enabled && !emailControl.valid) {
				todosEmailsValidos = false;
				const emailIncorreto = emailControl.value;
				emailControl.markAsTouched();
				this.notificationService.error(
					`O email com valor "${emailIncorreto}" é inválido.`,
					{
						timeout: 2000,
						bodyMaxLength: 300,
					}
				);
			}
		});

		if (todosEmailsValidos) {
			let controlMail = this.createEmailFormGroup();
			this.configCliFormValidation.verifyFormsToBeLoaded(
				controlMail,
				this.form
			);
			this.formDosEmails.push(controlMail);
		}
	}

	addEndereco() {
		if (
			!this.recursoEndereco ||
			(this.recursoEndereco.tipoPermissoes &&
				!this.recursoEndereco.tipoPermissoes.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			this.notificationService.error(
				"Usuário não possui a permissão 2.11 - Endereços ou não possui permissão para inclusão. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		let todosEnderecosValidos = true;
		// this.formDosEnderecos.controls.forEach(control => {
		//     const enderecoControl = control.get('endereco');
		//     if (!enderecoControl.valid) {
		//         todosEnderecosValidos = false;
		//         const enderecoErrado = enderecoControl.value;
		//         enderecoControl.markAsTouched();
		//         this.notificationService.error(
		//             `O endereco com valor "${enderecoErrado}" é inválido.`, {
		// 	timeout: 2000,
		// 	bodyMaxLength: 300
		// }
		//         );
		//     }
		// });

		if (todosEnderecosValidos) {
			let enderecoControl = this.createEnderecoFormGroup();
			this.configCliFormValidation.verifyFormsToBeLoaded(
				enderecoControl,
				this.form
			);

			const enderecosFormArray = this.form.get("enderecos") as FormArray;
			enderecosFormArray.push(enderecoControl);
			this.formDosEnderecos = this.form.get("enderecos") as FormArray;
		}
	}

	addTelefone() {
		if (
			!this.recursoTelefone ||
			(this.recursoTelefone.tipoPermissoes &&
				!this.recursoTelefone.tipoPermissoes.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			this.notificationService.error(
				"Usuário não possui a permissão 2.28 - Telefones ou não possui permissão para inclusão. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		let todosTelefonesValidos = true;
		this.formDosTelefones.controls.forEach((control: FormGroup) => {
			const telefoneControl = control.get("telefone");
			if (telefoneControl.enabled && !telefoneControl.valid) {
				todosTelefonesValidos = false;
				const telefoneIncorreto = telefoneControl.value;
				telefoneControl.markAsTouched();
				this.notificationService.error(
					`O número de telefone com valor "${telefoneIncorreto}" é inválido.`,
					{
						timeout: 2000,
						bodyMaxLength: 300,
					}
				);
			}
		});

		if (todosTelefonesValidos) {
			const controlTelefone = this.createTelefoneFormGroup();
			this.configCliFormValidation.verifyFormsToBeLoaded(
				controlTelefone,
				this.form
			);
			this.formDosTelefones.push(controlTelefone);
		}
	}

	createEmailFormGroup(element?: any) {
		if (!element) {
			element = {
				email: "",
				emailCorrespondencia: true,
				codigo: "",
				pessoa: "",
				bloqueadoBounce: "",
				receberEmailNovidades: "",
			};
		}

		return new FormGroup({
			email: new FormControl(element.email, Validators.email),
			emailCorrespondencia: new FormControl(element.emailCorrespondencia),
			codigo: new FormControl(element.codigo),
			pessoa: new FormControl(element.pessoa),
			bloqueadoBounce: new FormControl(
				element.bloqueadoBounce === "" ? false : element.bloqueadoBounce
			),
			receberEmailNovidades: new FormControl(
				element.receberEmailNovidades === ""
					? false
					: element.receberEmailNovidades
			),
		});
	}

	createTelefoneFormGroup(element?: any) {
		if (!element) {
			element = {
				codigo: "",
				pessoa: "",
				telefone: "",
				numero: "",
				tipoTelefone: "",
				receberSMS: "",
				usarNonoDigitoWApp: "",
				descricao: "",
			};
		}
		let telefone = "";
		if (element.numero && element.numero !== "") {
			telefone = element.numero;
		} else if (element.telefone && element.telefone !== "") {
			telefone = element.telefone;
		}
		return new FormGroup({
			telefone: new FormControl(telefone, [Validators.required]),
			numero: new FormControl(element.numero),
			descricao: new FormControl(element.descricao),
			descricaoTelefone: new FormControl(element.descricao),
			tipoTelefone: new FormControl(
				element.tipoTelefone && !element.tipoTelefone.codigo
					? { codigo: element.tipoTelefone }
					: element.tipoTelefone,
				[Validators.required]
			),
			codigo: new FormControl(element.codigo),
			pessoa: new FormControl(element.pessoa),
			receberSMS: new FormControl(element.receberSMS),
			usarNonoDigitoWApp: new FormControl(element.usarNonoDigitoWApp),
		});
	}

	createEnderecoFormGroup(element?: any) {
		if (!element) {
			element = {
				codigo: undefined,
				endereco: "",
				bairro: "",
				numero: "",
				complemento: "",
				tipoEndereco: "",
				// cidade: '',
				// estado: '',
				cep: "",
				pessoa: "",
			};
		}

		return new FormGroup({
			codigo: new FormControl(undefined),
			endereco: new FormControl(""),
			bairro: new FormControl(""),
			numero: new FormControl(""),
			complemento: new FormControl(""),
			tipoEndereco: new FormControl(""),
			// cidade: new FormControl(''),
			// estado: new FormControl(''),
			cep: new FormControl(""),
			pessoa: new FormControl(""),
		});
	}

	consultaCep(index) {
		let formDaVez = this.form.get(`enderecos.${index}`);
		let valorCep = formDaVez.get("cep").value.replace("-", "");
		if (valorCep !== "") {
			this.enderecoService
				.buscaCEP(this.sessionService.chave, valorCep)
				.subscribe((data) => {
					formDaVez.get("endereco").setValue(data.content.endereco);
					formDaVez.get("bairro").setValue(data.content.bairro);
					formDaVez.get("complemento").setValue(data.content.complemento);
				});
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("cep-nao-informado"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
		}
	}

	applyCepMask(event: any): void {
		const input = event.target;
		let value = input.value.replace(/\D/g, "");
		if (value.length > 5) {
			value = value.substring(0, 5) + "-" + value.substring(5, 8);
		}
		input.value = value;
		const formControl = input.getAttribute("formControlName");
		if (formControl) {
			this.form.get(`enderecos.${formControl}`).setValue(value);
		}
	}

	saveConfigFn() {
		if (this.formDosEmails.invalid) {
			this.formDosEmails.markAllAsTouched();
			this.notificationService.error(
				this.traducoes.getLabel("cli-emails-obrigatorio"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		let emailInvalido = false;
		this.formDosEmails.controls.forEach((emailObj) => {
			emailObj
				.get("email")
				.setValidators([Validators.email, Validators.required]);
			if (
				this.configCliFormValidation.configForms["email"].indicator === "*" &&
				(!emailObj.get("email").value || emailObj.get("email").value === "")
			) {
				this.notificationService.error(
					this.traducoes.getLabel("cli-emails-obrigatorio"),
					{
						timeout: 2000,
						bodyMaxLength: 300,
					}
				);
				emailInvalido = true;
			}
			emailObj.get("email").updateValueAndValidity();
			emailObj.get("email").markAsTouched();

			if (emailObj.invalid) {
				emailInvalido = true;
				this.notificationService.error(
					this.traducoes.getLabel("cli-emails-invalidos"),
					{
						timeout: 2000,
						bodyMaxLength: 300,
					}
				);
			}
		});
		if (emailInvalido) {
			return;
		}
		this.formDosTelefones.controls.forEach((item: FormGroup, index) => {
			item.get("numero").setValue(item.get("telefone").value);
			item.get("descricao").setValue(item.get("descricaoTelefone").value);
		});
		if (this.formDosTelefones.invalid) {
			this.formDosTelefones.markAllAsTouched();
			this.notificationService.error(
				this.traducoes.getLabel("cli-telefones-obrigatorio"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		let telefoneInvalido = false;
		this.formDosTelefones.controls.forEach((telefoneGroup) => {
			let telefone = "";
			if (
				telefoneGroup.get("numero").value &&
				telefoneGroup.get("numero").value !== ""
			) {
				telefone = telefoneGroup.get("numero").value;
			} else if (
				telefoneGroup.get("telefone").value &&
				telefoneGroup.get("telefone").value !== ""
			) {
				telefone = telefoneGroup.get("telefone").value;
			}
			const telefoneNumber = telefone
				.replace("(", "")
				.replace(")", "")
				.replace(/\_/g, "");
			if (telefoneNumber === "") {
				telefoneGroup.get("telefone").setValue(telefoneNumber);
				telefoneGroup.get("numero").setValue(telefoneNumber);
				telefoneGroup.get("numero").setValidators(Validators.required);
				telefoneGroup.get("telefone").setValidators(Validators.required);
				telefoneGroup.get("telefone").updateValueAndValidity();
				telefoneGroup.get("numero").updateValueAndValidity();
				telefoneGroup.get("numero").markAsTouched();
				telefoneGroup.get("telefone").markAsTouched();
			}
			if (
				this.configCliFormValidation.configForms["descricaoTelefone"]
					.indicator === "*" &&
				(!telefoneGroup.get("descricaoTelefone").value ||
					telefoneGroup.get("descricaoTelefone").value === "")
			) {
				telefoneGroup
					.get("descricaoTelefone")
					.setValidators(Validators.required);
				telefoneGroup.get("descricaoTelefone").updateValueAndValidity();
				telefoneGroup.get("descricaoTelefone").markAsTouched();
				this.notificationService.error(
					this.traducoes.getLabel("cli-telefones-obrigatorio"),
					{
						timeout: 2000,
						bodyMaxLength: 300,
					}
				);
				telefoneInvalido = true;
			}
		});
		this.form.get("telefone").patchValue(this.formDosTelefones.getRawValue());
		this.form.get("email").patchValue(this.formDosEmails.getRawValue());
		if (telefoneInvalido) {
			return;
		}
		if (this.form.get("telefoneEmergencia").value) {
			const telefoneNumber = this.form
				.get("telefoneEmergencia")
				.value.replace("(", "")
				.replace(")", "")
				.replace(/\_/g, "");
			if (telefoneNumber === "") {
				this.form.get("telefoneEmergencia").setValue(telefoneNumber);
			}
			this.form
				.get("telefoneEmergencia")
				.setValue(this.form.get("telefoneEmergencia").value.replace(/\_/g, ""));
		}
		if (
			moment(this.form.get("dataNasc").value).format("DD/MM/YYYY") >
			moment().format("DD/MM/YYYY")
		) {
			this.form.get("cpfMae").clearValidators();
			this.form.get("nomeMae").clearValidators();
			this.form.get("cpfPai").clearValidators();
			this.form.get("nomePai").clearValidators();
			this.form.get("cpfMae").updateValueAndValidity();
			this.form.get("nomeMae").updateValueAndValidity();
			this.form.get("cpfPai").updateValueAndValidity();
			this.form.get("nomePai").updateValueAndValidity();
		}
		this.saveConfig.emit();
	}

	removeEndereco(index) {
		const enderecosFormArray = this.form.get("enderecos") as FormArray;
		const endereco = enderecosFormArray.at(index).value;
		if (
			endereco.codigo &&
			(!this.recursoEndereco ||
				(this.recursoEndereco.tipoPermissoes &&
					!this.recursoEndereco.tipoPermissoes.find(
						(tp) =>
							tp === PerfilRecursoPermissoTipo.EXCLUIR ||
							tp === PerfilRecursoPermissoTipo.TOTAL ||
							tp === PerfilRecursoPermissoTipo.EDITAR
					)))
		) {
			this.notificationService.error(
				"Usuário não possui a permissão 2.11 - Endereços ou não possui permissão para remoção de endereço. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		enderecosFormArray.removeAt(index);
		this.formDosEnderecos = this.form.get("enderecos") as FormArray;
	}

	removeEmail(index) {
		const email = this.formDosEmails.at(index).value;
		if (
			email.codigo &&
			(!this.recursoEmail ||
				(this.recursoEmail.tipoPermissoes &&
					!this.recursoEmail.tipoPermissoes.find(
						(tp) =>
							tp === PerfilRecursoPermissoTipo.EXCLUIR ||
							tp === PerfilRecursoPermissoTipo.TOTAL ||
							tp === PerfilRecursoPermissoTipo.EDITAR
					)))
		) {
			this.notificationService.error(
				"Usuário não possui a permissão 2.09 - E-mails ou não possui permissão para remoção de e-mail. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		this.formDosEmails.removeAt(index);
	}

	removeTelefone(index: number) {
		const telefone = this.formDosTelefones.at(index).value;
		if (
			telefone.codigo &&
			(!this.recursoTelefone ||
				(this.recursoTelefone.tipoPermissoes &&
					!this.recursoTelefone.tipoPermissoes.find(
						(tp) =>
							tp === PerfilRecursoPermissoTipo.EXCLUIR ||
							tp === PerfilRecursoPermissoTipo.TOTAL ||
							tp === PerfilRecursoPermissoTipo.EDITAR
					)))
		) {
			this.notificationService.error(
				"Usuário não possui a permissão 2.28 - Telefones ou não possui permissão para remoção de telefone. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		this.formDosTelefones.removeAt(index);
	}

	infoGenero() {
		const modalRef = this.modalService.open(
			"Gênero",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			"Este campo serve para registrar o gênero com o qual o cliente se identifica. " +
			"Os papéis de gênero estão associados a comportamentos, atividades e atributos que " +
			"uma sociedade considera apropriados para homens, mulheres e outras identidades. " +
			"Gênero é uma construção social que inclui a expressão de gênero, ou seja, " +
			"como a pessoa manifesta sua identidade para o mundo. A percepção de gênero " +
			"pode variar amplamente entre diferentes culturas e ao longo do tempo. " +
			"Acreditamos que respeitar o gênero manifestado pelo próprio cliente " +
			"é uma forma de acolhimento e respeito à dignidade humana.";
		modalRef.componentInstance.showBtn = false;
	}

	infoSexo() {
		const modalRef = this.modalService.open(
			"Sexo biológico",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			'Dentro do sistema Pacto, o campo "Sexo Biológico" é utilizado para a aplicação prática de avaliações físicas. Atualmente, a maioria dos protocolos de avaliação disponíveis realiza cálculos distintos dependendo do sexo biológico da pessoa avaliada, tornando essa informação essencial para a execução adequada dos cálculos. No entanto, essa informação não determina a forma como a pessoa se identifica no mundo. Para refletir essa identificação pessoal, o sistema disponibiliza o campo "Gênero".';
		modalRef.componentInstance.showBtn = false;
	}

	infoNomeRegistro() {
		const modalRef = this.modalService.open(
			"Nome de registro",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			"O nome de registro é o nome cadastrado em documentos legais, como certidão de nascimento, carteira de identidade, passaporte e outros registros civis. A finalidade desse campo é garantir que pessoas que utilizam um nome social, diferente do nome legal, possam usá-lo em todos os pontos de contato do sistema, enquanto o nome de registro é mantido para a emissão de notas fiscais, contratos e outros documentos oficiais. Um exemplo prático dessa utilização é o cadastro de pessoas trans que ainda não retificaram seus documentos.";
		modalRef.componentInstance.showBtn = false;
	}

	private onChangePais() {
		this.form
			.get("pais")
			.valueChanges.pipe(pairwise())
			.subscribe(([prevValue, value]) => {
				if (value && prevValue && value.codigo !== prevValue.codigo) {
					this.form.get("estado").setValue(null);
					this.form.get("cidade").setValue(null);
				}
			});
	}

	private onChangeEstado() {
		this.form
			.get("estado")
			.valueChanges.pipe(pairwise())
			.subscribe(([prevValue, value]) => {
				if (value && prevValue && value.codigo !== prevValue.codigo) {
					this.form.get("cidade").setValue(null);
				}
			});
	}

	private disableElement(element: any) {
		if (this.permiteEditar) {
			if (element.hasAttribute(this.APP_DISABLED)) {
				if (element.getAttribute("disabled") !== "") {
					element.removeAttribute(this.DISABLED);
				}
				element.removeAttribute(this.APP_DISABLED);
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					element.removeAttribute(this.TAB_INDEX);
				}
			}
		} else {
			if (!element.hasAttribute(this.DISABLED)) {
				this.renderer.setAttribute(element, this.APP_DISABLED, "");
				this.renderer.setAttribute(element, this.DISABLED, "true");

				// disabling anchor tab keyboard event
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					this.renderer.setAttribute(element, this.TAB_INDEX, "-1");
				}
			}
		}
		if (element.children) {
			for (let ele of element.children) {
				this.disableElement(ele);
			}
		}
	}

	private verificarCamposApresentar() {
		this.isApresentarCnpjClienteSesi =
			this.configCliFormValidation.apresentarCampo("CNPJ Sesi Indústria");
	}

	verificarCPFObrigatorioMenor() {
		const dataNasc = this.form.get("dataNasc").value;
		if (dataNasc && this.isValidarCPFResponsaveis) {
			const idadeDifMs = Date.now() - new Date(dataNasc).getTime();
			const idadeData = new Date(idadeDifMs);
			const idade = idadeData.getUTCFullYear() - 1970;
			// const cpfMae = this.form.get('cpfMae').value;
			// const cpfPai = this.form.get('cpfPai').value;
			// if (idade < 18 && this.isValidarCPFResponsaveis && ((cpfMae && cpfMae.length > 0) || (cpfPai && cpfPai.length > 0))) {
			console.log("idade: ", idade);
			if (idade < 18) {
				this.form.get("cpf").clearValidators();
			}
		}
	}

	public removerResponsavelCPF() {
		this.form.get("pessoaResponsavel").setValue(null);
		this.form.get("utilizarResponsavelPagamento").setValue(false);
	}

	private loadConfigsSistema() {
		this.isValidarCPFResponsaveis =
			this.permissaoService.temConfiguracaoSistemaAdm("validarcpfresponsaveis");
	}

	formatResponsavelLabel(item: any): string {
		return item.nome + (item.cfp ? " - " + item.cfp : "");
	}

	removerEmpresaFornecedor() {
		this.form.get("empresaFornecedor").setValue(null);
	}
}
