import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

@Component({
	selector: "adm-autorizacao-acesso-password-modal",
	templateUrl: "./autorizacao-acesso-password-modal.component.html",
	styleUrls: ["./autorizacao-acesso-password-modal.component.scss"],
})
export class AutorizacaoAcessoPasswordModalComponent implements OnInit {
	formGroup: FormGroup;

	constructor() {}

	ngOnInit() {
		this.createForm();
	}

	createForm() {
		this.formGroup = new FormGroup({
			aluno: new FormControl({ value: "", disabled: true }),
			senha: new FormControl(null, [Validators.required]),
			confirmarSenha: new FormControl(null, [Validators.required]),
		});
	}

	save() {
		if (this.formGroup.invalid) {
			this.formGroup.markAllAsTouched();
			return;
		}

		console.log(this.formGroup.value);
	}
}
