<adm-layout
	(goBack)="voltarParaListagem()"
	i18n-modulo="@@justificativa-operacao:modulo"
	i18n-pageTitle="@@justificativa-operacao:title"
	modulo="Administrativo"
	pageTitle="Justificativa de operação">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<h1 class="titulo" i18n="@@justificativa-operacao:insira-os-dados">
				Insira os dados
			</h1>
			<div class="row">
				<div class="col-md-7">
					<pacto-cat-form-input
						[control]="formGroup.get('descricao')"
						[maxlength]="50"
						i18n-label="@@justificativa-operacao:label-descricao"
						i18n-placeholder="
							@@justificativa-operacao:label-descricao-placeholder"
						id="descricao"
						label="Descrição"
						placeholder="Digite a descrição"></pacto-cat-form-input>
				</div>
				<div class="col-md-3">
					<pacto-cat-form-select
						[control]="formGroup.get('tipoOperacao')"
						[items]="tiposOperacoes | async"
						i18n-label="@@justificativa-operacao:label-tipo-operacao"
						id="tipo-operacao"
						idKey="id"
						label="Tipo de operação"
						labelKey="descricao"></pacto-cat-form-select>
				</div>
			</div>
			<div class="row titulo-inputs" id="situacao">
				<div class="col-md-12">
					<h3 i18n="@@justificativa-operacao:situation">Situação</h3>
				</div>
			</div>
			<div class="row row-check-box-1">
				<div>
					<pacto-cat-checkbox
						[control]="formGroup.get('ativa')"
						i18n-label="@@justificativa-operacao:label-ativo"
						id="ativo"
						label="Ativo"></pacto-cat-checkbox>
				</div>
				<div style="margin-left: 36px">
					<pacto-cat-checkbox
						[control]="formGroup.get('apresentarTodasEmpresas')"
						i18n-label="@@justificativa-operacao:label-apresentar-todas-empresa"
						id="apresentar-todas-empresas"
						label="Apresentar para todas as empresas"></pacto-cat-checkbox>
				</div>
			</div>
			<div
				*ngIf="this.formGroup.get('tipoOperacao').value === 'CA'"
				class="row row-check-box-2">
				<div>
					<pacto-cat-checkbox
						[control]="formGroup.get('isentarMultaCancelamento')"
						i18n-label="@@justificativa-operacao:label-isentar-multa"
						id="insertar-multa"
						label="Isentar a multa no cancelamento"></pacto-cat-checkbox>
				</div>
				<div style="margin-left: 24px">
					<pacto-cat-checkbox
						[control]="formGroup.get('naoCobrarParcelasAtrasadasCancelamento')"
						i18n-label="
							@@justificativa-operacao:label-nao-cobrar-parcelas-atrasadas-cancelamento"
						label="Não cobrar parcelas atrasadas no cancelamento"></pacto-cat-checkbox>
				</div>
				<div style="margin-left: 24px">
					<pacto-cat-checkbox
						[control]="formGroup.get('necessarioAnexarComprovante')"
						i18n-label="@@justificativa-operacao:label-necessario-comprovante"
						id="comprovante"
						label="Necessário anexar um comprovante"></pacto-cat-checkbox>
				</div>
			</div>
			<div class="row">
				<div id="informe-os-dados">
					<h1 i18n="@@justificativa-operacao:informe-os-dados">
						Informe os dados
					</h1>
					<p i18n="@@justificativa-operacao:informe-os-parametros">
						Informe os parâmetros acima para cadastrar uma nova justificativa de
						operação.
					</p>
				</div>
			</div>
		</div>
		<div class="row justify-content-end">
			<pacto-cat-button
				(click)="voltarParaListagem()"
				i18n-label="@@justificativa-operacao:btn-voltar"
				id="btn-voltar"
				label="Voltar"
				size="LARGE"
				style="margin-right: 10px"
				type="OUTLINE"
				width="80px"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvar()"
				[iconPosition]="'after'"
				[icon]="'pct pct-save'"
				i18n-label="@@justificativa-operacao:btn-salvar"
				id="btn-salvar"
				label="Salvar"
				size="LARGE"
				type="PRIMARY"
				width="110px"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@justificativa-operacao:saved-success" xingling="saved-success">
		Justificativa salva com sucesso!
	</span>
	<span
		i18n="@@justificativa-operacao:msg-usuario-sem-permissao-editar"
		xingling="msg-usuario-sem-permissao-editar">
		Seu usuário não possui a permissão para editar, procure o administrador.
	</span>
	<span
		i18n="@@justificativa-operacao:msg-usuario-sem-permissao-cadastrar"
		xingling="msg-usuario-sem-permissao-cadastrar">
		Seu usuário não possui a permissão para cadastrar, procure o administrador.
	</span>
	<span i18n="@@justificativa-operacao:operation-type-atestado" xingling="at">
		Atestado
	</span>
	<span
		i18n="@@justificativa-operacao:operation-type-cancelamento"
		xingling="ca">
		Cancelamento
	</span>
	<span i18n="@@justificativa-operacao:operation-type-ferias" xingling="cr">
		Férias
	</span>
	<span
		i18n="@@justificativa-operacao:operation-type-trancamento"
		xingling="tr">
		Trancamento
	</span>
	<span i18n="@@justificativa-operacao:operation-type-bonus" xingling="bo">
		Bônus
	</span>
</pacto-traducoes-xingling>
