<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@impostoproduto:modulo"
	i18n-pageTitle="@@impostoproduto:title"
	modulo="Configurações"
	pageTitle="Imposto padrão para produtos">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableImpostoProduto
			(btnAddClick)="novoImpostoProduto()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editImpostoProduto($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="impostoproduto"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@impostoproduto:filtro-situacao" xingling="filtro-situacao">Situação</span>
	<span i18n="@@impostoproduto:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@impostoproduto:desativado" xingling="inativo">Inativo</span>

	<span i18n="@@impostoproduto:impostoProdutoDeleted" xingling="DELETED">
		Imposto Produto excluído com sucesso
	</span>
	<span i18n="@@impostoproduto:editAction" xingling="ACTION_EDIT">Editar</span>
	<span i18n="@@impostoproduto:editTooltip" xingling="TOOLTIP_EDIT">
		Editar Imposto Produto
	</span>
	<span i18n="@@impostoproduto:deleteAction" xingling="ACTION_DELETE">
		Excluir
	</span>
	<span i18n="@@impostoproduto:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir Imposto Produto
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@impostoproduto:columnCodigo">Código</span>
</ng-template>
<ng-template #columnCFOP>
	<span i18n="@@impostoproduto:columnCFOP">CFOP</span>
</ng-template>
<ng-template #columnNCM>
	<span i18n="@@impostoproduto:columnNCM">NCM</span>
</ng-template>
