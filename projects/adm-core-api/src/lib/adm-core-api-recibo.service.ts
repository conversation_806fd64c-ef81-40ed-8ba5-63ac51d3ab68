import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmCoreApiModule } from "./adm-core-api.module";
import { AdmCoreApiBaseService } from "./adm-core-api-base.service";
import { ApiResponseList } from "./base.model";
import { Recibo } from "./recibo-pagamento.model";

@Injectable({
	providedIn: AdmCoreApiModule,
})
export class AdmCoreApiReciboService {
	constructor(private restApi: AdmCoreApiBaseService) {}

	public findParcelasByRecibo(id: number | string): Observable<any> {
		return this.restApi.get(`recibos/${id}/parcelas`);
	}

	public findProdutosByRecibo(id: number | string): Observable<any> {
		return this.restApi.get(`recibos/${id}/produtos`);
	}

	public findPagamentosByRecibo(id: number | string): Observable<any> {
		return this.restApi.get(`recibos/${id}/pagamentos`);
	}

	recibosByPessoa(
		codigoPessoa: string | number
	): Observable<ApiResponseList<Recibo>> {
		return this.restApi.get<ApiResponseList<Recibo>>(`recibos/${codigoPessoa}`);
	}
}
