import { Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import {
	DialogService,
	GridFilterConfig,
	PactoDataGridConfig,
	PactoModalSize,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { AutorizacaoAcessoPasswordModalComponent } from "../autorizacao-acesso-password-modal/autorizacao-acesso-password-modal.component";

@Component({
	selector: "adm-autorizacao-acesso-lista",
	templateUrl: "./autorizacao-acesso-lista.component.html",
	styleUrls: ["./autorizacao-acesso-lista.component.scss"],
})
export class AutorizacaoAcessoListaComponent implements OnInit {
	filterConfig: GridFilterConfig = { filters: [] };
	tableAutorizacaoAcesso: PactoDataGridConfig;

	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;

	@ViewChild("nameCell", { static: true }) nameCell;
	@ViewChild("passwordCell", { static: true }) passwordCell;

	constructor(
		private admRest: AdmRestService,
		private router: Router,
		private dialogService: DialogService
	) {}

	ngOnInit() {
		this.initTableAutorizacaoAcesso();
	}

	navigateToNovaAutorizacaoAcesso() {
		this.router.navigate(["adm/autorizacao-acesso/nova-autorizacao-acesso"]);
	}

	navigateToEditarAutorizacaoAcesso(autorizacao) {
		this.router.navigate(["adm/autorizacao-acesso/editar/" + autorizacao.id]);
	}

	openAutorizacaoAcessoPasswordModal() {
		const dialogRef = this.dialogService.open(
			"Cadastrar senha de integração",
			AutorizacaoAcessoPasswordModalComponent,
			PactoModalSize.LARGE
		);
	}

	private initTableAutorizacaoAcesso() {
		// setTimeout(() => {
		this.tableAutorizacaoAcesso = new PactoDataGridConfig({
			// endpointUrl: this.admRest.buildFullUrl('/pacote', false, Api.MSPLANO),
			logUrl: this.admRest.buildFullUrl("log/log-autorizacao-acesso"),
			quickSearch: true,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return {
					content: [
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},

						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
						{
							codigo: 564,
							nome: "Nome do cliente",
							matricula: "56154",
							codAcesso: "84984",
							tipo: "Aluno",
							senhaDeAcessoIntegracao: "",
						},
					],
					// content: [],
					first: true,
					last: false,
					number: 0,
					size: 10,
					totalElements: 10,
					totalPages: 1,
				};
			},
			columns: [
				{ nome: "codigo", titulo: "Código", visible: true, ordenavel: false },
				{
					nome: "nome",
					styleClass: "center no-padding autorizacao-name",
					titulo: "Nome",
					celula: this.nameCell,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "matricula",
					styleClass: "center",
					titulo: "Matrícula",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "codAcesso",
					styleClass: "center",
					titulo: "Cod. Acesso",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "tipo",
					styleClass: "center",
					titulo: "Tipo",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "senhaDeAcessoIntegracao",
					styleClass: "center no-padding autorizacao-password",
					titulo: "Senha de acesso integração",
					celula: this.passwordCell,
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("action-editar"),
					iconClass: "pct pct-edit cor-action-default-able04",
					tooltipText: this.traducao.getLabel("btn-edit"),
					actionFn: (row) => this.navigateToEditarAutorizacaoAcesso(row),
				},
				{
					nome: this.traducao.getLabel("action-excluir"),
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					tooltipText: this.traducao.getLabel("btn-delete"),
					actionFn: (row) => this.navigateToEditarAutorizacaoAcesso(row),
				},
			],
		});
		// })
	}
}
