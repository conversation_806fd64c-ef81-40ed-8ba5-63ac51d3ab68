<div class="lista-alunos">
	<div class="line-text-title-fila">
		<span class="titulo column1" *ngIf="listaChamada">
			Alunos com reserva garantida
		</span>
		<span class="titulo column1" *ngIf="rankingSelfloops">
			Ranking Selfloops
		</span>
		<span class="column2 custom-buttom" *ngIf="integracaoSelfloopsAtivada">
			<pacto-cat-button
				(click)="visualizarListaChamada()"
				label="Lista de chamada"
				type="NO_BORDER"></pacto-cat-button>
			<span style="margin-right: 15px"></span>
			<pacto-cat-button
				(click)="visualizarRankingSelfloops()"
				label="Ranking da atividade"
				type="NO_BORDER"></pacto-cat-button>
		</span>
	</div>

	<div class="table-wrapper" *ngIf="listaChamada">
		<pacto-relatorio
			#tableData
			[table]="tableAlunos"
			actionTitulo="Ações"
			[dataFetchLoading]="true"
			[showShare]="false"
			[customEmptyContent]="customEmpty"
			[enableZebraStyle]="true"
			[alternatingColors]="'first'"
			i18n-actionTitulo="@@acoes:table-column-acoes"></pacto-relatorio>
		<pacto-cat-button
			*ngIf="liberado"
			id="btn-substituir"
			type="OUTLINE"
			(click)="confirmarTodosHandler()"
			label="Confirmar presença de todos"
			size="LARGE"></pacto-cat-button>
	</div>

	<div
		class="table-wrapper"
		*ngIf="integracaoSelfloopsAtivada && rankingSelfloops">
		<pacto-relatorio
			#tableDataRankingSelfloops
			[table]="tableRankingSelfloops"
			[dataFetchLoading]="true"
			[showShare]="false"
			[enableZebraStyle]="true"
			[alternatingColors]="'first'"
			i18n-actionTitulo="@@acoes:table-column-acoes"></pacto-relatorio>
		<pacto-cat-button
			id="btn-attranking"
			type="OUTLINE"
			(click)="atualizarRankingSelfloops()"
			label="Atualizar Ranking"
			size="LARGE"></pacto-cat-button>
	</div>
</div>

<ng-template #nomeColumnName>
	<span
		i18n="@@lista-alunos-detalhamento:table:cliente:title"
		class="font-size-larger">
		Cliente
	</span>
</ng-template>
<ng-template
	#nomeColumnContent
	let-item="item"
	class="row justify-content-start">
	<div class="d-flex align-items-center">
		<span class="capitalize font-size-larger me-2 mr-4">
			{{ item.nome.toLowerCase() }}
		</span>
		<pacto-cat-person-avatar
			*ngIf="
				item &&
				item.origemSistema &&
				item.origemSistema !== undefined &&
				item.origemSistema !== null
			"
			[diameter]="30"
			[uri]="
				'https://conversas.ai/wp-content/uploads/2024/09/cropped-Conversas-AI-logo-COLOR-192x192.png'
			"
			class="center-img"></pacto-cat-person-avatar>
	</div>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:situacao:title">Situação</span>
</ng-template>
<ng-template #situacaoColumnContent let-item="item">
	<span class="situacao">
		<pacto-cat-situacao-aluno
			[situacaoAluno]="item.situacaoAluno"
			class="item"></pacto-cat-situacao-aluno>
		<pacto-cat-situacao-contrato
			[situacaoContrato]="item.situacaoContrato"
			class="item"></pacto-cat-situacao-contrato>
	</span>
</ng-template>
<ng-template #fotoColumnContent let-item="item">
	<span class="foto-aluno" style="display: flex; align-items: center; gap: 8px">
		<!-- Espaço reservado dinamicamente -->
		<span
			class="boleto-icon"
			[ngStyle]="{
				width: hasAniversariante() ? '30px' : '0px',
				'text-align': 'center'
			}">
			<i *ngIf="isAniversario(item.dataNascimento)" class="pct pct-cake"></i>
		</span>

		<pacto-cat-person-avatar
			[diameter]="40"
			[uri]="item.imageUri"
			class="center-img"></pacto-cat-person-avatar>
	</span>
</ng-template>

<ng-template #statusColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:status:title">Status</span>
</ng-template>
<ng-template #statusColumnContent let-item="item">
	<span *ngIf="item.vinculoComAula" class="transform-none">
		{{ vinculoAulaTraducao.getLabel(item.vinculoComAula) }}
		{{ !item.origemSistema ? "" : " - AI" }}
	</span>
</ng-template>

<ng-template #horarioColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:horario-reserva:title">
		Horário da reserva
	</span>
</ng-template>
<ng-template #creditoColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:horario-reserva:title">
		Créditos
	</span>
</ng-template>
<ng-template #horarioColumnContent let-item="item">
	{{ item.horarioMarcacao | date : "dd/MM/yyyy HH:mm" }}
</ng-template>

<ng-template #equipamentoReservadoColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:equipamento-reserva:title">
		Reserva equip.
	</span>
</ng-template>
<ng-template #observacoesColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:equipamento-reserva:title">
		Observações
	</span>
</ng-template>
<ng-template #equipamentoReservadoColumnContent let-item="item">
	{{ getEquipamentoAluno(item) }}
</ng-template>
<ng-template #observacoesColumnContent let-item="item">
	<ng-container *ngIf="getObservacoes(item) | async as observacoes">
		<span class="link-cursor-observacoes" (click)="openObservations(item)">
			Observações ({{ observacoes?.length || 0 }})
		</span>
	</ng-container>
</ng-template>

<ng-template #acoesColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:acoes:title">Ações</span>
</ng-template>
<ng-template #posicaoRankingColumnContent let-item="item" let-i="index">
	<img
		*ngIf="i === 0"
		height="25"
		src="pacto-ui/images/medal-gold.png"
		width="17" />
	<img
		*ngIf="i === 1"
		height="25"
		src="pacto-ui/images/medal-silver.png"
		width="17" />
	<img
		*ngIf="i === 2"
		height="25"
		src="pacto-ui/images/medal-bronze.png"
		width="17" />
	{{ (i > 2 ? i + 1 + "ª " : " ") + item.nome.toLowerCase() }}
</ng-template>

<ng-template #averageColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:average:title">
		Potência média
	</span>
</ng-template>
<ng-template #averageColumnContent let-item="item">
	{{ item.averagePower ? item.averagePower : 0 }}
</ng-template>

<ng-template #caloriesColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:calories:title">Calorias</span>
</ng-template>
<ng-template #caloriesColumnContent let-item="item">
	{{ item.calories ? item.calories : 0 }}
</ng-template>

<ng-template #acoesColumnContent let-index="index" let-item="item">
	<div class="acoes">
		<i
			(click)="confirmarHandler(item)"
			*ngIf="!item.confirmado && item.vinculoComAula !== 'DESMARCADO'"
			class="pct desconfirmar pct-check-circle"
			i18n-title="@@agenda-detalhes-aula:confirmPresenca"
			id="acao-confirmar-presenca-{{ index }}"
			title="Confirmar presença"></i>
		<i
			(click)="desconfirmarHandler(item)"
			*ngIf="item.confirmado && item.vinculoComAula !== 'DESMARCADO'"
			class="pct confirmar pct-check-circle"
			i18n-title="@@agenda-detalhes-aula:desconfirmPresenca"
			id="acao-desconfirmar-presenca-{{ index }}"
			title="Desconfirmar presença"></i>
		<i
			(click)="removerHandler(item, false)"
			*ngIf="
				!item.confirmado &&
				item.vinculoComAula !== 'DESMARCADO' &&
				podeRemoverAluno
			"
			class="pct remover pct-trash-2"
			i18n-title="@@agenda-detalhes-aula:desmarcAluno"
			id="acao-desmarcar-aluno-{{ index }}"
			title="Desmarcar aluno"></i>
		<i
			class="pct pct-edit"
			*ngIf="turma.mapaEquipamentos"
			(click)="editarEquipamento(item)"
			i18n-title="@@agenda-detalhes-aula:desmarcAluno"
			title="Editar equipamento"
			id="acao-editar-equipamento-aluno-{{ index }}"></i>

		<i
			(click)="fixarAluno(item)"
			*ngIf="!item.fixo && !item.vinculoComAula && podeFixar"
			class="pct confirmar pct-pin"
			i18n-title="@@agenda-detalhes-aula:fixarAluno"
			id="acao-fixar-aluno-{{ index }}"
			title="Fixar aluno"></i>

		<i
			(click)="desafixarAluno(item)"
			*ngIf="item.fixo && podeFixar"
			class="pct confirmar pct-unpin"
			i18n-title="@@agenda-detalhes-aula:desafixarAluno"
			id="acao-desafixar-aluno-{{ index }}"
			title="Desafixar aluno"></i>
		<i
			(click)="justificarHandler(item)"
			*ngIf="
				!item.confirmado &&
				item.vinculoComAula == 'DESMARCADO' &&
				integracaoZW &&
				!turma?.aulaCheia &&
				item.justificativa != null &&
				item.justificativa != ''
			"
			class="fa fa-pencil"
			i18n-title="@@agenda-detalhes-aula:justificativaAluno"
			id="acao-justificativa-aluno-{{ index }}"
			title="Justificar falta aluno"></i>
	</div>
</ng-template>

<ng-template #presencaColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:presenca:title">Presença</span>
</ng-template>

<ng-container *ngIf="turma.mapaEquipamentos">
	<div class="col-md-12" *ngIf="turma">
		<map-selection
			#mapSelection
			[title]="'Professor(a): ' + turma.professor.nome"
			[listEquipamentosSelecionados]="turma.mapaEquipamentos"
			[listaMapaEquipamentoAparelho]="turma.listaMapaEquipamentoAparelho"
			[listEquipamentosOcupados]="getEquipamentosOcupados()"
			[apresentacao]="true"></map-selection>
	</div>
</ng-container>

<!-- Lista de Alunos na Fila de Espera -->
<ng-container *ngIf="espera">
	<div class="lista-alunos">
		<div class="line-text-title-fila">
			<span class="titulo column1">Alunos na fila de espera</span>
			<span class="column2 size16">
				<pacto-cat-button
					(click)="openModalAddFilaTurma()"
					*ngIf="temPermissaoFila && isTurmaSomenteLimite"
					i18n-label="@@integracoes:label-btn-salvar-alteracoes"
					id="btn-fila"
					label="Adicionar na fila de espera"
					size="LARGE"
					type="PRIMARY"></pacto-cat-button>
			</span>
		</div>
		<div class="table-wrapper">
			<pacto-relatorio
				#tableDataEspera
				(loadedData)="tableEsperaDataLoaded()"
				[alternatingColors]="'first'"
				[customEmptyContent]="customEmptyEspera"
				[dataFetchLoading]="true"
				[enableZebraStyle]="true"
				[showShare]="false"
				[table]="tableAlunosEspera"
				actionTitulo="Ações"
				i18n-actionTitulo="@@acoes:table-column-acoes"></pacto-relatorio>
		</div>
	</div>
</ng-container>
<ng-template #ordemColumnNameEspera>
	<span i18n="@@lista-alunos-detalhamento:table:ordenacao:title">
		Posição na fila
	</span>
</ng-template>
<ng-template #ordemColumnContentEspera let-item="item">
	<ds3-form-field *ngIf="item.formControl">
		<ds3-number-field
			(change)="alterarOrdemManual(item, $event)"
			(decrease)="diminuirOrdem(item)"
			(increase)="aumentarOrdem(item)"
			[formControl]="item.formControl"
			ds3Input></ds3-number-field>
	</ds3-form-field>
</ng-template>
<ng-template #nomeColumnNameEspera>
	<span i18n="@@lista-alunos-detalhamento:table:cliente:title">Cliente</span>
</ng-template>
<ng-template #nomeColumnContentEspera let-item="item">
	<span class="capitalize">
		{{ item.nomeAluno ? item.nomeAluno.toLowerCase() : "" }}
	</span>
</ng-template>
<ng-template #situacaoColumnNameEspera>
	<span i18n="@@lista-alunos-detalhamento:table:situacao:title">Situação</span>
</ng-template>
<ng-template #situacaoColumnContentEspera let-item="item">
	<span class="situacao">
		<pacto-cat-situacao-aluno
			[situacaoAluno]="item.situacaoAluno"
			class="item"></pacto-cat-situacao-aluno>
		<pacto-cat-situacao-contrato
			[situacaoContrato]="item.situacaoContrato"
			class="item"></pacto-cat-situacao-contrato>
	</span>
</ng-template>
<ng-template #fotoColumnContentEspera let-item="item">
	<span class="foto-aluno">
		<pacto-cat-person-avatar
			[diameter]="40"
			[uri]="item.imageUri"
			class="center-img"></pacto-cat-person-avatar>
	</span>
</ng-template>
<ng-template #horarioColumnNameEspera>
	<span i18n="@@lista-alunos-detalhamento:table:horario-reserva:title">
		Horário da reserva
	</span>
</ng-template>
<ng-template #horarioColumnContentEspera let-item="item">
	{{ item.dia | date : "dd/MM/yyyy HH:mm" }}
</ng-template>
<ng-template #creditoColumnContent let-item="item">
	<span title="{{ item.toolTipCredito }}">{{ item.credito }}</span>
</ng-template>

<ng-template #acoesColumnNameEspera>
	<span i18n="@@lista-alunos-detalhamento:table:acoes:title">Ações</span>
</ng-template>

<ng-template #acoesColumnContentEspera let-index="index" let-item="item">
	<div class="acoes">
		<i
			(click)="removerHandler(item, true)"
			*ngIf="podeRemoverAluno && espera"
			class="pct remover pct-trash-2"
			i18n-title="@@agenda-detalhes-aula:desmarcAluno"
			id="acao-desmarcar-aluno-fila-espera-{{ index }}"
			title="Desmarcar aluno da fila de espera"></i>
	</div>
</ng-template>
<ng-template #customEmptyEspera>
	<div class="empty-turma">
		<img src="pacto-ui/images/empty-state-turma.svg" />
		<span class="titulo">Fila de espera vazia</span>
		<span>Ainda não possuímos alunos nesta fila de espera</span>
	</div>
</ng-template>

<!-- -&&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->

<pacto-traducoes-xingling #vinculoAulaTraducao>
	<span i18n="@@agenda-detalhes-aula:turmaCheia" xingling="turmaCheia">
		A turma está cheia.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemcadastroZW"
		xingling="naoTemcadastroZW">
		O aluno não tem cadastro no ZW
	</span>
	<span
		i18n="@@agenda-detalhes-aula:editarReposicaoAula"
		xingling="editarReposicaoAula">
		Editar reposição de aula
	</span>
	<span i18n="@@agenda-detalhes-aula:marcacaoAula" xingling="marcacaoAula">
		Marcação de aula
	</span>
	<span
		i18n="@@agenda-detalhes-aula:editarExperimental"
		xingling="editarExperimental">
		Editar aula experimental
	</span>
	<span
		i18n="@@agenda-detalhes-aula:inseridoSucesso"
		xingling="inseridoSucesso">
		Aluno inserido com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:MATRICULADO" xingling="MATRICULADO">
		Matriculado
	</span>
	<span i18n="@@agenda-detalhes-aula:REPOSICAO" xingling="REPOSICAO">
		Reposição
	</span>
	<span i18n="@@agenda-detalhes-aula:REPOSICAO" xingling="INTEGRACAO">
		Integração
	</span>
	<span i18n="@@agenda-detalhes-aula:DEPENDENTE" xingling="DEPENDENTE">
		Dependente
	</span>
	<span i18n="@@agenda-detalhes-aula:DESMARCADO" xingling="DESMARCADO">
		Desmarcado
	</span>
	<span
		i18n="@@agenda-detalhes-aula:AULA_EXPERIMENTAL"
		xingling="AULA_EXPERIMENTAL">
		Aula experimental
	</span>
	<span
		i18n="@@agenda-detalhes-aula:PARTICIPANTE_FIXO"
		xingling="PARTICIPANTE_FIXO">
		Participante fixo desta aula
	</span>
	<span i18n="@@agenda-detalhes-aula:DIARIA" xingling="DIARIA">Diaria</span>
	<span i18n="@@agenda-detalhes-aula:ESPERA" xingling="ESPERA">
		Fila de espera
	</span>
	<span i18n="@@agenda-detalhes-aula:DESAFIO" xingling="DESAFIO">Desafio</span>
	<span i18n="@@agenda-detalhes-aula:DIARIA_GYMPASS" xingling="DIARIA_GYMPASS">
		Diaria gympass
	</span>
	<span
		i18n="@@agenda-detalhes-aula:DIARIA_TOTALPASS"
		xingling="DIARIA_TOTALPASS">
		Diaria totalpass
	</span>
	<span i18n="@@agenda-detalhes-aula:MARCACAO" xingling="MARCACAO">
		Marcação
	</span>
	<span i18n="@@agenda-detalhes-aula:VISITANTE" xingling="VISITANTE">
		Visitante
	</span>
	<span i18n="@@agenda-detalhes-aula:VISITANTE" xingling="CONTRATO_FUTURO">
		Aluno com contrato futuro e não atende demais condições para ser inserido na
		aula ou turma
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #modalDesmarcarAluno>
	<span i18n="@@agenda-detalhes-modalDesmarcar:title" xingling="title">
		Desmarcar aula
	</span>
	<span i18n="@@agenda-detalhes-modalDesmarcar:title" xingling="title2">
		Desmarcar turma
	</span>
	<span xingling="body">
		Deseja realmente remover {{ alunoASerRemovido }} desta aula?
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #modalDesmarcarAlunoEspera>
	<span
		i18n="@@agenda-detalhes-modalDesmarcarAlunoEspera:title"
		xingling="title">
		Desmarcar fila de espera
	</span>
	<span xingling="body">
		Deseja realmente remover {{ alunoASerRemovido }} desta fila de espera?
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #notificacoes>
	<span i18n="@@agenda-detalhes-aula:confirmSuccess" xingling="confirmSuccess">
		Confirmado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:confirmSuccess"
		xingling="confirmedSuccess">
		Alunos confirmados com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:confirmSuccess"
		xingling="atualizadoSuccess">
		Ranking atualizado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:desconfirmSuccess"
		xingling="desconfirmSuccess">
		Desconfirmado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:desmarcadoSuccess"
		xingling="desmarcadoSuccess">
		Desmarcado com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:validCreditos" xingling="validCreditos">
		O número de aulas não pode exceder a quantidade de créditos.
	</span>
	<span i18n="@@agenda-detalhes-aula:validPermissao" xingling="validPermissao">
		Ops! Essa turma não permite aula experimental ou diária.
	</span>
	<span i18n="@@agenda-detalhes-aula:soPodeMarcar" xingling="soPodeMarcar">
		Você só pode marcar
	</span>
	<span i18n="@@agenda-detalhes-aula:aposInicioAula" xingling="aposInicioAula">
		após o início da aula.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeMarcarAte"
		xingling="soPodeMarcarAte">
		Você só pode marcar até
	</span>
	<span
		i18n="@@agenda-detalhes-aula:antesInicioAula"
		xingling="antesInicioAula">
		antes do início da aula.
	</span>
	<span i18n="@@agenda-detalhes-aula:aulaEstaCheia" xingling="aulaEstaCheia">
		A aula já está cheia!
	</span>
	<span
		i18n="@@agenda-detalhes-aula:semPermissaoDiariaFreePass"
		xingling="semPermissaoDiariaFreePass">
		Você precisa ter as permissões: (2.51 - Lançar Free Pass), (4.01 - Diária),
		para realizar esta operação.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:jaMatriculouNaAula"
		xingling="jaMatriculouNaAula">
		Você já se matriculou nessa aula!
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemModalidadeExperimental"
		xingling="naoTemModalidadeExperimental">
		O aluno não tem essa modalidade, deseja usar uma de suas
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemModalidadeExperimental2"
		xingling="naoTemModalidadeExperimental2">
		aulas experimentais?
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeDesmarcarAulaAte"
		xingling="soPodeDesmarcarAulaAte">
		Você só pode desmarcar a aula com até
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeDesmarcarAulaAte2"
		xingling="soPodeDesmarcarAulaAte2">
		de antecedência
	</span>
	<span
		i18n="@@agenda-detalhes-aula:foraHorarioPlano"
		xingling="foraHorarioPlano">
		A aula não está dentro do horário do seu plano, deseja usar uma de suas
	</span>
	<span
		i18n="@@agenda-detalhes-aula:foraHorarioPlano2"
		xingling="foraHorarioPlano2">
		aulas experimentais?
	</span>
	<span
		i18n="@@agenda-detalhes-aula:aulaCheiaJustificada"
		xingling="aulaCheiaJustificada">
		Falta justificada com sucesso.
	</span>
</pacto-traducoes-xingling>
