export class ModeloContrato {
	codigo: number;
	dataDefinicao: any;
	descricao: string;
	imagemLogo?: any;
	responsavelDefinicao?: {
		codigo: number;
		nome: string;
	};
	nomeResponsavel: string;
	situacao: string;
	texto: string;
	tipoContrato: string;
	replicar: boolean;
}

// export const marcadoresCliente = [
//     { tag: '[(60){}AssinaturaDigital_Cliente]', nome: 'AssinaturaDigital' },
//     { tag: '[(10){}Matricula_Cliente]', nome: 'Matricula_CLiente' },
//     { tag: '[(61){}AssinaturaBiometriaDigital_Cliente]', nome: 'AssinaturaBiometriaDigital' },
//     { tag: '[(30){}Banco_Cliente]', nome: 'Banco_CLiente' },
//     { tag: '[(20){}Agencia_Cliente]', nome: 'Agencia_CLiente' },
//     { tag: '[(20){}Conta_Cliente]', nome: 'Conta_Cliente' },
//     { tag: '[(50){}Webpage_Cliente]', nome: 'Webpage_Cliente' },
//     { tag: '[(2){}Sexo_Cliente]', nome: 'Sexo_Cliente' },
//     { tag: '[(20){}Naturalidade_Cliente]', nome: 'Naturalidade_Cliente' },
//     { tag: '[(10){}EstadoCivil_Cliente]', nome: 'EstadoCivil_Cliente' },
//     { tag: '[(20){}Rg_Cliente]', nome: 'Rg_Cliente' },
//     { tag: '[(14){}Cpf_Cliente]', nome: 'Cpf_Cliente' },
//     { tag: '[(20){}DataNasc_Cliente]', nome: 'DataNasc_Cliente' },
//     { tag: '[(80){}Nome_Cliente]', nome: 'Nome_Cliente' },
//     { tag: '[(10){}Codigo_Cliente]', nome: 'Codigo_Cliente' },
//     { tag: '[(20){}Telefone_Cliente]', nome: 'Telefone_Cliente' },
//     { tag: '[(20){}Telefone_Celular_Cliente]', nome: 'Telefone_Celular_Cliente' },
//     { tag: '[(20){}Telefone_Comercial_Cliente]', nome: 'Telefone_Comercial_Cliente' },
//     { tag: '[(40){}Endereco_Cliente]', nome: 'Endereco_Cliente' },
//     { tag: '[(5){}Endereco_Numero_Cliente]', nome: 'Endereco_Numero_Cliente' },
//     { tag: '[(20){}Endereco_Cidade_Cliente]', nome: 'Endereco_Cidade_Cliente' },
//     { tag: '[(20){}Endereco_Estado_Cliente]', nome: 'Endereco_Estado_Cliente' },
//     { tag: '[(10){}CEP_Cliente]', nome: 'CEP_Cliente' },
//     { tag: '[(40){}Profissao_Cliente]', nome: 'Profissao_Cliente' },
//     { tag: '[(40){}Mae_Cliente]', nome: 'Mae_Cliente' },
//     { tag: '[(40){}Pai_Cliente]', nome: 'Pai_Cliente' },
//     { tag: '[(40){}Responsavel_Cliente]', nome: 'Responsavel_Cliente' },
//     { tag: '[(14){}Responsavel_Cliente_Cpf]', nome: 'Responsavel_Cliente_Cpf' },
//     { tag: '[(20){}Responsavel_Cliente_Rg]', nome: 'Responsavel_Cliente_Rg' },
//     { tag: '[(40){}Responsavel_Cliente_Pai]', nome: 'Responsavel_Cliente_Pai' },
//     { tag: '[(40){}Responsavel_Cliente_Pai_Cpf]', nome: 'Responsavel_Cliente_Pai_Cpf' },
//     { tag: '[(20){}Responsavel_Cliente_Pai_Rg]', nome: 'Responsavel_Cliente_Pai_Rg' },
//     { tag: '[(40){}Responsavel_Cliente_Mae]', nome: 'Responsavel_Cliente_Mae' },
//     { tag: '[(40){}Responsavel_Cliente_Mae_Cpf]', nome: 'Responsavel_Cliente_Mae_Cpf' },
//     { tag: '[(20){}Responsavel_Cliente_Mae_Rg]', nome: 'Responsavel_Cliente_Mae_Rg' },
//     { tag: '[(40){}ComplementoEnd_Cliente]', nome: 'ComplementoEnd_Cliente' },
//     { tag: '[(40){}Email_Cliente]', nome: 'Email_Cliente' },
//     { tag: '[(50){}Grupo_Cliente]', nome: 'Grupo_Cliente' },
//     { tag: '[(250){}AutorizacaoCobranca_Cliente]', nome: 'AutorizacaoCobranca_Cliente' },
//     { tag: '[(250){}Observacao_Cliente]', nome: 'Observacao_Cliente' },
//     { tag: '[(50){}Categoria_Cliente]', nome: 'Categoria_Cliente' },
//     { tag: '[(50){}BairroEnd_Cliente]', nome: 'BairroEnd_Cliente' },
//     { tag: '[(50){}ConsultorAtual_Cliente]', nome: 'ConsultorAtual_Cliente' },
//     { tag: '[(80){}Responsavel_Cliente_Cpf_Nome]', nome: 'Responsavel_Cliente_Cpf_Nome' },
//     { tag: '[(20){}Habilitacao_Sesc_Cliente]', nome: 'Habilitacao_Sesc_Cliente' },
//     { tag: '[(30){}Contato_Emergencia_Cliente]', nome: 'Contato_Emergencia_Cliente' },
//     { tag: '[(20){}Telefone_Emergencia_Cliente]', nome: 'Telefone_Emergencia_Cliente' },
//     { tag: '[(50){}Bandeira_Cartao_Cliente]', nome: 'Bandeira_Cartao_Cliente' },
//     { tag: '[(50){}Nome_Titular_Cartao_Cliente]', nome: 'Nome_Titular_Cartao_Cliente' },
//     { tag: '[(100){}Numero_Cartao_Cliente]', nome: 'Numero_Cartao_Cliente' },
//     { tag: '[(7){}aValidade_Cartao_Cliente]', nome: 'aValidade_Cartao_Cliente' },
//     { tag: '[(14){}CPF_Titular_Cartao_Cliente]', nome: 'CPF_Titular_Cartao_Cliente' },
// ];

// export const marcadoresContrato = [
//     { tag: '[(10){}Codigo_Contrato]', nome: 'Codigo_Contrato' },
//     { tag: '[(50){}Responsavel_Contrato]', nome: 'Responsavel_Contrato' },
//     { tag: '[(50){}ConsultorResponsavel_Contrato]', nome: 'ConsultorResponsavel_Contrato' },
//     { tag: '[(20){}VigenciaDe_Contrato]', nome: 'VigenciaDe_Contrato' },
//     { tag: '[(20){}VigenciaAte_Contrato]', nome: 'VigenciaAte_Contrato' },
//     { tag: '[(20){}VigenciaAteAjustada_Contrato]', nome: 'VigenciaAteAjustada_Contrato' },
//     { tag: '[(10){}Duracao_Contrato]', nome: 'Duracao_Contrato' },
//     { tag: '[(20){}Horario_Contrato]', nome: 'Horario_Contrato' },
//     { tag: '[(15){}ValorBaseCalculo_Contrato]', nome: 'ValorBaseCalculo_Contrato' },
//     { tag: '[(15){}ValorFinal_Contrato]', nome: 'ValorFinal_Contrato' },
//     { tag: '[(250){}Observacao_Contrato]', nome: 'Observacao_Contrato' },
//     { tag: '[(10){}NomeModalidades_Contrato]', nome: 'NomeModalidades_Contrato' },
//     { tag: '[(250){}NomeCompletoModalidades_Contrato]', nome: 'NomeCompletoModalidades_Contrato' },
//     { tag: '[(250){}NrVezesNomeCompletoModalidades_Contrato]', nome: 'NrVezesNomeCompletoModalidades_Contrato' },
//     { tag: '[(10){}valorMensal_Contrato]', nome: 'valorMensal_Contrato' },
//     { tag: '[(10){}valorMensalDesconto_Contrato]', nome: 'valorMensalDesconto_Contrato' },
//     { tag: '[(10){}valorMensalAdequado_Contrato]', nome: 'valorMensalAdequado_Contrato' },
//     { tag: '[(10){}ValorMensalBase_Contrato]', nome: 'ValorMensalBase_Contrato' },
//     { tag: '[(10){}valorDescontoExtra_Contrato]', nome: 'valorDescontoExtra_Contrato' },
//     { tag: '[(10){}DtLancamento_Contrato]', nome: 'DtLancamento_Contrato' },
//     { tag: '[(80){}ValorPorExtenso_Contrato]', nome: 'ValorPorExtenso_Contrato' },
//     { tag: '[(50){}Convenio_Contrato]', nome: 'Convenio_Contrato' },
//     { tag: '[(50){}ValorMatricula_Contrato]', nome: 'ValorMatricula_Contrato' },
//     { tag: '[(50){}ValorRematricula_Contrato]', nome: 'ValorRematricula_Contrato' },
//     { tag: '[(50){}CondicaoPagamento_Contrato]', nome: 'CondicaoPagamento_Contrato' },
//     { tag: '[(50){}NomeProduto_Contrato]', nome: 'NomeProduto_Contrato' },
//     { tag: '[(50){}QtdProduto_Contrato]', nome: 'QtdProduto_Contrato' },
//     { tag: '[(50){}ValorProduto_Contrato]', nome: 'ValorProduto_Contrato' },
//     { tag: '[(50){}ValorDescontoProduto_Contrato]', nome: 'ValorDescontoProduto_Contrato' },
//     { tag: '[(50){}ValorUnitarioProduto_Contrato]', nome: 'ValorUnitarioProduto_Contrato' },
//     { tag: '[(50){}TabelaProdutos_Contrato]', nome: 'TabelaProdutos_Contrato' },
//     { tag: '[(250){}ConvenioDescontoResumo_Contrato]', nome: 'ConvenioDescontoResumo_Contrato' },
//     { tag: '[(2){}diaVencimentoCartao_ContratoRecorrencia]', nome: 'diaVencimentoCartao_ContratoRecorrencia' },
//     { tag: '[(10){}valorAnuidade_ContratoRecorrencia]', nome: 'valorAnuidade_ContratoRecorrencia' },
//     { tag: '[(50){}valorTotalSemDesconto_Contrato]', nome: 'valorTotalSemDesconto_Contrato' },
//     { tag: '[(50){}valorTotalDescontoContrato_Contrato]', nome: 'valorTotalDescontoContrato_Contrato' },
//     { tag: '[(50){}valorTotalContratoSemDescontoExtra_Contrato]', nome: 'valorTotalContratoSemDescontoExtra_Contrato' },
//     { tag: '[(50){}valorParcelaMensal_Contrato]', nome: 'valorParcelaMensal_Contrato' },
//     { tag: '[(10){}quantidadeCreditoTreino_Contrato]', nome: 'quantidadeCreditoTreino_Contrato' },
//     { tag: '[(10){}totalDias_Contrato]', nome: 'totalDias_Contrato' },
//     { tag: '[(3){}DiasCarencia_Contrato]', nome: 'DiasCarencia_Contrato' },
//     { tag: '[(250){}NomeModalidadesNrVezes_Contrato]', nome: 'NomeModalidadesNrVezes_Contrato' },
//     { tag: '[(15){}ValorAdesao_Contrato]', nome: 'ValorAdesao_Contrato' },
//     { tag: '[(15){}Saldo_Credito_Contrato]', nome: 'Saldo_Credito_Contrato' },
//     { tag: '[(50){}ValorCheioMatricula_Contrato]', nome: 'ValorCheioMatricula_Contrato' },
//     { tag: '[(50){}ValorDescontoMatricula_Contrato]', nome: 'ValorDescontoMatricula_Contrato' },
//     { tag: '[(50){}ValorCheioRematricula_Contrato]', nome: 'ValorCheioRematricula_Contrato' },
//     { tag: '[(50){}ValorDescontoRematricula_Contrato]', nome: 'ValorDescontoRematricula_Contrato' },
//     { tag: '[(10){}valorDescontoAnuidade_ContratoRecorrencia]', nome: 'valorDescontoAnuidade_ContratoRecorrencia' },
//     { tag: '[(10){}valorFinalAnuidade_ContratoRecorrencia]', nome: 'valorFinalAnuidade_ContratoRecorrencia' },
// ];

// export const marcadoresPlano = [
//     { tag: '[(10){}Codigo_Plano]', nome: 'Codigo_Plano' },
//     { tag: '[(200){}Descricao_Plano]', nome: 'Descricao_Plano' },
//     { tag: '[(20){}VigenciaDe_Plano]', nome: 'VigenciaDe_Plano' },
//     { tag: '[(20){}VigenciaAte_Plano]', nome: 'VigenciaAte_Plano' },
//     { tag: '[(20){}IngressoAte_Plano]', nome: 'IngressoAte_Plano' },
// ];

// export const marcadoresModalidade = [
//     { tag: '[(10){}Codigo_Modalidade]', nome: 'Codigo_Modalidade' },
//     { tag: '[(15){}ValorMensal_Modalidade]', nome: 'ValorMensal_Modalidade' },
//     { tag: '[(10){}NrVezes_Modalidade]', nome: 'NrVezes_Modalidade' },
//     { tag: '[(50){}Nome_Modalidade]', nome: 'Nome_Modalidade' },
//     { tag: '[(80){}NomeVezes_Modalidade]', nome: 'NomeVezes_Modalidade' },
// ];

// export const marcadoresTurma = [
//     { tag: '[(10){}Codigo_Turma]', nome: 'Codigo da Turma' },
//     { tag: '[(200){}Descricao_Turma]', nome: 'Horário da turma (Completo)' },
//     { tag: '[(20){}Identificador_Turma]', nome: 'Identificador da Turma' },
//     { tag: '[(20){}DataInicioVigencia_Turma]', nome: 'Data Inicial da Vigência da Turma' },
//     { tag: '[(20){}DataFinalVigencia_Turma]', nome: 'Data Final da Vigência da Turma' },
//     { tag: '[(5){}IdadeMinima_Turma]', nome: 'Idade Mínima da Turma' },
//     { tag: '[(5){}IdadeMaxima_Turma]', nome: 'Idade Máxima da Turma' },
//     { tag: '[(200){}DescricaoCurta_Turma]', nome: 'Horário da turma (Resumida)' },
// ];

// export const marcadoresMovParcela = [
//     { tag: '[(10){}Codigo_MovParcela]', nome: 'Codigo_MovParcela' },
//     { tag: '[(20){}DataVencimento_MovParcela]', nome: 'DataVencimento_MovParcela' },
//     { tag: '[(15){}ValorParcela_MovParcela]', nome: 'ValorParcela_MovParcela' },
//     { tag: '[(15){}PercentualMulta_MovParcela]', nome: 'PercentualMulta_MovParcela' },
//     { tag: '[(15){}PercentualJuro_MovParcela]', nome: 'PercentualJuro_MovParcela' },
//     { tag: '[(60){}Descricao_MovParcela]', nome: 'Descricao_MovParcela' },
// ];

// export const marcadoresReciboPagamento = [
//     { tag: '[(10){}codigo_Recibo]', nome: 'codigo_Recibo' },
//     { tag: '[(10){}valorTotal_Recibo]', nome: 'valorTotal_Recibo' },
//     { tag: '[(200){}nomePessoaPagador_Recibo]', nome: 'nomePessoaPagador_Recibo' },
//     { tag: '[(200){}responsavelLancamento_Recibo]', nome: 'responsavelLancamento_Recibo' },
//     { tag: '[(10){}contrato_Recibo]', nome: 'contrato_Recibo' },
//     { tag: '[(10){}data_Recibo]', nome: 'data_Recibo' },
//     { tag: '[(200){}valorPorExtenso_Recibo]', nome: 'valorPorExtenso_Recibo' },
//     { tag: '[(20){}dataImpressao_Recibo]', nome: 'dataImpressao_Recibo' },
// ];

// export const marcadoresMovPagamento = [
//     { tag: '[(40){}tipoFormaPagamento_MovPagamento]', nome: 'tipoFormaPagamento_MovPagamento' },
//     { tag: '[(20){ Operadora do Cartão: } operadoraCC_MovPagamento]', nome: 'operadoraCC_MovPagamento' },
//     { tag: '[(40){}valor_MovPagamento]', nome: 'valor_MovPagamento' },
//     // numero de vezes no cartao de credito
//     { tag: '[(20) { Vezes: } parcelasCC_MovPagamento]', nome: 'parcelasCC_MovPagamento' },
//     { tag: '[(20){}valor_Cheque]', nome: 'valor_Cheque' },
//     { tag: '[(20){}banco_Cheque]', nome: 'banco_Cheque' },
//     { tag: '[(20){}agencia_Cheque]', nome: 'agencia_Cheque' },
//     { tag: '[(20){}contaCorrente_Cheque]', nome: 'contaCorrente_Cheque' },
//     { tag: '[(20){}numero_Cheque]', nome: 'numero_Cheque' },
//     { tag: '[(20){}dataCompensacao_Cheque]', nome: 'dataCompensacao_Cheque' },
// ];

// export const marcadoresPacote = [
//     { tag: '[(10){}Codigo_Composicao]', nome: 'Codigo_Composicao' },
//     { tag: '[(45){}Descricao_Composicao]', nome: 'Descricao_Composicao' },
//     { tag: '[(15){}PrecoComposicao_Composicao]', nome: 'PrecoComposicao_Composicao' },
// ];

// export const marcadoresUsuario = [
//     { tag: '[(10){}Codigo_Usuario]', nome: 'Codigo_Usuario' },
//     { tag: '[(100){}Nome_Usuario]', nome: 'Nome_Usuario' },
//     { tag: '[(10){}Username_Usuario]', nome: 'Username_Usuario' },
//     { tag: '[(60){}Usuario_Assinatura]', nome: 'Usuario_Assinatura' },
// ];

// export const marcadoresEmpresa = [
//     { tag: '[(10){}Codigo_Empresa]', nome: 'Codigo_Empresa' },
//     { tag: '[(40){}Assinatura_Empresa]', nome: 'Assinatura_Empresa' },
//     { tag: '[(14){}Fax_Empresa]', nome: 'Fax_Empresa' },
//     { tag: '[(50){}Site_Empresa]', nome: 'Site_Empresa' },
//     { tag: '[(50){}Email_Empresa]', nome: 'Email_Empresa' },
//     { tag: '[(14){}TelComercial1_Empresa]', nome: 'TelComercial1_Empresa' },
//     { tag: '[(14){}TelComercial2_Empresa]', nome: 'TelComercial2_Empresa' },
//     { tag: '[(14){}TelComercial3_Empresa]', nome: 'TelComercial3_Empresa' },
//     { tag: '[(20){}InscEstadual_Empresa]', nome: 'InscEstadual_Empresa' },
//     { tag: '[(18){}Cnpj_Empresa]', nome: 'Cnpj_Empresa' },
//     { tag: '[(10){}Cep_Empresa]', nome: 'Cep_Empresa' },
//     { tag: '[(50){}Complemento_Empresa]', nome: 'Complemento_Empresa' },
//     { tag: '[(5){}Numero_Empresa]', nome: 'Numero_Empresa' },
//     { tag: '[(20){}Setor_Empresa]', nome: 'Setor_Empresa' },
//     { tag: '[(50){}Endereco_Empresa]', nome: 'Endereco_Empresa' },
//     { tag: '[(50){}RazaoSocial_Empresa]', nome: 'RazaoSocial_Empresa' },
//     { tag: '[(50){}Nome_Empresa]', nome: 'Nome_Empresa' },
//     { tag: '[(50){}Cidade_Empresa]', nome: 'Cidade_Empresa' },
//     { tag: '[(50){}Estado_Empresa]', nome: 'Estado_Empresa' },
// ];

// export const marcadoresVenda = [
//     { tag: '[(20){}Data_Venda]', nome: 'Data_Venda' },
//     { tag: '[(15){}ValorTotal_Venda]', nome: 'ValorTotal_Venda' },
//     { tag: '[(15){}ValorFinal_Venda]', nome: 'ValorFinal_Venda' },
//     { tag: '[(15){}ValorDesconto_Venda]', nome: 'ValorDesconto_Venda' },
//     { tag: '[(5){}Codigo_Venda]', nome: 'Codigo_Venda' },
// ];

// export const marcadoresItensVenda = [
//     { tag: '[(5){}Codigo_Itens]', nome: 'Codigo_Itens' },
//     { tag: '[(150){}Descricao_Itens]', nome: 'Descricao_Itens' },
//     { tag: '[(20){}ValorUnitario_Itens]', nome: 'ValorUnitario_Item_Venda' },
//     { tag: '[(20){}ValorDesconto_Itens]', nome: 'ValorDesconto_Item_Venda' },
//     { tag: '[(20){}ValorFinal_Itens]', nome: 'ValorFinal_Item_Venda' },
//     { tag: '[(20){}Quantidade_Itens]', nome: 'Quantidade_Itens' },
// ];

// export const marcadoresPacoteVenda = [
//     { tag: '[(5){}Codigo_PacoteItem]', nome: 'Codigo_PacoteItem' },
//     { tag: '[(50){}Descricao_PacoteItem]', nome: 'Descricao_PacoteItem' },
//     { tag: '[(15){}Valor_PacoteItem]', nome: 'Valor_PacoteItem' },
// ];

export const marcadoresCliente = [
	{
		tag: "[(60){}AssinaturaDigital_Cliente]",
		nome: "AssinaturaDigital",
		translationId: "assinaturadigital",
	},
	{
		tag: "[(60){}AssinaturaDigital_RespFinanceiro_Cliente]",
		nome: "AssinaturaDigitalRespFinanceiro",
		translationId: "assinaturadigitalrespfinanceiro",
	},
	{
		tag: "[(10){}Matricula_Cliente]",
		nome: "Matricula_CLiente",
		translationId: "matricula-cliente",
	},
	{
		tag: "[(61){}AssinaturaBiometriaDigital_Cliente]",
		nome: "AssinaturaBiometriaDigital",
		translationId: "assinaturabiometriadigital",
	},
	{
		tag: "[(30){}Banco_Cliente]",
		nome: "Banco_CLiente",
		translationId: "banco-cliente",
	},
	{
		tag: "[(20){}Agencia_Cliente]",
		nome: "Agencia_CLiente",
		translationId: "agencia-cliente",
	},
	{
		tag: "[(20){}Conta_Cliente]",
		nome: "Conta_Cliente",
		translationId: "conta-cliente",
	},
	{
		tag: "[(75){}NowLocationIpVOnline_Cliente]",
		nome: "NowLocationIpVOnline_Cliente",
		translationId: "nowlocationipvonline_cliente",
	},
	{
		tag: "[(50){}Webpage_Cliente]",
		nome: "Webpage_Cliente",
		translationId: "webpage-cliente",
	},
	{
		tag: "[(2){}Sexo_Cliente]",
		nome: "Sexo_Cliente",
		translationId: "sexo-cliente",
	},
	{
		tag: "[(20){}Naturalidade_Cliente]",
		nome: "Naturalidade_Cliente",
		translationId: "naturalidade-cliente",
	},
	{
		tag: "[(10){}EstadoCivil_Cliente]",
		nome: "EstadoCivil_Cliente",
		translationId: "estadocivil-cliente",
	},
	{
		tag: "[(20){}Rg_Cliente]",
		nome: "Rg_Cliente",
		translationId: "rg-cliente",
	},
	{
		tag: "[(14){}Cpf_Cliente]",
		nome: "Cpf_Cliente",
		translationId: "cpf-cliente",
	},
	{
		tag: "[(14){}Cpf_ResponsavelLegal_Cliente]",
		nome: "Cpf_ResponsavelLegal_Cliente",
		translationId: "cpf-responsavel-legal-cliente",
	},
	{
		tag: "[(20){}DataNasc_Cliente]",
		nome: "DataNasc_Cliente",
		translationId: "datanasc-cliente",
	},
	{
		tag: "[(80){}Nome_Cliente]",
		nome: "Nome_Cliente",
		translationId: "nome-cliente",
	},
	{
		tag: "[(80){}Nome_ResponsavelLegal_Cliente]",
		nome: "Nome_ResponsavelLegal_Cliente",
		translationId: "nome-responsavel-legal-cliente",
	},
	{
		tag: "[(10){}Codigo_Cliente]",
		nome: "Codigo_Cliente",
		translationId: "codigo-cliente",
	},
	{
		tag: "[(20){}Telefone_Cliente]",
		nome: "Telefone_Cliente",
		translationId: "telefone-cliente",
	},
	{
		tag: "[(20){}Telefone_Celular_Cliente]",
		nome: "Telefone_Celular_Cliente",
		translationId: "telefone-celular-cliente",
	},
	{
		tag: "[(20){}Telefone_Comercial_Cliente]",
		nome: "Telefone_Comercial_Cliente",
		translationId: "telefone-comercial-cliente",
	},
	{
		tag: "[(40){}Endereco_Cliente]",
		nome: "Endereco_Cliente",
		translationId: "endereco-cliente",
	},
	{
		tag: "[(5){}Endereco_Numero_Cliente]",
		nome: "Endereco_Numero_Cliente",
		translationId: "endereco-numero-cliente",
	},
	{
		tag: "[(20){}Endereco_Cidade_Cliente]",
		nome: "Endereco_Cidade_Cliente",
		translationId: "endereco-cidade-cliente",
	},
	{
		tag: "[(20){}Endereco_Estado_Cliente]",
		nome: "Endereco_Estado_Cliente",
		translationId: "endereco-estado-cliente",
	},
	{
		tag: "[(10){}CEP_Cliente]",
		nome: "CEP_Cliente",
		translationId: "cep-cliente",
	},
	{
		tag: "[(40){}Profissao_Cliente]",
		nome: "Profissao_Cliente",
		translationId: "profissao-cliente",
	},
	{
		tag: "[(40){}Mae_Cliente]",
		nome: "Mae_Cliente",
		translationId: "mae-cliente",
	},
	{
		tag: "[(40){}Pai_Cliente]",
		nome: "Pai_Cliente",
		translationId: "pai-cliente",
	},
	{
		tag: "[(40){}Responsavel_Cliente]",
		nome: "Responsavel_Cliente",
		translationId: "responsavel-cliente",
	},
	{
		tag: "[(14){}Responsavel_Cliente_Cpf]",
		nome: "Responsavel_Cliente_Cpf",
		translationId: "responsavel-cliente-cpf",
	},
	{
		tag: "[(20){}Responsavel_Cliente_Rg]",
		nome: "Responsavel_Cliente_Rg",
		translationId: "responsavel-cliente-rg",
	},
	{
		tag: "[(40){}Responsavel_Cliente_Pai]",
		nome: "Responsavel_Cliente_Pai",
		translationId: "responsavel-cliente-pai",
	},
	{
		tag: "[(40){}Responsavel_Cliente_Pai_Cpf]",
		nome: "Responsavel_Cliente_Pai_Cpf",
		translationId: "responsavel-cliente-pai-cpf",
	},
	{
		tag: "[(20){}Responsavel_Cliente_Pai_Rg]",
		nome: "Responsavel_Cliente_Pai_Rg",
		translationId: "responsavel-cliente-pai-rg",
	},
	{
		tag: "[(40){}Responsavel_Cliente_Mae]",
		nome: "Responsavel_Cliente_Mae",
		translationId: "responsavel-cliente-mae",
	},
	{
		tag: "[(40){}Responsavel_Cliente_Mae_Cpf]",
		nome: "Responsavel_Cliente_Mae_Cpf",
		translationId: "responsavel-cliente-mae-cpf",
	},
	{
		tag: "[(20){}Responsavel_Cliente_Mae_Rg]",
		nome: "Responsavel_Cliente_Mae_Rg",
		translationId: "responsavel-cliente-mae-rg",
	},
	{
		tag: "[(40){}Responsavel_Financeiro_Nome_Cliente]",
		nome: "Responsavel_Financeiro_Nome",
		translationId: "responsavel-financeiro-nome",
	},
	{
		tag: "[(40){}Responsavel_Financeiro_Cpf_Cliente]",
		nome: "Responsavel_Financeiro_Cpf",
		translationId: "responsavel-financeiro-cpf",
	},
	{
		tag: "[(20){}Responsavel_Financeiro_Rg_Cliente]",
		nome: "Responsavel_Financeiro_Rg",
		translationId: "responsavel-financeiro-rg",
	},
	{
		tag: "[(40){}Responsavel_Financeiro_Email_Cliente]",
		nome: "Responsavel_Financeiro_Email",
		translationId: "responsavel-financeiro-email",
	},
	{
		tag: "[(40){}ComplementoEnd_Cliente]",
		nome: "ComplementoEnd_Cliente",
		translationId: "complementoend-cliente",
	},
	{
		tag: "[(40){}Email_Cliente]",
		nome: "Email_Cliente",
		translationId: "email-cliente",
	},
	{
		tag: "[(50){}Grupo_Cliente]",
		nome: "Grupo_Cliente",
		translationId: "grupo-cliente",
	},
	{
		tag: "[(250){}AutorizacaoCobranca_Cliente]",
		nome: "AutorizacaoCobranca_Cliente",
		translationId: "autorizacaocobranca-cliente",
	},
	{
		tag: "[(250){}Observacao_Cliente]",
		nome: "Observacao_Cliente",
		translationId: "observacao-cliente",
	},
	{
		tag: "[(50){}Categoria_Cliente]",
		nome: "Categoria_Cliente",
		translationId: "categoria-cliente",
	},
	{
		tag: "[(50){}BairroEnd_Cliente]",
		nome: "BairroEnd_Cliente",
		translationId: "bairroend-cliente",
	},
	{
		tag: "[(50){}ConsultorAtual_Cliente]",
		nome: "ConsultorAtual_Cliente",
		translationId: "consultoratual-cliente",
	},
	{
		tag: "[(80){}Responsavel_Cliente_Cpf_Nome]",
		nome: "Responsavel_Cliente_Cpf_Nome",
		translationId: "responsavel-cliente-cpf-nome",
	},
	{
		tag: "[(20){}Habilitacao_Sesc_Cliente]",
		nome: "Habilitacao_Sesc_Cliente",
		translationId: "habilitacao-sesc-cliente",
	},
	{
		tag: "[(30){}Contato_Emergencia_Cliente]",
		nome: "Contato_Emergencia_Cliente",
		translationId: "contato-emergencia-cliente",
	},
	{
		tag: "[(20){}Telefone_Emergencia_Cliente]",
		nome: "Telefone_Emergencia_Cliente",
		translationId: "telefone-emergencia-cliente",
	},
	{
		tag: "[(50){}Bandeira_Cartao_Cliente]",
		nome: "Bandeira_Cartao_Cliente",
		translationId: "bandeira-cartao-cliente",
	},
	{
		tag: "[(50){}Nome_Titular_Cartao_Cliente]",
		nome: "Nome_Titular_Cartao_Cliente",
		translationId: "nome-titular-cartao-cliente",
	},
	{
		tag: "[(100){}Numero_Cartao_Cliente]",
		nome: "Numero_Cartao_Cliente",
		translationId: "numero-cartao-cliente",
	},
	{
		tag: "[(7){}aValidade_Cartao_Cliente]",
		nome: "aValidade_Cartao_Cliente",
		translationId: "avalidade-cartao-cliente",
	},
	{
		tag: "[(14){}CPF_Titular_Cartao_Cliente]",
		nome: "CPF_Titular_Cartao_Cliente",
		translationId: "cpf-titular-cartao-cliente",
	},
];

export const marcadoresContrato = [
	{
		tag: "[(10){}Codigo_Contrato]",
		nome: "Codigo_Contrato",
		translationId: "codigo-contrato",
	},
	{
		tag: "[(50){}Responsavel_Contrato]",
		nome: "Responsavel_Contrato",
		translationId: "responsavel-contrato",
	},
	{
		tag: "[(50){}ConsultorResponsavel_Contrato]",
		nome: "ConsultorResponsavel_Contrato",
		translationId: "consultorresponsavel-contrato",
	},
	{
		tag: "[(20){}VigenciaDe_Contrato]",
		nome: "VigenciaDe_Contrato",
		translationId: "vigenciade-contrato",
	},
	{
		tag: "[(20){}VigenciaAte_Contrato]",
		nome: "VigenciaAte_Contrato",
		translationId: "vigenciaate-contrato",
	},
	{
		tag: "[(20){}VigenciaAteAjustada_Contrato]",
		nome: "VigenciaAteAjustada_Contrato",
		translationId: "vigenciaateajustada-contrato",
	},
	{
		tag: "[(10){}Duracao_Contrato]",
		nome: "Duracao_Contrato",
		translationId: "duracao-contrato",
	},
	{
		tag: "[(20){}Horario_Contrato]",
		nome: "Horario_Contrato",
		translationId: "horario-contrato",
	},
	{
		tag: "[(15){}ValorBaseCalculo_Contrato]",
		nome: "ValorBaseCalculo_Contrato",
		translationId: "valorbasecalculo-contrato",
	},
	{
		tag: "[(15){}ValorFinal_Contrato]",
		nome: "ValorFinal_Contrato",
		translationId: "valorfinal-contrato",
	},
	{
		tag: "[(250){}Observacao_Contrato]",
		nome: "Observacao_Contrato",
		translationId: "observacao-contrato",
	},
	{
		tag: "[(10){}NomeModalidades_Contrato]",
		nome: "NomeModalidades_Contrato",
		translationId: "nomemodalidades-contrato",
	},
	{
		tag: "[(250){}NomeCompletoModalidades_Contrato]",
		nome: "NomeCompletoModalidades_Contrato",
		translationId: "nomecompletomodalidades-contrato",
	},
	{
		tag: "[(250){}NrVezesNomeCompletoModalidades_Contrato]",
		nome: "NrVezesNomeCompletoModalidades_Contrato",
		translationId: "nrvezesnomecompletomodalidades-contrato",
	},
	{
		tag: "[(10){}valorMensal_Contrato]",
		nome: "valorMensal_Contrato",
		translationId: "valormensal-contrato",
	},
	{
		tag: "[(10){}valorMensalDesconto_Contrato]",
		nome: "valorMensalDesconto_Contrato",
		translationId: "valormensaldesconto-contrato",
	},
	{
		tag: "[(10){}valorMensalAdequado_Contrato]",
		nome: "valorMensalAdequado_Contrato",
		translationId: "valormensaladequado-contrato",
	},
	{
		tag: "[(10){}ValorMensalBase_Contrato]",
		nome: "ValorMensalBase_Contrato",
		translationId: "valormensalbase-contrato",
	},
	{
		tag: "[(10){}valorDescontoExtra_Contrato]",
		nome: "valorDescontoExtra_Contrato",
		translationId: "valordescontoextra-contrato",
	},
	{
		tag: "[(10){}DtLancamento_Contrato]",
		nome: "DtLancamento_Contrato",
		translationId: "dtlancamento-contrato",
	},
	{
		tag: "[(80){}ValorPorExtenso_Contrato]",
		nome: "ValorPorExtenso_Contrato",
		translationId: "valorporextenso-contrato",
	},
	{
		tag: "[(50){}Convenio_Contrato]",
		nome: "Convenio_Contrato",
		translationId: "convenio-contrato",
	},
	{
		tag: "[(50){}ValorMatricula_Contrato]",
		nome: "ValorMatricula_Contrato",
		translationId: "valormatricula-contrato",
	},
	{
		tag: "[(50){}ValorRematricula_Contrato]",
		nome: "ValorRematricula_Contrato",
		translationId: "valorrematricula-contrato",
	},
	{
		tag: "[(50){}CondicaoPagamento_Contrato]",
		nome: "CondicaoPagamento_Contrato",
		translationId: "condicaopagamento-contrato",
	},
	{
		tag: "[(50){}NomeProduto_Contrato]",
		nome: "NomeProduto_Contrato",
		translationId: "nomeproduto-contrato",
	},
	{
		tag: "[(50){}QtdProduto_Contrato]",
		nome: "QtdProduto_Contrato",
		translationId: "qtdproduto-contrato",
	},
	{
		tag: "[(50){}ValorProduto_Contrato]",
		nome: "ValorProduto_Contrato",
		translationId: "valorproduto-contrato",
	},
	{
		tag: "[(50){}ValorDescontoProduto_Contrato]",
		nome: "ValorDescontoProduto_Contrato",
		translationId: "valordescontoproduto-contrato",
	},
	{
		tag: "[(50){}ValorUnitarioProduto_Contrato]",
		nome: "ValorUnitarioProduto_Contrato",
		translationId: "valorunitarioproduto-contrato",
	},
	{
		tag: "[(50){}TabelaProdutos_Contrato]",
		nome: "TabelaProdutos_Contrato",
		translationId: "tabelaprodutos-contrato",
	},
	{
		tag: "[(250){}ConvenioDescontoResumo_Contrato]",
		nome: "ConvenioDescontoResumo_Contrato",
		translationId: "conveniodescontoresumo-contrato",
	},
	{
		tag: "[(2){}diaVencimentoCartao_ContratoRecorrencia]",
		nome: "diaVencimentoCartao_ContratoRecorrencia",
		translationId: "diavencimentocartao-contratorecorrencia",
	},
	{
		tag: "[(10){}valorAnuidade_ContratoRecorrencia]",
		nome: "valorAnuidade_ContratoRecorrencia",
		translationId: "valoranuidade-contratorecorrencia",
	},
	{
		tag: "[(50){}valorTotalSemDesconto_Contrato]",
		nome: "valorTotalSemDesconto_Contrato",
		translationId: "valortotalsemdesconto-contrato",
	},
	{
		tag: "[(50){}valorTotalDescontoContrato_Contrato]",
		nome: "valorTotalDescontoContrato_Contrato",
		translationId: "valortotaldescontocontrato-contrato",
	},
	{
		tag: "[(50){}valorTotalContratoSemDescontoExtra_Contrato]",
		nome: "valorTotalContratoSemDescontoExtra_Contrato",
		translationId: "valortotalcontratosemdescontoextra-contrato",
	},
	{
		tag: "[(50){}valorParcelaMensal_Contrato]",
		nome: "valorParcelaMensal_Contrato",
		translationId: "valorparcelamensal-contrato",
	},
	{
		tag: "[(10){}quantidadeCreditoTreino_Contrato]",
		nome: "quantidadeCreditoTreino_Contrato",
		translationId: "quantidadecreditotreino-contrato",
	},
	{
		tag: "[(10){}totalDias_Contrato]",
		nome: "totalDias_Contrato",
		translationId: "totaldias-contrato",
	},
	{
		tag: "[(3){}DiasCarencia_Contrato]",
		nome: "DiasCarencia_Contrato",
		translationId: "diascarencia-contrato",
	},
	{
		tag: "[(250){}NomeModalidadesNrVezes_Contrato]",
		nome: "NomeModalidadesNrVezes_Contrato",
		translationId: "nomemodalidadesnrvezes-contrato",
	},
	{
		tag: "[(15){}ValorAdesao_Contrato]",
		nome: "ValorAdesao_Contrato",
		translationId: "valoradesao-contrato",
	},
	{
		tag: "[(15){}Saldo_Credito_Contrato]",
		nome: "Saldo_Credito_Contrato",
		translationId: "saldo-credito-contrato",
	},
	{
		tag: "[(50){}ValorCheioMatricula_Contrato]",
		nome: "ValorCheioMatricula_Contrato",
		translationId: "valorcheiomatricula-contrato",
	},
	{
		tag: "[(50){}ValorDescontoMatricula_Contrato]",
		nome: "ValorDescontoMatricula_Contrato",
		translationId: "valordescontomatricula-contrato",
	},
	{
		tag: "[(50){}ValorCheioRematricula_Contrato]",
		nome: "ValorCheioRematricula_Contrato",
		translationId: "valorcheiorematricula-contrato",
	},
	{
		tag: "[(50){}ValorDescontoRematricula_Contrato]",
		nome: "ValorDescontoRematricula_Contrato",
		translationId: "valordescontorematricula-contrato",
	},
	{
		tag: "[(10){}valorDescontoAnuidade_ContratoRecorrencia]",
		nome: "valorDescontoAnuidade_ContratoRecorrencia",
		translationId: "valordescontoanuidade-contratorecorrencia",
	},
	{
		tag: "[(10){}valorFinalAnuidade_ContratoRecorrencia]",
		nome: "valorFinalAnuidade_ContratoRecorrencia",
		translationId: "valorfinalanuidade-contratorecorrencia",
	},
	{
		tag: "[(1){}docRg]",
		nome: "docRg_Assinatura_Contrato",
		translationId: "docrg-assinatura-contrato",
	},
	{
		tag: "[(1){}endereco]",
		nome: "endereco_Assinatura_Contrato",
		translationId: "endereco-assinatura-contrato",
	},
	{
		tag: "[(1){}atestado]",
		nome: "atestado_Assinatura_Contrato",
		translationId: "atestado-assinatura-contrato",
	},
	{
		tag: "[(1){}anexo1]",
		nome: "anexo1_Assinatura_Contrato",
		translationId: "anexo1-assinatura-contrato",
	},
	{
		tag: "[(1){}anexo2]",
		nome: "anexo2_Assinatura_Contrato",
		translationId: "anexo2-assinatura-contrato",
	},
];

export const marcadoresPlano = [
	{
		tag: "[(10){}Codigo_Plano]",
		nome: "Codigo_Plano",
		translationId: "codigo-plano",
	},
	{
		tag: "[(200){}Descricao_Plano]",
		nome: "Descricao_Plano",
		translationId: "descricao-plano",
	},
	{
		tag: "[(20){}VigenciaDe_Plano]",
		nome: "VigenciaDe_Plano",
		translationId: "vigenciade-plano",
	},
	{
		tag: "[(20){}VigenciaAte_Plano]",
		nome: "VigenciaAte_Plano",
		translationId: "vigenciaate-plano",
	},
	{
		tag: "[(20){}IngressoAte_Plano]",
		nome: "IngressoAte_Plano",
		translationId: "ingressoate-plano",
	},
];

export const marcadoresModalidade = [
	{
		tag: "[(10){}Codigo_Modalidade]",
		nome: "Codigo_Modalidade",
		translationId: "codigo-modalidade",
	},
	{
		tag: "[(15){}ValorMensal_Modalidade]",
		nome: "ValorMensal_Modalidade",
		translationId: "valormensal-modalidade",
	},
	{
		tag: "[(10){}NrVezes_Modalidade]",
		nome: "NrVezes_Modalidade",
		translationId: "nrvezes-modalidade",
	},
	{
		tag: "[(50){}Nome_Modalidade]",
		nome: "Nome_Modalidade",
		translationId: "nome-modalidade",
	},
	{
		tag: "[(80){}NomeVezes_Modalidade]",
		nome: "NomeVezes_Modalidade",
		translationId: "nomevezes-modalidade",
	},
];

export const marcadoresTurma = [
	{
		tag: "[(10){}Codigo_Turma]",
		nome: "Codigo da Turma",
		translationId: "codigo-da-turma",
	},
	{
		tag: "[(200){}Descricao_Turma]",
		nome: "Horário da turma (Completo)",
		translationId: "horario-da-turma-completo",
	},
	{
		tag: "[(20){}Identificador_Turma]",
		nome: "Identificador da Turma",
		translationId: "identificador-da-turma",
	},
	{
		tag: "[(20){}DataInicioVigencia_Turma]",
		nome: "Data Inicial da Vigência da Turma",
		translationId: "data-inicial-da-vigencia-da-turma",
	},
	{
		tag: "[(20){}DataFinalVigencia_Turma]",
		nome: "Data Final da Vigência da Turma",
		translationId: "data-final-da-vigencia-da-turma",
	},
	{
		tag: "[(5){}IdadeMinima_Turma]",
		nome: "Idade Mínima da Turma",
		translationId: "idade-minima-da-turma",
	},
	{
		tag: "[(5){}IdadeMaxima_Turma]",
		nome: "Idade Máxima da Turma",
		translationId: "idade-maxima-da-turma",
	},
	{
		tag: "[(200){}DescricaoCurta_Turma]",
		nome: "Horário da turma (Resumida)",
		translationId: "horario-da-turma-resumida",
	},
];

export const marcadoresMovParcela = [
	{
		tag: "[(10){}Codigo_MovParcela]",
		nome: "Codigo_MovParcela",
		translationId: "codigo-movparcela",
	},
	{
		tag: "[(10){}Codigo_MovParcela_Sem_Renegociadas]",
		nome: "Codigo_MovParcela_Sem_Renegociadas",
		translationId: "codigo-movparcela-sem-renegociadas",
	},
	{
		tag: "[(20){}DataVencimento_MovParcela]",
		nome: "DataVencimento_MovParcela",
		translationId: "datavencimento-movparcela",
	},
	{
		tag: "[(20){}DataVencimento_MovParcela_Sem_Renegociadas]",
		nome: "DataVencimento_MovParcela_Sem_Renegociadas",
		translationId: "datavencimento-movparcela-sem-renegociadas",
	},
	{
		tag: "[(15){}ValorParcela_MovParcela]",
		nome: "ValorParcela_MovParcela",
		translationId: "valorparcela-movparcela",
	},
	{
		tag: "[(15){}ValorParcela_MovParcela_Sem_Renegociadas]",
		nome: "ValorParcela_MovParcela_Sem_Renegociadas",
		translationId: "valorparcela-movparcela-sem-renegociadas",
	},
	{
		tag: "[(15){}PercentualMulta_MovParcela]",
		nome: "PercentualMulta_MovParcela",
		translationId: "percentualmulta-movparcela",
	},
	{
		tag: "[(15){}PercentualJuro_MovParcela]",
		nome: "PercentualJuro_MovParcela",
		translationId: "percentualjuro-movparcela",
	},
	{
		tag: "[(60){}Descricao_MovParcela]",
		nome: "Descricao_MovParcela",
		translationId: "descricao-movparcela",
	},
	{
		tag: "[(60){}Descricao_MovParcela_Sem_Renegociadas]",
		nome: "Descricao_MovParcela_Sem_Renegociadas",
		translationId: "descricao-movparcela-sem-renegociadas",
	},
];

export const marcadoresPacote = [
	{
		tag: "[(10){}Codigo_Composicao]",
		nome: "Codigo_Composicao",
		translationId: "codigo-composicao",
	},
	{
		tag: "[(45){}Descricao_Composicao]",
		nome: "Descricao_Composicao",
		translationId: "descricao-composicao",
	},
	{
		tag: "[(15){}PrecoComposicao_Composicao]",
		nome: "PrecoComposicao_Composicao",
		translationId: "precocomposicao-composicao",
	},
];

export const marcadoresUsuario = [
	{
		tag: "[(10){}Codigo_Usuario]",
		nome: "Codigo_Usuario",
		translationId: "codigo-usuario",
	},
	{
		tag: "[(100){}Nome_Usuario]",
		nome: "Nome_Usuario",
		translationId: "nome-usuario",
	},
	{
		tag: "[(10){}Username_Usuario]",
		nome: "Username_Usuario",
		translationId: "username-usuario",
	},
	{
		tag: "[(60){}Usuario_Assinatura]",
		nome: "Usuario_Assinatura",
		translationId: "usuario-assinatura",
	},
];

export const marcadoresEmpresa = [
	{
		tag: "[(10){}Codigo_Empresa]",
		nome: "Codigo_Empresa",
		translationId: "codigo-empresa",
	},
	{
		tag: "[(40){}Assinatura_Empresa]",
		nome: "Assinatura_Empresa",
		translationId: "assinatura-empresa",
	},
	{
		tag: "[(14){}Fax_Empresa]",
		nome: "Fax_Empresa",
		translationId: "fax-empresa",
	},
	{
		tag: "[(50){}Site_Empresa]",
		nome: "Site_Empresa",
		translationId: "site-empresa",
	},
	{
		tag: "[(50){}Email_Empresa]",
		nome: "Email_Empresa",
		translationId: "email-empresa",
	},
	{
		tag: "[(14){}TelComercial1_Empresa]",
		nome: "TelComercial1_Empresa",
		translationId: "telcomercial1-empresa",
	},
	{
		tag: "[(14){}TelComercial2_Empresa]",
		nome: "TelComercial2_Empresa",
		translationId: "telcomercial2-empresa",
	},
	{
		tag: "[(14){}TelComercial3_Empresa]",
		nome: "TelComercial3_Empresa",
		translationId: "telcomercial3-empresa",
	},
	{
		tag: "[(20){}InscEstadual_Empresa]",
		nome: "InscEstadual_Empresa",
		translationId: "inscestadual-empresa",
	},
	{
		tag: "[(18){}Cnpj_Empresa]",
		nome: "Cnpj_Empresa",
		translationId: "cnpj-empresa",
	},
	{
		tag: "[(10){}Cep_Empresa]",
		nome: "Cep_Empresa",
		translationId: "cep-empresa",
	},
	{
		tag: "[(50){}Complemento_Empresa]",
		nome: "Complemento_Empresa",
		translationId: "complemento-empresa",
	},
	{
		tag: "[(5){}Numero_Empresa]",
		nome: "Numero_Empresa",
		translationId: "numero-empresa",
	},
	{
		tag: "[(20){}Setor_Empresa]",
		nome: "Setor_Empresa",
		translationId: "setor-empresa",
	},
	{
		tag: "[(50){}Endereco_Empresa]",
		nome: "Endereco_Empresa",
		translationId: "endereco-empresa",
	},
	{
		tag: "[(50){}RazaoSocial_Empresa]",
		nome: "RazaoSocial_Empresa",
		translationId: "razaosocial-empresa",
	},
	{
		tag: "[(50){}Nome_Empresa]",
		nome: "Nome_Empresa",
		translationId: "nome-empresa",
	},
	{
		tag: "[(50){}Cidade_Empresa]",
		nome: "Cidade_Empresa",
		translationId: "cidade-empresa",
	},
	{
		tag: "[(50){}Estado_Empresa]",
		nome: "Estado_Empresa",
		translationId: "estado-empresa",
	},
];

export const marcadoresVenda = [
	{
		tag: "[(20){}Data_Venda]",
		nome: "Data_Venda",
		translationId: "data-venda",
	},
	{
		tag: "[(15){}ValorTotal_Venda]",
		nome: "ValorTotal_Venda",
		translationId: "valortotal-venda",
	},
	{
		tag: "[(15){}ValorFinal_Venda]",
		nome: "ValorFinal_Venda",
		translationId: "valorfinal-venda",
	},
	{
		tag: "[(15){}ValorDesconto_Venda]",
		nome: "ValorDesconto_Venda",
		translationId: "valordesconto-venda",
	},
	{
		tag: "[(5){}Codigo_Venda]",
		nome: "Codigo_Venda",
		translationId: "codigo-venda",
	},
];

export const marcadoresItensVenda = [
	{
		tag: "[(5){}Codigo_Itens]",
		nome: "Codigo_Itens",
		translationId: "codigo-itens",
	},
	{
		tag: "[(150){}Descricao_Itens]",
		nome: "Descricao_Itens",
		translationId: "descricao-itens",
	},
	{
		tag: "[(20){}ValorUnitario_Itens]",
		nome: "ValorUnitario_Item_Venda",
		translationId: "valorunitario-item-venda",
	},
	{
		tag: "[(20){}ValorDesconto_Itens]",
		nome: "ValorDesconto_Item_Venda",
		translationId: "valordesconto-item-venda",
	},
	{
		tag: "[(20){}ValorFinal_Itens]",
		nome: "ValorFinal_Item_Venda",
		translationId: "valorfinal-item-venda",
	},
	{
		tag: "[(20){}Quantidade_Itens]",
		nome: "Quantidade_Itens",
		translationId: "quantidade-itens",
	},
];

export const marcadoresPacoteVenda = [
	{
		tag: "[(5){}Codigo_PacoteItem]",
		nome: "Codigo_PacoteItem",
		translationId: "codigo-pacoteitem",
	},
	{
		tag: "[(50){}Descricao_PacoteItem]",
		nome: "Descricao_PacoteItem",
		translationId: "descricao-pacoteitem",
	},
	{
		tag: "[(15){}Valor_PacoteItem]",
		nome: "Valor_PacoteItem",
		translationId: "valor-pacoteitem",
	},
];
