import { Injectable } from "@angular/core";
import * as CryptoJ<PERSON> from "crypto-js";
import { environment } from "../environments/environment";

export enum AlgoritmoCriptoEnum {
	ALGORITMO_TRIPLE_DES = "DESede",
	ALGORITMO_DES = "DES",
	ALGORITMO_BLOWFISH = "Blowfish",
	ALGORITMO_AES = "AES"
}

@Injectable({
	providedIn: "root",
})
export class CryptoService {
	private readonly cryptoKey: string;
	private algorithmConfigs = {
		[AlgoritmoCriptoEnum.ALGORITMO_TRIPLE_DES]: { cbc: "DESede/CBC/PKCS5Padding", tamanho: 24 },
		[AlgoritmoCriptoEnum.ALGORITMO_DES]: { cbc: "DES/ECB/PKCS5Padding", tamanho: 8 },
		[AlgoritmoCriptoEnum.ALGORITMO_BLOWFISH]: { cbc: "Blowfish/CBC/PKCS5Padding", tamanho: 16 },
		[AlgoritmoCriptoEnum.ALGORITMO_AES]: { cbc: "AES/CBC/PKCS5Padding", tamanho: 16 },
	};

	constructor() {
		this.cryptoKey = environment.cryptoKey;

		// Validar se a chave tem o tamanho correto (16 caracteres para AES-128)
		if (!this.cryptoKey) {
			throw new Error(`Chave de criptografia vazia!`);
		}
	}

	encrypt(data: string): string {
		return this.encryptChave(data, this.cryptoKey);
	}

	encryptChave(data: string, secret: string): string {
		const key = CryptoJS.enc.Utf8.parse(secret.padEnd(16, "\0").slice(0, 16));
		const encrypted = CryptoJS.AES.encrypt(data, key, {
			mode: CryptoJS.mode.ECB,
			padding: CryptoJS.pad.Pkcs7,
		});
		return encrypted.toString(); // base64
	}

	decrypt(data: string): string {
		return this.decryptChave(data, this.cryptoKey);
	}

	decryptChave(encryptedString: string, secret: string): string {
		const key = CryptoJS.enc.Utf8.parse(secret.padEnd(16, "\0").slice(0, 16));
		const decrypted = CryptoJS.AES.decrypt(encryptedString, key, {
			mode: CryptoJS.mode.ECB,
			padding: CryptoJS.pad.Pkcs7,
		});
		return decrypted.toString(CryptoJS.enc.Utf8);
	}
}
