import { Fatura } from "./fatura.model";

export const ultimasFaturas: Array<Fatura> = [
	{
		codigo: 311492,
		situacao: "Em Aberto",
		valor: 562.5,
		vencimento: "03/07/2019",
		urlBoleto: "",
		descricao:
			"CRED. METODO DE GESTAO/CRM/TREINO/FINANCEIRO/RECORRENCIA/REC FACIAL PARC. 4/4",
	},
	{
		codigo: 312909,
		situacao: "Em Aberto",
		valor: 100,
		vencimento: "03/07/2019",
		urlBoleto: "",
		descricao: "CONTRATO EMISSAO DE NOTA FISCAL REF. JUL/2019",
	},
	{
		codigo: 312921,
		situacao: "Em Aberto",
		valor: 650,
		vencimento: "03/07/2019",
		urlBoleto: "",
		descricao: "CONTRATO METODO DE GESTAO REF. JUL/2019",
	},
	{
		codigo: 312920,
		situacao: "Em Aberto",
		valor: 650,
		vencimento: "03/06/2019",
		urlBoleto: "",
		descricao: "CONTRATO METODO DE GESTAO REF. JUN/2019",
	},
	{
		codigo: 312908,
		situacao: "Em Aberto",
		valor: 100,
		vencimento: "03/06/2019",
		urlBoleto: "",
		descricao: "CONTRATO EMISSAO DE NOTA FISCAL REF. JUN/2019",
	},
	{
		codigo: 311491,
		situacao: "Em Aberto",
		valor: 562.5,
		vencimento: "03/06/2019",
		urlBoleto: "",
		descricao:
			"CRED. METODO DE GESTAO/CRM/TREINO/FINANCEIRO/RECORRENCIA/REC FACIAL PARC. 3/4",
	},
	{
		codigo: 312907,
		situacao: "Em Aberto",
		valor: 100,
		vencimento: "03/05/2019",
		urlBoleto: "",
		descricao: "CONTRATO EMISSAO DE NOTA FISCAL REF. MAI/2019",
	},
	{
		codigo: 311490,
		situacao: "Em Aberto",
		valor: 562.5,
		vencimento: "03/05/2019",
		urlBoleto: "",
		descricao:
			"CRED. METODO DE GESTAO/CRM/TREINO/FINANCEIRO/RECORRENCIA/REC FACIAL PARC. 2/4",
	},
	{
		codigo: 312919,
		situacao: "Em Aberto",
		valor: 650,
		vencimento: "03/05/2019",
		urlBoleto: "",
		descricao: "CONTRATO METODO DE GESTAO REF. MAI/2019",
	},
	{
		codigo: 311489,
		situacao: "Em Aberto",
		valor: 562.5,
		vencimento: "25/04/2019",
		urlBoleto: "",
		descricao:
			"CRED. METODO DE GESTAO/CRM/TREINO/FINANCEIRO/RECORRENCIA/REC FACIAL PARC. 1/4",
	},
	{
		codigo: 311493,
		situacao: "Em Aberto",
		valor: 450,
		vencimento: "25/04/2019",
		urlBoleto: "",
		descricao: "CONTRATO ANUAL APP CROSS/ TREINO REF PARC. 1/1",
	},
	{
		codigo: 312906,
		situacao: "Em Aberto",
		valor: 36.63,
		vencimento: "25/04/2019",
		urlBoleto: "",
		descricao:
			"CONTRATO EMISSAO DE NOTA FISCAL REF. ABR/2019 DO DIA 19/04 A 30/04.",
	},
];
