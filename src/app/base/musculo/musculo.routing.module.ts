import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { MusculoListaComponent } from "./components/musculo-lista/musculo-lista.component";
import { MusculoEditComponent } from "./components/musculo-edit/musculo-edit.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const recurso = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.MUSCULOS, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: MusculoListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: MusculoEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "adicionar",
		component: MusculoEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class MusculoRoutingModule {}
