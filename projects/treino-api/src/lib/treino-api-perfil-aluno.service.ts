import { Injectable } from "@angular/core";
import { ApiResponseSingle } from "./base.model";
import { TreinoApiModule } from "./treino-api.module";

import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { TreinoApiBaseService } from "./treino-api-base.service";
import { EvolucaoFisica } from "./perfil-aluno-avaliacao.model";

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiPerfilAlunoService {
	constructor(private restService: TreinoApiBaseService) {}

	evolucaoFisica(matricula?: string): Observable<any> {
		return this.restService
			.get(`avaliacoes-fisica/evolucao-fisica/${matricula}`)
			.pipe(
				map((response: ApiResponseSingle<EvolucaoFisica>) => {
					return response.content;
				}),
				catchError((err) => {
					if (err.status === 404) {
						return new Observable((o) => {
							o.next("error");
							o.complete();
						});
					}
				})
			);
	}
}
