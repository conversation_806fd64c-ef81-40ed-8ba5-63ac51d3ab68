import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { Plano, TipoPlano } from "../../../plano.model";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { PlanoStateService } from "../plano-state.service";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PlanoAdvancedConfigComponent } from "../../plano-advanced-config/plano-advanced-config.component";
import { PlanoApiProdutoService } from "plano-api";

@Component({
	selector: "adm-plano-dados-basicos",
	templateUrl: "./plano-dados-basicos.component.html",
	styleUrls: ["./plano-dados-basicos.component.scss"],
})
export class PlanoDadosBasicosComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Input() planoDTO: Plano;
	@Input() tableDuracaoValor: PactoDataGridConfig = new PactoDataGridConfig({});
	tiposPlano: any[];
	produtosContrato: Array<any> = new Array<any>();

	formGroup: FormGroup = new FormGroup(
		{
			descricao: new FormControl(null, [Validators.required]),
			tipoPlano: new FormControl(),
			vigenciaDe: new FormControl(new Date(), [Validators.required]),
			ingressoAte: new FormControl(new Date(), [Validators.required]),
			vigenciaAte: new FormControl(new Date(), [Validators.required]),
			produtoContrato: new FormControl(),
			gerarParcelasValorDiferente: new FormControl(),
			restringeVendaPorCategoria: new FormControl(),
		},
		{
			updateOn: "blur",
		}
	);

	@Output() nome: EventEmitter<string> = new EventEmitter();

	produtoelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	constructor(
		private planoStateService: PlanoStateService,
		private dialogService: DialogService,
		public produtoService: PlanoApiProdutoService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initTiposPlano();
		this.loadForm();
		this.loadProdutosContrato();
		this.formGroup.valueChanges.subscribe((value) => {
			if (this.planoStateService.plano) {
				this.planoDTO = this.planoStateService.plano;
			}
			if (this.planoDTO.tipoPlano !== value.tipoPlano) {
				this.planoStateService.plano = null;
			}
			this.planoDTO.descricao = value.descricao;
			this.planoDTO.tipoPlano = value.tipoPlano;
			this.planoDTO.vigenciaDe = value.vigenciaDe;
			this.planoDTO.ingressoAte = value.ingressoAte;
			this.planoDTO.vigenciaAte = value.vigenciaAte;
			this.planoDTO.restringeVendaPorCategoria =
				value.restringeVendaPorCategoria;
			this.planoDTO.produtoContrato = value.produtoContrato;
			this.planoDTO.planoRecorrencia = {
				gerarParcelasValorDiferente: value.gerarParcelasValorDiferente,
			};
			this.planoStateService.plano = this.planoDTO;
		});
		if (
			!this.planoDTO ||
			(this.planoDTO &&
				(!this.planoDTO.vigenciaDe ||
					!this.planoDTO.vigenciaAte ||
					!this.planoDTO.ingressoAte))
		) {
			const date = new Date();
			date.setFullYear(date.getFullYear() + 1);
			this.formGroup.get("vigenciaDe").setValue(new Date().getTime());
			this.formGroup.get("ingressoAte").setValue(date.getTime());
			this.formGroup.get("vigenciaAte").setValue(date.getTime());
		}
		this.formGroup.controls["descricao"].valueChanges.subscribe((v) => {
			this.nome.emit(v);
		});
	}

	loadProdutosContrato() {
		this.produtoService.getProdutosTipoPlano().subscribe((response: any) => {
			this.produtosContrato = response.content;
			if (
				(this.planoStateService.plano &&
					this.planoStateService.plano.codigo === undefined) ||
				this.planoStateService.plano.codigo === null
			) {
				this.formGroup
					.get("produtoContrato")
					.setValue(
						this.produtosContrato.find((prod) => prod.tipoProduto === "PM")
					);
			}
		});
	}

	onChangeTabCategoria() {
		this.formGroup.valueChanges.subscribe((value) => {
			this.cd.detectChanges();
		});
	}

	configuracoesPlano() {
		const dialogRef = this.dialogService.open(
			this.traducoes.getLabel("advanced-config"),
			PlanoAdvancedConfigComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoStateService.plano;
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {
					this.planoStateService.plano = value;
				})
				.catch((error) => {
					console.log(error);
				});
		}
	}

	private loadForm() {
		if (this.planoDTO) {
			this.formGroup.patchValue({
				descricao: this.planoDTO.descricao,
				tipoPlano: this.planoDTO.tipoPlano,
				vigenciaDe: this.planoDTO.vigenciaDe,
				ingressoAte: this.planoDTO.ingressoAte,
				vigenciaAte: this.planoDTO.vigenciaAte,
				produtoContrato: this.planoDTO.produtoContrato,
				gerarParcelasValorDiferente: this.planoDTO.planoRecorrencia
					? this.planoDTO.planoRecorrencia.gerarParcelasValorDiferente
					: null,
				restringeVendaPorCategoria: this.planoDTO.restringeVendaPorCategoria,
			});
		}
	}

	private initTiposPlano() {
		setTimeout(() => {
			const tipos = [];
			tipos.push({
				label: this.traducoes.getLabel("PN"),
				id: TipoPlano.PLANO_NORMAL,
			});
			tipos.push({
				label: this.traducoes.getLabel("PR"),
				id: TipoPlano.PLANO_RECORRENCIA,
			});
			tipos.push({
				label: this.traducoes.getLabel("PC"),
				id: TipoPlano.PLANO_CREDITO,
			});
			tipos.push({
				label: this.traducoes.getLabel("PP"),
				id: TipoPlano.PLANO_PERSONAL,
			});
			tipos.push({
				label: this.traducoes.getLabel("PA"),
				id: TipoPlano.PLANO_AVANCADO,
			});
			this.tiposPlano = tipos;
			this.cd.detectChanges();
		});
	}
}
