import {
	AfterViewInit,
	Component,
	ContentChild,
	ContentChildren,
	ElementRef,
	HostBinding,
	Input,
	OnInit,
	QueryList,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { CatInputDirective } from "./directives/cat-input.directive";
import { CatPrefixDirective } from "./directives/cat-prefix.directive";
import { CatSuffixDirective } from "./directives/cat-suffix.directive";
import {
	MAT_RIPPLE_GLOBAL_OPTIONS,
	RippleGlobalOptions,
} from "@angular/material";
import { FormControl } from "@angular/forms";

let uniqueId = 0;

const globalRippleConfig: RippleGlobalOptions = {
	disabled: true,
	animation: {
		enterDuration: 0,
		exitDuration: 0,
	},
};

@Component({
	selector: "login-cat-form-field",
	templateUrl: "./cat-form-field.component.html",
	styleUrls: ["./cat-form-field.component.scss"],
	encapsulation: ViewEncapsulation.None,
	providers: [
		{ provide: MAT_RIPPLE_GLOBAL_OPTIONS, useValue: globalRippleConfig },
	],
})
export class CatFormFieldComponent implements OnInit, AfterViewInit {
	@ContentChild(CatInputDirective, { static: false })
	catInputDirective: CatInputDirective;
	@ContentChildren(CatPrefixDirective, { descendants: true })
	prefixes: QueryList<CatPrefixDirective>;
	@ContentChildren(CatSuffixDirective, { descendants: true })
	suffixes: QueryList<CatSuffixDirective>;
	@ViewChild("labelElement", { static: false }) labelElement;
	@ViewChild("formFieldInputElement", { static: false }) formFieldInputElement;
	@Input() id: string;
	@Input() requiredSymbol = "*";
	@Input() appearence: "outlined" | "underlined" = "outlined";

	get disabled(): boolean {
		if (this.catInputDirective) {
			if (this.catInputDirective.control) {
				return this.catInputDirective.control.disabled;
			} else if (
				this.catInputDirective.element.nativeElement.hasOwnProperty("disabled")
			) {
				return this.catInputDirective.element.nativeElement.hasOwnProperty(
					"disabled"
				);
			}
		}
	}

	constructor(private elementRef: ElementRef) {}

	ngOnInit() {}

	ngAfterViewInit() {
		const label =
			this.elementRef.nativeElement.getElementsByTagName("label")[0];
		if (this.catInputDirective) {
			const id = this.catInputDirective.id;
			this.catInputDirective.element.nativeElement.id = id
				? id
				: this.id
				? this.id
				: `cat-input-${uniqueId++}`;
			if (label) {
				label.id = id
					? `cat-input-label-${id}`
					: this.id
					? `cat-input-label-${this.id}`
					: `cat-input-label-${uniqueId++}`;
				label.setAttribute(
					"for",
					this.catInputDirective.element.nativeElement.id
				);
				if (
					this.catInputDirective.element.nativeElement.hasAttribute(
						"required"
					) ||
					this.hasRequiredValidator(this.catInputDirective)
				) {
					label.textContent += ` ${this.requiredSymbol}`;
				}
			}

			if (
				this.catInputDirective.element.nativeElement.hasAttribute("autofocus")
			) {
				this.catInputDirective.element.nativeElement.focus();
			}

			if (
				this.catInputDirective.element.nativeElement.hasAttribute("disabled")
			) {
				this.catInputDirective.control.control.disable();
			}
		}

		if (this.formFieldInputElement) {
			if (this.appearence === "underlined") {
				this.formFieldInputElement.nativeElement.classList.add("underlined");
			}
		}
	}

	hasRequiredValidator(directive) {
		if (directive.control) {
			const { validator } = directive.control.control;
			if (validator) {
				const validation = validator(new FormControl());
				return validation !== null && validation.required;
			}
		}
	}
}
