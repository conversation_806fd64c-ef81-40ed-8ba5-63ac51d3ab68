@import "src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/typography/mixins";

.lista-alunos {
	margin: 16px;
	border-radius: 5px;
	border: 1px solid #c9cbcf;
	padding: 16px;
	margin-bottom: 32px;

	::ng-deep .table-content {
		padding: 0;
	}

	.acoes {
		font-size: 18px;
		cursor: pointer;

		.pct {
			padding: 0px 5px;
		}

		.confirmar {
			color: $azulimPri;
		}

		.desconfirmar {
			color: $gelo03;
		}

		.remover {
			color: $hellboyPri;
		}
	}

	.titulo {
		display: block;
		color: #43474b;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: 125%;
		letter-spacing: 0.25px;
		margin-bottom: 32px;
	}

	.situacao {
		display: flex;

		.item {
			display: block;
			margin: 5px;
		}
	}

	::ng-deep .ds3-form-field {
		--form-field-content-spacing-vertical: 2px;
		--form-field-content-spacing-horizontal: 0.25rem;
		--form-field-border-radius: 0.25rem;
		display: inline-block !important;
	}

	::ng-deep .ds3-number-field {
		display: grid !important;
		grid-template-columns: 16px 1fr 16px !important;
		width: 40px !important;
		gap: 0.1rem !important;
		align-items: center !important;
	}

	::ng-deep .ds3-number-field .number-input {
		width: 100% !important;
		text-align: center !important;
		padding: 2px !important;
		font-size: 0.75rem !important;

		&::-webkit-outer-spin-button,
		&::-webkit-inner-spin-button {
			-webkit-appearance: none !important;
		}

		&[type="number"] {
			-moz-appearance: textfield !important;
		}
	}

	::ng-deep .ds3-icon-button {
		padding: 0 !important;
		width: 16px !important;
		height: 16px !important;
		font-size: 0.75rem !important;
	}

	::ng-deep .ds3-button.ds3-icon-button {
		min-width: 16px !important;
		min-height: 16px !important;
	}

	.capitalize {
		text-transform: capitalize;
	}

	.transform-none {
		text-transform: none;
	}

	.empty-turma {
		display: grid;
		place-items: center;
		gap: 16px;
		margin: 32px;
		text-align: center;
		font-size: 16px;
		font-weight: 400;
		line-height: 18px;

		span {
			width: 450px;
		}

		.titulo {
			font-size: 16px;
			font-weight: 600;
			line-height: 18px;
			letter-spacing: 0.25px;
		}
	}
}

.line-text-title-fila {
	display: inline-flex;
	width: 100%;

	.column1 {
		text-align: left;
		width: 50%;
	}

	.column2 {
		text-align: right;
		width: 50%;
	}

	.custom-buttom {
		::ng-deep button {
			all: unset;
			font-family: Poppins;
			color: $azulim08;
			font-weight: 600;
			cursor: pointer;
			padding: 10px 8px 10px 8px;
			border-radius: 4px;
			gap: 8px;
			font-size: 12px;

			&:hover {
				background-color: $azulim09;
			}
		}
	}
}

::ng-deep .modal-reserva-equip .modal-dialog {
	max-width: 100% !important;
}

.link-cursor-observacoes {
	cursor: pointer;
	color: $actionDefaultAble04 !important;
	@extend .pct-overline2-bold;
	text-decoration: none !important;
	display: inline-block;
}

.link-cursor-observacoes:hover {
	text-decoration: underline;
}
