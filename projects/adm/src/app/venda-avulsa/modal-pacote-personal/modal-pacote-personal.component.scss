table {
	width: calc(100% - 32px);
	margin: 16px;

	th:first-child {
		text-align: left;
	}

	th {
		border-bottom: 1px solid #d7d8db;
		color: var(--type-default-title, #55585e);
		font-size: 12px;
		font-style: normal;
		font-weight: 700;
		line-height: 125%;
		letter-spacing: 0.25px;
		text-align: center;
		padding: 12px;
	}

	td {
		cursor: pointer;
		padding: 16px;
		color: #797d86;
		font-size: 12px;
		line-height: 16px;
		text-transform: capitalize;
		text-align: center;

		div {
			span {
				margin-left: 10px;
			}

			display: flex;
			align-items: center;
		}
	}

	.zebra-row {
		background-color: #fafafa;
	}

	.coluna-link {
		color: #1e60fa;
		cursor: pointer;
		font-weight: 600;
	}
}
