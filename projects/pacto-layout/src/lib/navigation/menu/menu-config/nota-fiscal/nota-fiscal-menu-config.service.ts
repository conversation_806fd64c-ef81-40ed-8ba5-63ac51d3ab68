import { Injectable } from "@angular/core";
import { MenuConfigService } from "../menu-config.service";
import { Observable, zip } from "rxjs";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../models";
import { LayoutNavigationService } from "../../../layout-navigation.service";
import { map } from "rxjs/operators";
import { NotaFiscalCadastrosService } from "./cadastros/nota-fiscal-cadastros.service";
import { NotaFiscalRelatoriosService } from "./relatorios/nota-fiscal-relatorios.service";
import { NotaFiscalOperacoesService } from "./operacoes/nota-fiscal-operacoes.service";

@Injectable({
	providedIn: "root",
})
export class NotaFiscalMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private notaFiscalCadastrosService: NotaFiscalCadastrosService,
		private notaFiscalRelatoriosService: NotaFiscalRelatoriosService,
		private notaFiscalOperacoesService: NotaFiscalOperacoesService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return zip(
			this.notaFiscalOperacoesService.menus,
			this.notaFiscalRelatoriosService.menus,
			this.notaFiscalCadastrosService.menus
		).pipe(
			map((results) => {
				const newArray: Array<PlatformMenuItem> = [];
				results.forEach((item) => newArray.push(...item));
				this.setMenusModule(PlataformModuleConfig.NOTA_FISCAL, newArray);
				this.setExternalUrls(newArray);
				this.setMenuInativos(newArray);
				return this.filterMenusInativos(newArray);
			})
		);
	}
}
