import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Api, ClientDiscoveryService, SessionService } from "sdk";
import { AdmRestService } from "../../../adm-rest.service";
import { Router } from "@angular/router";
import { DataFiltro } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalRef,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RelatorioGympassDetalheComponent } from "../relatorio-gympass-detalhe/relatorio-gympass-detalhe.component";

@Component({
	selector: "adm-relatorio-gympass",
	templateUrl: "./relatorio-gympass.component.html",
	styleUrls: ["./relatorio-gympass.component.scss"],
})
export class RelatorioGympassComponent implements OnInit, AfterViewInit {
	@ViewChild("tableGympassComponent", { static: false })
	tableGympassComponent: RelatorioComponent;
	@ViewChild("columnMatricula", { static: true })
	columnMatricula: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnDataCadastro", { static: true })
	columnDataCadastro: TemplateRef<any>;
	@ViewChild("columnToken", { static: true }) columnToken: TemplateRef<any>;
	@ViewChild("columnTipoToken", { static: true })
	columnTipoToken: TemplateRef<any>;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;

	tableGympass: PactoDataGridConfig;
	modalDetalhamentoRef: PactoModalRef;
	formGroup = new FormGroup({
		periodoPesquisaInicial: new FormControl(),
		periodoPesquisaFinal: new FormControl(),
	});
	mostarTabelaGympass: boolean;
	filters: any;
	baseFilter: DataFiltro = {};
	inicioMesAtual = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
	fimMesAtual = new Date(
		new Date().getFullYear(),
		new Date().getMonth() + 1,
		0
	);
	showLoader = false;

	constructor(
		private router: Router,
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private clientDiscoveryService: ClientDiscoveryService,
		private modalService: DialogService
	) {}

	ngOnInit() {
		this.formGroup.get("periodoPesquisaInicial").setValue(this.inicioMesAtual);
		this.formGroup.get("periodoPesquisaFinal").setValue(this.fimMesAtual);
	}

	ngAfterViewInit() {
		this.createTableGympass();
		this.cd.detectChanges();
	}

	voltarAdm() {
		this.router.navigate(["adm"]);
	}

	cleanFilters() {
		this.formGroup.get("periodoPesquisaInicial").setValue(undefined);
		this.formGroup.get("periodoPesquisaFinal").setValue(undefined);
	}

	consult() {
		if (
			this.sessionService.perfilUsuario.funcionalidades.get(
				"permitevisualizagympassperiodo"
			)
		) {
			this.mostarTabelaGympass = true;
			const dataInicial = new Date(
				this.formGroup.get("periodoPesquisaInicial").value
			);
			const dataFinal = new Date(
				this.formGroup.get("periodoPesquisaFinal").value
			);
			this.filters = {
				empresa: parseInt(this.sessionService.empresaId, 10),
				periodoPesquisaInicial: dataInicial.getTime(),
				periodoPesquisaFinal: dataFinal.getTime(),
			};
			this.baseFilter.filters = this.filters;
			setTimeout(() => {
				this.tableGympassComponent.reloadData();
				this.scrollToComponent("tableGympassComponent");
			});
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("usuario-sem-permissao")
			);
		}
	}

	scrollToComponent(idComponent) {
		const el = document.getElementById(idComponent);
		el.scrollIntoView();
	}

	private createTableGympass() {
		this.tableGympass = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrl(
				"/relatorio-gympass",
				false,
				Api.MSRELATORIO
			),
			quickSearch: false,
			ghostLoad: false,
			ghostAmount: 5,
			showFilters: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataRegistro",
					titulo: this.columnDataCadastro,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "token",
					titulo: this.columnToken,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "tipoToken",
					titulo: this.columnTipoToken,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataNasc",
					titulo: "dataNasc",
					visible: false,
					ordenavel: false,
					exportNoVisible: false,
				},
				{
					nome: "email",
					titulo: "email",
					visible: false,
					ordenavel: false,
					exportNoVisible: false,
				},
				{
					nome: "telefone",
					titulo: "telefone",
					visible: false,
					ordenavel: false,
					exportNoVisible: false,
				},
				{
					nome: "empresa",
					titulo: "empresa",
					visible: false,
					ordenavel: false,
					exportNoVisible: false,
				},
			],
			actions: [
				{
					nome: "action-detalhamento",
					iconClass: "pct pct-zoom-in cor-azulim05",
					tooltipText: "",
				},
			],
		});
	}

	openModalDetalhes(row) {
		this.modalDetalhamentoRef = this.modalService.open(
			"Detalhamento do Gympass",
			RelatorioGympassDetalheComponent,
			PactoModalSize.LARGE
		);
		this.modalDetalhamentoRef.componentInstance.relatorioGymPass = row;
	}

	iconClick(event) {
		if (event.iconName === "action-detalhamento") {
			event.row.rowIndex = event.rowIndex;
			this.openModalDetalhes(event.row);
		}
	}
}
