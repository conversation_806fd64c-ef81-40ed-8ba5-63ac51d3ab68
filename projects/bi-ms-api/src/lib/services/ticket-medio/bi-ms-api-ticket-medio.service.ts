import { Injectable } from "@angular/core";
import { BiMsApiBaseService } from "../../base/bi-ms-api-base.service";
import { BiMsApiModule } from "../../bi-ms-api.module";
import { FiltroBi } from "../../models/filtro-bi.model";
import { map } from "rxjs/operators";
import { throwError } from "rxjs";
import { FiltroTicketMedio } from "../../models/ticket-medio/filtro-ticket-medio.model";
import { TicketMedioResponseModel } from "../../models/ticket-medio/ticket-medio-response.model";
import { ApiResponseSingle } from "../../base/base.model";

@Injectable({
	providedIn: BiMsApiModule,
})
export class BiMsApiTicketMedioService {
	constructor(private biMsApiBaseService: BiMsApiBaseService) {}

	list(data: { filtroTicketMedio: FiltroTicketMedio; reloadFull?: boolean }) {
		let reloadFull = "false";
		if (data.reloadFull) {
			reloadFull = data.reloadFull.toString();
		}
		return this.biMsApiBaseService
			.post<ApiResponseSingle<FiltroBi>>(
				"v2-ticket-medio",
				data.filtroTicketMedio,
				{
					params: {
						reloadFull,
					},
				}
			)
			.pipe(
				map((response) => {
					try {
						return JSON.parse(
							response.content.jsonDados
						) as TicketMedioResponseModel;
					} catch (e) {
						console.error(
							`Não foi possível realizar a conversão dos dados: ${e}`
						);
						throwError(`Não foi possível realizar a conversão dos dados: ${e}`);
					}
				})
			);
	}
}
