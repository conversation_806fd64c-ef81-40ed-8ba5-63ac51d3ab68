  discovery-ms[m
  feature/PI-112[m
  feature/PI-113[m
  feature/PI-115_removendoDash2[m
  feature/PI-118[m
  feature/PI-162[m
  feature/PI-176[m
  feature/PI-176-Continuacao[m
  feature/PI-185[m
  feature/PI-188[m
  feature/PI-189[m
  feature/PI-191[m
  feature/PI-195[m
  feature/PI-204[m
  feature/PI-223[m
  feature/plano_de_sucesso_individual[m
  hotfix/PI-0_Ajuste_scss[m
  hotfix/PI-126[m
  hotfix/PI-176_Corrigindo_Acoes[m
  hotfix/PI-176_Link_URL[m
  hotfix/PI-214[m
  hotfix/PI-218[m
  hotfix/PI-219[m
  hotfix/PI_0_Icone_NFe[m
  hotfix/ajuste-acoes-plano-sucesso[m
  hotfix/data-ptbr-canal-cliente[m
* [32mmaster[m
  [31mremotes/origin/#269999TagVersion[m
  [31mremotes/origin/#510-treino-cadastro-grupo-muscular[m
  [31mremotes/origin/#518-avaliacao-anamnese[m
  [31mremotes/origin/#53-cadastro-rapido-modalidade[m
  [31mremotes/origin/#567-configuracao-de-perfis[m
  [31mremotes/origin/#567-configuracao-perfis-cadstros-auxiliares-correcao-01[m
  [31mremotes/origin/#568-configuracao-perfil-correcao[m
  [31mremotes/origin/#568-configuracao-perfil-correcao-01[m
  [31mremotes/origin/#568-configuracao-perfil-crossfit[m
  [31mremotes/origin/#571-bi-treino[m
  [31mremotes/origin/#581-gestao-carteira-correcao[m
  [31mremotes/origin/#583-indicadores-agenda[m
  [31mremotes/origin/#588-substituto-correcao[m
  [31mremotes/origin/#592-ranking-professores-correcao-01[m
  [31mremotes/origin/#607-ajuste-tela-alunos[m
  [31mremotes/origin/#666-bitreino-detalhe-indicadores[m
  [31mremotes/origin/#671-avaliacao-fisica[m
  [31mremotes/origin/#671-avaliacaoes-aluno-01[m
  [31mremotes/origin/#741_anamnes_erro_500[m
  [31mremotes/origin/#744_avaliacao_fisica[m
  [31mremotes/origin/#746_validacao_data_sexo[m
  [31mremotes/origin/#748_avaliacao_somatotipia[m
  [31mremotes/origin/#751_evolucao_fisica[m
  [31mremotes/origin/#753_evolucao_dobras[m
  [31mremotes/origin/#801_avaliacao_fisica[m
  [31mremotes/origin/#801_avaliacao_fisica_03[m
  [31mremotes/origin/HEAD[m -> origin/master
  [31mremotes/origin/I9-492-migrar-ui-kit-plataforma[m
  [31mremotes/origin/bugfix/#371-zw-aula-cheia-dependente[m
  [31mremotes/origin/bugfix/#456-zw-cadastro-usuario[m
  [31mremotes/origin/bugfix/#798-av-fisica-usar-pressao-arterial-sistolica-diastolica[m
  [31mremotes/origin/bugfix/#882-agenda-servico-agendamento-nao-esta-sobrepondo-disponibilidade[m
  [31mremotes/origin/bugfix/#885-menu-global-nao-estou-conseguindo-pesquisar-ranking-dos-professores[m
  [31mremotes/origin/bugfix/#927-alterar-nome-identificacao-sistema-pacto[m
  [31mremotes/origin/bugfix/traducao-dos-arquivos[m
  [31mremotes/origin/colocando-empresaId-no-header-da-requisicao[m
  [31mremotes/origin/develop[m
  [31mremotes/origin/e2e-bi-treino[m
  [31mremotes/origin/e2e-programa-de-treino-tela-de-montagem[m
  [31mremotes/origin/e2e_teste_primeira_x[m
  [31mremotes/origin/feature/#19-envio-de-avaliacao[m
  [31mremotes/origin/feature/#23451-criar-api-de-trial-para-novo-treino[m
  [31mremotes/origin/feature/#23786[m
  [31mremotes/origin/feature/#24965[m
  [31mremotes/origin/feature/#360-test-automatizado-login-com-sem-acesso-ao-sistema[m
  [31mremotes/origin/feature/#371-zw-aula[m
  [31mremotes/origin/feature/#374-resultado-unico-na-busca-global[m
  [31mremotes/origin/feature/#471-zw-configuracoes-marcacao-aulas[m
  [31mremotes/origin/feature/#473-zw-aula-cheia-independente[m
  [31mremotes/origin/feature/#476-zw-perfil-do-aluno[m
  [31mremotes/origin/feature/#*******************************************de-benchmark[m
  [31mremotes/origin/feature/#492-correcao-put-alunos-id[m
  [31mremotes/origin/feature/#497-criar-ids-cadastro-do-aluno-treino-independente[m
  [31mremotes/origin/feature/#499-tr-independente-nao-esta-mostrando-mensagem-erro[m
  [31mremotes/origin/feature/#502-teste-automatizado[m
  [31mremotes/origin/feature/#509-teste-automatizado[m
  [31mremotes/origin/feature/#513-e2e-criar-ids-para-teste-automatizado[m
  [31mremotes/origin/feature/#514-perfil-do-aluno-treinos[m
  [31mremotes/origin/feature/#521-criar-ids-para-testes-automatizados[m
  [31mremotes/origin/feature/#528-criar-ids-para-o-tipo-agendamento[m
  [31mremotes/origin/feature/#529-avaliacoes-postura-upload-de-imagem[m
  [31mremotes/origin/feature/#530-avaliacoes-par-q-resultado[m
  [31mremotes/origin/feature/#537-traduzir-perfil-do-aluno[m
  [31mremotes/origin/feature/#53_cadastro_rapido[m
  [31mremotes/origin/feature/#540-e2e-cadastro-de-aula-coletiva[m
  [31mremotes/origin/feature/#541-e2e-cadastro-de-anamnese[m
  [31mremotes/origin/feature/#542-e2e-cadastro-de-aparelho-cross-fit[m
  [31mremotes/origin/feature/#543-independente-erro-ao-cadastrar-wod[m
  [31mremotes/origin/feature/#545-e2e-cadastro-de-atividade-crossfit[m
  [31mremotes/origin/feature/#546-atualizar-listagem-wod[m
  [31mremotes/origin/feature/#547-atualizar-listagem-tipo-wod[m
  [31mremotes/origin/feature/#553-e2e-cadastro-benchmark[m
  [31mremotes/origin/feature/#561-zw-links-de-integracao[m
  [31mremotes/origin/feature/#564-configuracao-de-perfis-agenda[m
  [31mremotes/origin/feature/#566-configuracao-de-perfis-aula-cheia[m
  [31mremotes/origin/feature/#569-configuracao-de-perfis-aba-aluno[m
  [31mremotes/origin/feature/#571-atualizacao-dashboard-bi[m
  [31mremotes/origin/feature/#571-refresh-indicadores-bi[m
  [31mremotes/origin/feature/#574-comportamento-configuracoes-gerais[m
  [31mremotes/origin/feature/#574-correcao[m
  [31mremotes/origin/feature/#574-correcao-wod-edit[m
  [31mremotes/origin/feature/#574Configurações-Globais[m
  [31mremotes/origin/feature/#575-componente-configuracoes-gestao[m
  [31mremotes/origin/feature/#575-remoção-do-arquivo-spec.ts[m
  [31mremotes/origin/feature/#576-avaliacao-modificacoes[m
  [31mremotes/origin/feature/#576-avalicao-produtos-dobras[m
  [31mremotes/origin/feature/#576-configuracoes-avaliacao-fisica[m
  [31mremotes/origin/feature/#576-mod-componente-avaliacao[m
  [31mremotes/origin/feature/#576-modificacao-label-notificacao[m
  [31mremotes/origin/feature/#577-conflito-git[m
  [31mremotes/origin/feature/#577-correcao-conflito[m
  [31mremotes/origin/feature/#577-modificacoes-config-app[m
  [31mremotes/origin/feature/#581-gestao-carteira-professores[m
  [31mremotes/origin/feature/#584-atualizacao-relatorio-programa[m
  [31mremotes/origin/feature/#584-correcao-ordenacao[m
  [31mremotes/origin/feature/#584-correcoes-relatorio-programas[m
  [31mremotes/origin/feature/#584-programas-menu-search[m
  [31mremotes/origin/feature/#584-relatorio-programas-treino[m
  [31mremotes/origin/feature/#588-relatorio-professores[m
  [31mremotes/origin/feature/#588-relatorio-professores-substitutos[m
  [31mremotes/origin/feature/#592-ranking-professores[m
  [31mremotes/origin/feature/#596-integrado[m
  [31mremotes/origin/feature/#599-troca-empresa-em-telas-unicas[m
  [31mremotes/origin/feature/#607-atualizacao-filtros-aluno[m
  [31mremotes/origin/feature/#607-modificao-filtros-alunos[m
  [31mremotes/origin/feature/#617-configuracoes-sub-funcoes-aulas[m
  [31mremotes/origin/feature/#624-permissoes-criar-crud-perfil-acesso[m
  [31mremotes/origin/feature/#631-agenda-turmas-dia-endpoints[m
  [31mremotes/origin/feature/#633-integrado-criar-usuario-aplicativo-a-partir-perfil-aluno[m
  [31mremotes/origin/feature/#649-menu-global-pesquisa-inteligente-para-objetivos-e-periodizacao[m
  [31mremotes/origin/feature/#652-treino-independente-retirar-essas-configuracoes-ambiente-independente[m
  [31mremotes/origin/feature/#653-Ambiente-Independente-Erro-400-ao-adicionar-atividade[m
  [31mremotes/origin/feature/#653-Ambiente-Independente-Erro-400-ao-adicionar-atividade-2[m
  [31mremotes/origin/feature/#665-configuracoes-de-perfis-geral[m
  [31mremotes/origin/feature/#666-alteracao-focus-relatorio[m
  [31mremotes/origin/feature/#666-alterações-querys-filtro-por-professor[m
  [31mremotes/origin/feature/#666-permissao-ver-bi-outros-professores[m
  [31mremotes/origin/feature/#666-todas-alteracoes-BI[m
  [31mremotes/origin/feature/#666-tooltip[m
  [31mremotes/origin/feature/#674-Global-Input-Não-estou-conseguindo-acessar-configurações-pela-pesquisa[m
  [31mremotes/origin/feature/#701-modificacoes-modal-disponibilidade[m
  [31mremotes/origin/feature/#701-refatorar-cadastro-de-disponibilidades[m
  [31mremotes/origin/feature/#701-remocao-class-agendadisponibilidade[m
  [31mremotes/origin/feature/#707-Permissão-Alterar-nome-da-permissão-de-Tipo-de-evento[m
  [31mremotes/origin/feature/#718-correcoes-indicadores-bi[m
  [31mremotes/origin/feature/#718-seletor-professor-bi[m
  [31mremotes/origin/feature/#723-agenda-turma-dia-conectar-front-com-os-endpoints-do-back[m
  [31mremotes/origin/feature/#728-Agenda-Turma-dia-acoes-integracao[m
  [31mremotes/origin/feature/#729-carteira-professores-retirar-filtro-alunos-dentro-filtro[m
  [31mremotes/origin/feature/#743_duplica_valor[m
  [31mremotes/origin/feature/#745-Integrado-Correção-ortográfica-alteração-de-nomes[m
  [31mremotes/origin/feature/#750-agenda-servicos-dia-integracao[m
  [31mremotes/origin/feature/#759-Integrado-Gráfico-de-execução-de-treino-esta-quebrando-com-um-numero-especifico-de-execução[m
  [31mremotes/origin/feature/#760-integrado-agenda-dia-aulas-coletivas[m
  [31mremotes/origin/feature/#763-independente-agenda-dia-aulas-coletivas[m
  [31mremotes/origin/feature/#767-agenda-integrado-dia-turmas-coletivas[m
  [31mremotes/origin/feature/#770-integrado-agenda-dia-turmas-plano-credito[m
  [31mremotes/origin/feature/#775-agenda-lentidao-adicionar-confirmar-desmarcar-aluno-agenda[m
  [31mremotes/origin/feature/#779-Sub-situaçãodo-aluno-não-esta-sendo-apresentada[m
  [31mremotes/origin/feature/#786-independente-direcionar-para-o-perfil-do-aluno[m
  [31mremotes/origin/feature/#789-Programa-de-treino-não-esta-aparecendo-após-a-montagem[m
  [31mremotes/origin/feature/#796-integrado-erro-na-listagem-de-alunos-ao-adicionar-em-uma-turma[m
  [31mremotes/origin/feature/#797-Estou-precisando-dar-um-clique-na-tela-para-trazer-informações[m
  [31mremotes/origin/feature/#797-Estou-precisando-dar-um-clique-na-tela-para-trazer-informações-2[m
  [31mremotes/origin/feature/#798-av-fisica-usar-pressao-arterial-sistolica-e-diastolica[m
  [31mremotes/origin/feature/#803-Mascara-de-campos-na-montagem-de-treino[m
  [31mremotes/origin/feature/#804-criar-ids-para-o-teste-automatizado-agenda[m
  [31mremotes/origin/feature/#810-Agenda-Mudar-mensagem-e-localização-de-indicador[m
  [31mremotes/origin/feature/#811-BI-Av-Listar-os-clientes-de-cada-indicador[m
  [31mremotes/origin/feature/#811-bi-av-listar-os-clientes-de-cada-indicador-2[m
  [31mremotes/origin/feature/#814-Verificar-lentidao-em-alguns-momentos[m
  [31mremotes/origin/feature/#815-colocar-icone-de-carregando-na-agenda-visao-dia-e-semana[m
  [31mremotes/origin/feature/#817-avaliacao-fisica-colocar-o-carregando-apos-salvar-avaliacao-fisica[m
  [31mremotes/origin/feature/#820-integrado-filtro-por-professor-nao-esta-filtrando-corretamente[m
  [31mremotes/origin/feature/#826-tv-aula[m
  [31mremotes/origin/feature/#827-backend-tv-aula[m
  [31mremotes/origin/feature/#851-renovar-programas-de-treino[m
  [31mremotes/origin/feature/#866-Filtros-no-TV-Gestor-Frontend[m
  [31mremotes/origin/feature/#869-listagem-alunos-tv-gestor-frontend[m
  [31mremotes/origin/feature/#880-listagem-alunos-frontend[m
  [31mremotes/origin/feature/#938-logar-no-treino-beta-pelo-oamd[m
  [31mremotes/origin/feature/701-crud-disponibilidade[m
  [31mremotes/origin/feature/A2-120[m
  [31mremotes/origin/feature/A2-168[m
  [31mremotes/origin/feature/AKASUKI-524[m
  [31mremotes/origin/feature/AKATSUKI-289[m
  [31mremotes/origin/feature/AKATSUKI-547[m
  [31mremotes/origin/feature/AKATSUKI-564[m
  [31mremotes/origin/feature/AKATSUKI-635[m
  [31mremotes/origin/feature/AKATSUKI-643[m
  [31mremotes/origin/feature/AKATSUKI-645[m
  [31mremotes/origin/feature/PI-112[m
  [31mremotes/origin/feature/PI-113[m
  [31mremotes/origin/feature/PI-118[m
  [31mremotes/origin/feature/PI-162[m
  [31mremotes/origin/feature/PI-176[m
  [31mremotes/origin/feature/PI-176-Continuacao[m
  [31mremotes/origin/feature/PI-185[m
  [31mremotes/origin/feature/PI-188[m
  [31mremotes/origin/feature/PI-189[m
  [31mremotes/origin/feature/PI-191[m
  [31mremotes/origin/feature/PI-195[m
  [31mremotes/origin/feature/PI-204[m
  [31mremotes/origin/feature/PI-223[m
  [31mremotes/origin/feature/PI-97[m
  [31mremotes/origin/feature/auto-tag[m
  [31mremotes/origin/feature/canal-cliente-atendimento[m
  [31mremotes/origin/feature/commands-docker-setup-env[m
  [31mremotes/origin/feature/criando-arquivo-de-traducao-anamnese[m
  [31mremotes/origin/feature/criando-check-box-app[m
  [31mremotes/origin/feature/desafio-online[m
  [31mremotes/origin/feature/e2e[m
  [31mremotes/origin/feature/e2e-generalizar-funcao-login[m
  [31mremotes/origin/feature/e2e_carteira_dos_professores[m
  [31mremotes/origin/feature/e2e_carteira_professores[m
  [31mremotes/origin/feature/e2e_configuracao_avaliacao_fisica[m
  [31mremotes/origin/feature/e2e_funcao_login_parta_testes_comuns[m
  [31mremotes/origin/feature/e2e_pedro[m
  [31mremotes/origin/feature/e2e_relatorio_andamento[m
  [31mremotes/origin/feature/personal-fit-fase-3[m
  [31mremotes/origin/feature/plano_de_sucesso_individual[m
  [31mremotes/origin/feature/removendo-dependencia-angular2-multiselect-dropdown[m
  [31mremotes/origin/feature/spivi-novo-treino[m
  [31mremotes/origin/feature/teste_wod[m
  [31mremotes/origin/feature/traducao-dos-arquivos-de-traducao[m
  [31mremotes/origin/horfix/marketPlaceNavegacao[m
  [31mremotes/origin/hotfix/#883-crossfit-tela-esta-quebrando-ao-gerar-link-no-monitor[m
  [31mremotes/origin/hotfix/#916-edicao-ficha-erro-ao-adicionar-virgula-na-distancia[m
  [31mremotes/origin/hotfix/#932-Treinos-vencidos-com-data-de-vencimento-invalida[m
  [31mremotes/origin/hotfix/#934-Contagem-treinos-vencidos-diferentes-entre-BI-Indicadores-carteira-professores[m
  [31mremotes/origin/hotfix/#PI1Front[m
  [31mremotes/origin/hotfix/#PI26_novo_layout_canalcliente[m
  [31mremotes/origin/hotfix/A2-32[m
  [31mremotes/origin/hotfix/AKATSUKI-734[m
  [31mremotes/origin/hotfix/I9-480-CorrecaoImagemAtividadeInvertida[m
  [31mremotes/origin/hotfix/I9-686[m
  [31mremotes/origin/hotfix/TestAutomatizadoZwUi[m
  [31mremotes/origin/hotfix/ki-ui0.1.1[m
  [31mremotes/origin/hotfix/pactoStoreSMS[m
  [31mremotes/origin/hotifx[m
  [31mremotes/origin/master[m
  [31mremotes/origin/release/v1.0-rc1[m
  [31mremotes/origin/revisaoTesteCypress[m
  [31mremotes/origin/storybook-documentation[m
  [31mremotes/origin/treinamento[m
