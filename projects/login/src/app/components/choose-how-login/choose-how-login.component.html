<div class="choose-hint">
	<span i18n="@@login:choose-how-to-login">Como deseja continuar?</span>
</div>
<div *ngIf="!urlLoginAzAd" class="button-section">
	<button i18n="@@login:email" login-cat-flat-button routerLink="email">
		E-mail
	</button>
	<button i18n="@@login:celular" login-cat-flat-button routerLink="celular">
		Celular
	</button>
</div>

<div *ngIf="urlLoginAzAd" class="a-section">
	<a href="{{ urlLoginAzAd }}">
		<button i18n="@@login:azure" login-cat-flat-button>Azure AD</button>
	</a>
</div>
