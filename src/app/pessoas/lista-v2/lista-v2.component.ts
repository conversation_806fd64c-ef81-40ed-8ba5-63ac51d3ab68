import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiClienteService,
	AdmCoreApiNegociacaoService,
} from "adm-core-api";
import { CadastroAuxApiProfissaoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
} from "treino-api";
import {
	LayoutNavigationService,
	PermissaoService,
	PlataformModuleConfig,
	PlatformMenuItem,
} from "pacto-layout";
import { Observable } from "rxjs";
import {
	DataFiltro,
	GridFilterConfig,
	LoaderService,
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { isArray, isObject } from "util";
import { ClientDiscoveryService, PerfilRecursoPermissoTipo } from "sdk";
import { PactoDataGridOrdenacaoDirecao } from "../../cobranca/components/relatorio-cobranca/data-grid.model";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { MatDialog } from "@angular/material";
import { DatePipe } from "@angular/common";

@Component({
	selector: "pacto-lista-v2",
	templateUrl: "./lista-v2.component.html",
	styleUrls: ["./lista-v2.component.scss"],
})
export class ListaV2Component implements OnInit, AfterViewInit {
	cpfMask = [
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];
	foneMask = [
		"(",
		/[0-9]/,
		/[0-9]/,
		")",
		" ",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];
	numberMask = [
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];
	colunasVisiveis = {
		nome: true,
		tipo: true,
		emails: true,
		empresas: true,
		situacoes: true,
	};

	tipoOptions = [];
	treinoOptions = [];
	empresaOptions = [];
	situacoesOptions = [];
	categoriaOptions = [];
	classificacaoOptions = [];
	grupoOptions = [];
	profissaoOptions = [];
	professoresOptions = [];
	tipoDeConsultaOptions = [];
	consultoresOptions = [];
	recursoTelaAlunoPadraoEmpresa: boolean = true;
	isFiltersOpen: boolean = false;
	filterConfig: GridFilterConfig;
	formGroup = new FormGroup({
		parametro: new FormControl(),
		nome: new FormControl(),
		tipos: new FormControl(),
		treino: new FormControl(),
		email: new FormControl(),
		empresas: new FormControl(),
		situacoes: new FormControl(),
		categorias: new FormControl(),
		matricula: new FormControl(),
		classificacoes: new FormControl(),
		codigoDeAcessoAlternativo: new FormControl(),
		pessoa: new FormControl(),
		contrato: new FormControl(),
		cpf: new FormControl(),
		grupos: new FormControl(),
		passaporte: new FormControl(),
		placa: new FormControl(),
		profissoes: new FormControl(),
		responsavel: new FormControl(),
		cpfresponsavel: new FormControl(),
		rg: new FormControl(),
		rne: new FormControl(),
		telefone: new FormControl(),
		professores: new FormControl(),
		tipoDeConsulta: new FormControl(),
		colaborador: new FormControl(),
		consultores: new FormControl(),
	});
	sizePadrao = 30;
	itensPerPage = [
		{ id: 30, label: "30" },
		{ id: 50, label: "50" },
		{ id: 100, label: "100" },
	];
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("filterContentRef", { static: true })
	filterContentRef: TemplateRef<any>;

	endpointUrlListaPessoas;
	tableData: PactoDataGridConfig;
	@ViewChild("tableDataRef", { static: false })
	tableDataRef: RelatorioComponent;
	@ViewChild("buttonName", { static: true }) buttonName;

	@ViewChild("nomeCell", { static: true }) nomeCell: TemplateRef<any>;
	@ViewChild("tipoCell", { static: true }) tipoCell: TemplateRef<any>;
	@ViewChild("empresaCell", { static: true }) empresaCell: TemplateRef<any>;
	@ViewChild("situacoesCell", { static: true }) situacoesCell: TemplateRef<any>;
	@ViewChild("situacoesCellCol", { static: true })
	situacoesCellCol: TemplateRef<any>;
	@ViewChild("eMailCell", { static: true }) eMailCell: TemplateRef<any>;

	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	loadedFilters;
	permissaoCliente2_04: any;
	permissaoColaborador2_07: any;
	permissoesAluno;
	permissaoVisualizarAlunosOutrasCarteiras;
	permissaoExportar9_66: boolean = false;
	permissaoRelatorioCliente6_22: boolean = false;
	permissaoDadosCliente13_13: boolean = false;
	totalize = 0;
	appliedFilters = [];
	constructor(
		private router: Router,
		private rest: RestService,
		public sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		private cd: ChangeDetectorRef,
		private layoutNavigationService: LayoutNavigationService,
		private clienteService: AdmCoreApiClienteService,
		private profissaoService: CadastroAuxApiProfissaoService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private notifyService: SnotifyService,
		private loaderService: LoaderService,
		private permissaoService: PermissaoService,
		private dialogService: MatDialog,
		private datePipe: DatePipe
	) {}

	ngOnInit() {
		let textoPesquisa;
		this.carregarRecursoPadraoEmpresa();
		this.carregarPermissoes();
		this.fecharMenu();
		this.initTable();
		setTimeout(() => {
			this.recarregarFiltros();
		});
		this.configFilters();
		this.formGroup.get("parametro").valueChanges.subscribe((valor) => {
			textoPesquisa = valor;
			setTimeout(() => {
				if (textoPesquisa === valor || valor === "") {
					this.limparPaginacao();
					this.consultar();
				}
			}, 500);
		});
		this.cd.detectChanges();
	}

	carregarRecursoPadraoEmpresa() {
		this.admCoreApiNegociacaoService
			.recursoPadraoEmpresa("TELA_ALUNO")
			.subscribe(
				(response) => {
					this.recursoTelaAlunoPadraoEmpresa = response;
				},
				(httpErrorResponse) => {
					this.recursoTelaAlunoPadraoEmpresa = false;
				}
			);
	}

	private carregarPermissoes() {
		this.permissoesAluno = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.ALUNOS
		);
		let permissaoCarteira = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.VER_ALUNOS_OUTRAS_CARTEIRAS
		);
		if (permissaoCarteira === undefined) {
			permissaoCarteira = false;
		}
		this.permissaoVisualizarAlunosOutrasCarteiras = permissaoCarteira;
	}

	ngAfterViewInit() {
		this.permissaoCliente2_04 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.04"
			);
		this.permissaoColaborador2_07 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.07"
			);
		this.permissaoExportar9_66 = this.permissaoService.temPermissaoAdm("9.66");
		this.permissaoRelatorioCliente6_22 =
			this.permissaoService.temPermissaoAdm("6.22");
		this.permissaoDadosCliente13_13 =
			this.permissaoService.temPermissaoAdm("13.13");
	}

	fecharMenu() {
		try {
			const menu = document.getElementById("sidebar-menu-toggle");
			if (menu.classList.contains("opened")) {
				menu.click();
			}
		} catch (e) {}
	}

	obterState(): PactoDataGridState {
		const state: PactoDataGridState = {
			paginaNumero: 0,
			paginaTamanho: this.sizePadrao,
			ordenacaoColuna: "nome",
			ordenacaoDirecao: PactoDataGridOrdenacaoDirecao.ASC,
		};

		const sortLocalstorage: any = JSON.parse(
			localStorage.getItem("listaPessoasSortTable")
		);
		if (sortLocalstorage) {
			state.ordenacaoColuna = sortLocalstorage.columnName;
			state.ordenacaoDirecao = sortLocalstorage.direction;
		}
		const pageLocalstorage: any = JSON.parse(
			localStorage.getItem("listaPessoasPage")
		);
		if (pageLocalstorage) {
			if (pageLocalstorage.pagina < 0) {
				pageLocalstorage.pagina = 0;
			}
			state.paginaNumero = pageLocalstorage.pagina;
		}
		const sizeLocalstorage: any = JSON.parse(
			localStorage.getItem("listaPessoasSize")
		);
		if (sizeLocalstorage) {
			state.paginaTamanho = sizeLocalstorage.tamanho;
		}
		return state;
	}

	initTable(): Observable<boolean> {
		const state = this.obterState();
		setTimeout(() => {
			(this.endpointUrlListaPessoas = this.rest.buildFullUrlAdmCore(
				`clientes/alunoColaborador?carteiras=` +
					this.permissaoVisualizarAlunosOutrasCarteiras +
					"&empresas=" +
					this.permiteTodasEmpresas()
			)),
				(this.tableData = new PactoDataGridConfig({
					endpointUrl: this.endpointUrlListaPessoas,
					state,
					quickSearch: false,
					ghostLoad: true,
					ghostAmount: 3,
					showFilters: false,
					columns: [
						{
							nome: "nome",
							titulo: "nome",
							visible: this.colunasVisiveis.nome,
							ordenavel: true,
							celula: this.nomeCell,
							buscaRapida: true,
						},
						{
							nome: "tipo",
							titulo: "tipo",
							styleClass: "center",
							visible: this.colunasVisiveis.tipo,
							ordenavel: true,
							celula: this.tipoCell,
						},
						{
							nome: "emails",
							titulo: "emails",
							styleClass: "center",
							visible:
								this.colunasVisiveis.emails && this.permissaoDadosCliente13_13,
							ordenavel: true,
							celula: this.eMailCell,
							buscaRapida: true,
						},
						{
							nome: "empresas",
							titulo: "empresas",
							visible: this.colunasVisiveis.empresas,
							ordenavel: true,
							celula: this.empresaCell,
						},
						{
							nome: "periodoContrato",
							titulo: "Período do contrato",
							styleClass: "center",
							visible: true,
							ordenavel: true,
							orderColumn: "clienteDataVigenciaInicioContrato",
							valueTransform: (_, row) => {
								const formattedVigenciaInicio = this.datePipe.transform(
									row.clienteDataVigenciaInicioContrato,
									"dd/MM/yyyy"
								);
								const formattedVigenciaFim = this.datePipe.transform(
									row.clienteDataVigenciaFimContrato,
									"dd/MM/yyyy"
								);
								return `${formattedVigenciaInicio || ""} - ${
									formattedVigenciaFim || ""
								}`;
							},
						},
						{
							nome: "situacaoCliente",
							titulo: "Cliente",
							styleClass: "center",
							visible: this.colunasVisiveis.situacoes,
							ordenavel: true,
							orderColumn: "clienteSituacao",
							celula: this.situacoesCell,
						},
						{
							nome: "situacaoColaborador",
							titulo: "Colaborador",
							styleClass: "center",
							visible: this.colunasVisiveis.situacoes,
							ordenavel: true,
							orderColumn: "colaboradorSituacao",
							celula: this.situacoesCellCol,
						},
					],
					actions: [
						{
							nome: "edit",
							iconClass: "pct pct-edit cor-azulim05",
							tooltipText: "Editar",
							actionFn: (row) => this.goToUserPage(row, "edit"),
						},
					],
				}));
			this.cd.detectChanges();
		});
		return new Observable<boolean>();
	}

	goToUserPage(row, tipo: "user" | "edit") {
		this.loaderService.show();
		// setTimeout(() => {
		// // 	this.limparPactoCatTolltip();
		// }, 500);
		if (row.tipos.includes("Cliente")) {
			const permition = this.permissaoCliente2_04;
			if (tipo === "edit") {
				// const isPermited = permition && permition.tipoPermissoes.find(tp =>
				// 	tp === PerfilRecursoPermissoTipo.EDITAR
				// 	|| tp === PerfilRecursoPermissoTipo.TOTAL
				// 	|| tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
				// );
				const isPermited = true;
				if (isPermited) {
					this.router.navigate([
						"pessoas",
						"perfil-v2",
						"configuracoes-cliente",
						row.matricula,
						row.codigo || row.pessoa,
					]);
				} else {
					// this.notifyService.warning('Você não possui as permissões necessárias (2.04 - Cliente) para acessar esta tela.');
					this.router.navigateByUrl(
						`/cadastros/alunos/perfil/${row.matricula}%3Forigem%3Dbi`
					);
					return;
				}
			} else if (tipo === "user") {
				// const isPermited = permition && permition.tipoPermissoes.find(tp =>
				// 	tp === PerfilRecursoPermissoTipo.CONSULTAR
				// 	|| tp === PerfilRecursoPermissoTipo.INCLUIR
				// 	|| tp === PerfilRecursoPermissoTipo.EDITAR
				// 	|| tp === PerfilRecursoPermissoTipo.EXCLUIR
				// 	|| tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
				// 	|| tp === PerfilRecursoPermissoTipo.TOTAL
				// );
				const isPermited = true;
				if (isPermited) {
					this.router.navigate(["pessoas", "perfil-v2", row.matricula]);
				} else {
					// this.notifyService.warning('Você não possui as permissões necessárias (2.04 - Cliente) para acessar esta tela.');
					this.router.navigateByUrl(
						`/cadastros/alunos/perfil/${row.matricula}%3Forigem%3Dbi`
					);
					return;
				}
			}
		} else if (row.tipos.includes("Colaborador")) {
			const permition = this.permissaoColaborador2_07;
			if (permition) {
				const nomeFuncionalidade = row.colaborador
					? `COLABORADOR&codigoColaborador=${row.colaborador}`
					: `COLABORADOR`;
				const menu: PlatformMenuItem = {
					id: "Colaborador",
					route: {
						queryParams: {
							funcionalidadeNome: nomeFuncionalidade,
							openAsPopup: true,
							windowWidth: 1070,
							windowHeight: 600,
						},
					},
				};
				this.layoutNavigationService.makeRedirect(
					this.layoutNavigationService.redirectToModule(
						PlataformModuleConfig.ADM_LEGADO,
						menu
					) as Observable<string>
				);
			} else {
				this.notifyService.warning(
					"Você não possui as permissões necessárias (2.07 - Colaborador) para acessar esta tela."
				);
				return;
			}
		}
	}

	configFilters() {
		this.tipoOptions = [
			{ id: 1, label: "Cliente" },
			{ id: 2, label: "Colaborador" },
		];
		this.treinoOptions = [
			{ id: "MINHA_CARTEIRA", label: "Minha carteira" },
			{ id: "NA_ACADEMIA", label: "Na academia" },
			{
				id: "NA_ACADEMIA_COM_TREINO_VENCIDO",
				label: "Na academia com treino vencido",
			},
			{
				id: "NA_ACADEMIA_COM_TREINO_A_VENCER",
				label: "Na academia com treino a vencer",
			},
			{
				id: "NA_ACADEMIA_DA_MINHA_CARTEIRA",
				label: "Na academia da minha carteira",
			},
			{ id: "NA_ACADEMIA_SEM_TREINO", label: "Na academia sem treino" },
			{ id: "TREINO_VENCIDO", label: "Treino Vencido" },
			{ id: "SEM_TREINO", label: "Sem Treino" },
		];
		this.empresaOptions = this.sessionService.empresas
			.filter(
				(e, index, self) =>
					self.findIndex((opt) => opt.codigo === e.codigo) === index
			)
			.map((e) => ({
				id: e.codigo,
				label: e.nome,
			}));

		this.situacoesOptions = [
			{
				id: "VI",
				label: "Visitante",
			},
			{
				id: "NO",
				label: "Normal",
			},
			{
				id: "AT",
				label: "Ativo",
			},
			{
				id: "DEP",
				label: "Dependente",
			},
			{
				id: "FR",
				label: "Freepass",
			},
			{
				id: "GY",
				label: "Gympass",
			},
			{
				id: "GD",
				label: "Gogood",
			},
			{
				id: "TP",
				label: "TotalPass",
			},
			{
				id: "AA",
				label: "Aula avulsa",
			},
			{
				id: "DI",
				label: "Diaria",
			},
			{
				id: "IN",
				label: "Inativo",
			},
			{
				id: "CA",
				label: "Cancelado",
			},
			{
				id: "DE",
				label: "Desistente",
			},
			{
				id: "VE",
				label: "Vencido",
			},
			{
				id: "TR",
				label: "Trancado",
			},
			{
				id: "AE",
				label: "Atestado médico",
			},
			{
				id: "CR",
				label: "Férias",
			},
			{
				id: "AV",
				label: "A vencer",
			},
		];

		this.clienteService.obterTodasCategorias().subscribe((result) => {
			this.categoriaOptions = result.content.map((categoria: any) => {
				return { id: categoria.codigo, label: categoria.nome };
			});
		});
		this.clienteService
			.obterTodasClassificacoes(false, true)
			.subscribe((result) => {
				this.classificacaoOptions = result.content.map((classificacao: any) => {
					return { id: classificacao.codigo, label: classificacao.nome };
				});
			});
		this.clienteService.obterTodosGrupos().subscribe((result) => {
			this.grupoOptions = result.content.map((grupo: any) => {
				return { id: grupo.codigo, label: grupo.descricao };
			});
		});
		this.profissaoService.all().subscribe((result) => {
			this.profissaoOptions = result.content.map((profissao) => {
				return { id: profissao.codigo, label: profissao.descricao };
			});
		});
		this.clienteService.obterConsultoresAtivos().subscribe((result) => {
			this.consultoresOptions = result.content.map((consultor: any) => {
				return { id: consultor.codigo, label: consultor.pessoa.nome };
			});
		});
		this.clienteService.obterProfessoresAtivos().subscribe((result) => {
			this.professoresOptions = result.content.map((classificacao: any) => {
				return { id: classificacao.codigo, label: classificacao.pessoa.nome };
			});
		});

		this.tipoDeConsultaOptions = [
			{ id: "ultimos30Dias", label: "Contrato - Ultimos 30 dias" },
			{ id: "ultimos15Dias", label: "Contrato - Ultimos 15 dias" },
			{ id: "ultimos7Dias", label: "Contrato - Ultimos 7 dias" },
			{ id: "hoje", label: "Contrato - Hoje" },
			{ id: "ultimos30DiasBV", label: "BV - Ultimos 30 dias" },
			{ id: "ultimos15DiasBV", label: "BV - Ultimos 15 dias" },
			{ id: "ultimos7DiasBV", label: "BV - Ultimos 7 dias" },
			{ id: "hojeBV", label: "BV - Hoje" },
		];
	}

	recarregarFiltros() {
		this.loadedFilters = window.localStorage.getItem("filtroListaV2");
		if (this.loadedFilters) {
			Object.keys(this.loadedFilters).forEach((item) => {
				if (
					!this.loadedFilters[item] ||
					this.loadedFilters[item].length === 0
				) {
					delete this.loadedFilters[item];
				}
			});
			if (this.loadedFilters && this.loadedFilters.length > 0) {
				this.formGroup.patchValue(JSON.parse(this.loadedFilters));
				if (this.loadedFilters !== "{}") {
					this.cd.detectChanges();
					this.consultar();
					this.notifyService.info("Filtros carregados da última pesquisa");
				}
			}
		}
	}

	columnDisplayHandler(nomeColuna) {
		const colunaVisivel = (this.colunasVisiveis[nomeColuna] =
			!this.colunasVisiveis[nomeColuna]);
		this.tableData.columns.find(
			(colunaConfig) => colunaConfig.titulo === nomeColuna
		).visible = colunaVisivel;
	}

	limparPaginacao() {
		this.pageEvent(1);
		this.sizeEvent(this.sizePadrao);
		this.tableData.state.paginaNumero = 0;
		this.tableData.state.paginaTamanho = this.sizePadrao;
	}

	consultar() {
		this.limparPaginacao();
		const formControls = this.formGroup.controls;
		const valorToLS = this.formGroup.getRawValue();
		Object.keys(valorToLS).forEach((item) => {
			if (!valorToLS[item] || valorToLS[item].length === 0) {
				delete valorToLS[item];
			}
		});
		window.localStorage.setItem("filtroListaV2", JSON.stringify(valorToLS));
		if (JSON.stringify(valorToLS) == "{}") {
			this.tableDataRef.temporaryFilters = null;
		}
		for (const controlName in formControls) {
			if (formControls.hasOwnProperty(controlName)) {
				const controlValue = formControls[controlName].value;
				if (controlValue !== null && controlValue !== undefined) {
					const objFilter = this.valueToFilter(controlValue);
					this.tableDataRef.addFilter(controlName, objFilter);
				}
			}
		}
		this.tableDataRef.reloadData();
		this.closeSideFilter();
	}

	limparFiltros() {
		this.formGroup.reset();
		Object.keys(this.formGroup.controls).forEach((key) => {
			this.tableDataRef.removeFilter(key.toString());
		});
		this.closeSideFilter();
	}

	isAplicouFiltro(): boolean {
		try {
			const valorToLS = this.formGroup.getRawValue();
			Object.keys(valorToLS).forEach((item) => {
				if (
					!valorToLS[item] ||
					valorToLS[item].length === 0 ||
					item === "parametro"
				) {
					delete valorToLS[item];
				}
			});
			const jsonFilter = JSON.stringify(valorToLS);
			if (jsonFilter !== "{}") {
				return true;
			}
			return false;
		} catch (e) {
			return false;
		}
	}

	// closeSideFilter() {
	// 	this.dialogService.closeAll();
	// try {
	// 	if (document.getElementById('filtrer-lista-pessoas').getElementsByClassName('dropdown-menu show').length === 0) {
	// 		return;
	// 	}
	// 	const doc = document.getElementById('filtrer-lista-pessoas');
	// 	if (doc) {
	// 		const element: HTMLElement = doc.getElementsByClassName('btn btn-primary btn-icon dropdown-toggle')[0] as HTMLElement;
	// 		element.click();
	// 	}
	// } catch (e) {
	// 	console.log(e);
	// }
	// setTimeout(() => {
	// // 	this.limparPactoCatTolltip();
	// }, 500);
	// }

	valueToFilter(controlValue) {
		if (isArray(controlValue)) {
			return controlValue.map((i) => i.id);
		} else if (isObject(controlValue)) {
			return controlValue.id;
		} else {
			return controlValue ? controlValue : null;
		}
	}

	iconClickFn(event: { row: any; iconName: string }) {
		// setTimeout(() => {
		// // 	this.limparPactoCatTolltip();
		// }, 500);
		switch (event.iconName) {
			case "edit":
				this.goToUserPage(event.row, "edit");
				break;

			default:
				break;
		}
	}

	stringToKebab(str: string) {
		if (str) {
			return str
				.match(
					/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
				)
				.map((x) => x.toLowerCase())
				.join("-");
		} else {
			return "";
		}
	}

	cadastrarNovoColaborador() {
		const permition = this.permissaoColaborador2_07;
		if (
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			)
		) {
			const menu: PlatformMenuItem = {
				id: "cadastrarNovoColaborador",
				route: {
					queryParams: {
						funcionalidadeNome: "COLABORADOR",
						openAsPopup: true,
						windowWidth: 1070,
						windowHeight: 600,
					},
				},
			};
			this.layoutNavigationService.makeRedirect(
				this.layoutNavigationService.redirectToModule(
					PlataformModuleConfig.ADM_LEGADO,
					menu
				) as Observable<string>
			);
		} else {
			this.notifyService.warning(
				"Você não possui as permissões necessárias (2.07 - Colaborador) para acessar esta tela."
			);
		}
	}

	permiteTodasEmpresas() {
		return this.permissaoService.temPermissaoAdm("9.50");
	}

	cadastrarNovoCliente() {
		const permition = this.permissaoCliente2_04;
		if (
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			)
		) {
			const menu: PlatformMenuItem = {
				id: "cadastrarNovoCliente",
				route: {
					queryParams: {
						urlRedirect: "uriPrecadastro",
						jspPage: "preCadastro.jsp",
						windowWidth: 1070,
						windowHeight: 600,
					},
				},
			};
			this.layoutNavigationService.makeRedirect(
				this.layoutNavigationService.redirectToModule(
					PlataformModuleConfig.ADM_LEGADO,
					menu
				) as Observable<string>
			);
		} else {
			this.notifyService.warning(
				"Você não possui as permissões necessárias (2.04 - Cliente) para acessar esta tela."
			);
		}
	}

	relatorioDePessoa() {
		if (!this.permissaoRelatorioCliente6_22) {
			this.notifyService.warning(
				"Você não possui as permissões necessárias (6.22 - Relatório Geral de Clientes)."
			);
			return;
		}
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				const url = `${urlZw}&funcionalidadeNome=GERAL_CLIENTES&jspPage=listasRelatorios.jsp&menu=true`;
				window.open(url, "_blank");
			});
	}

	openFeedbackForm() {
		window.open("https://forms.gle/b1jYXM8Nug5PeM3q8", "_blank");
	}

	getFiltersShare(): DataFiltro {
		return { filters: this.formGroup.getRawValue(), configs: {} };
	}

	sortEvent(event: any) {
		localStorage.setItem(
			"listaPessoasSortTable",
			JSON.stringify({
				columnName: event.columnName,
				direction: event.direction,
			})
		);
		// setTimeout(() => {
		// // 	this.limparPactoCatTolltip();
		// }, 500);
	}

	sizeEvent(event: any) {
		localStorage.setItem(
			"listaPessoasSize",
			JSON.stringify({
				tamanho: event,
			})
		);
		// setTimeout(() => {
		// // 	this.limparPactoCatTolltip();
		// }, 500);
	}

	pageEvent(event: any) {
		localStorage.setItem(
			"listaPessoasPage",
			JSON.stringify({
				pagina: event - 1,
			})
		);
		// setTimeout(() => {
		// // 	this.limparPactoCatTolltip();
		// }, 500);
	}

	filterChangeEvent(event: any) {
		localStorage.setItem(
			"listaPessoasFilterTable",
			JSON.stringify({
				filters: event.filters,
				configs: event.configs,
				sortField: event.sortField,
				sortDirection: event.sortDirecton,
			})
		);
		// setTimeout(() => {
		// // 	this.limparPactoCatTolltip();
		// }, 500);
	}

	getSituacoesOptionsSort() {
		return this.situacoesOptions.sort(this.dynamicSort("label"));
	}

	dynamicSort(property) {
		var sortOrder = 1;
		if (property[0] === "-") {
			sortOrder = -1;
			property = property.substr(1);
		}
		return function (a, b) {
			// /* next line works with strings and numbers,
			//  * and you may want to customize it to your needs
			//  */
			var result =
				a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;
			return result * sortOrder;
		};
	}

	getTextTooltip(tipo: "aluno" | "contrato" | "outro", situacaoSigla): string {
		let captalizePipe = new CaptalizePipe();
		let valorPipeado = captalizePipe.transform(this.getSituacao(situacaoSigla));
		if (tipo === "aluno") {
			return `Situação do cliente: ${valorPipeado}`;
		}
		if (tipo === "contrato") {
			return `Situação do contrato: ${valorPipeado}`;
		}
		if (tipo === "outro") {
			return `${valorPipeado}`;
		}
	}

	getSituacao(s: string): string {
		switch (s) {
			case "AE":
				return "ATESTADO";
			case "AT":
				return "ATIVO";
			case "AV":
				return "A VENCER";
			case "NO":
				return "NORMAL";
			case "CR":
				return "FÉRIAS";
			case "DE":
				return "DESISTENTE";
			case "CA":
				return "CANCELADO";
			case "VE":
				return "VENCIDO";
			case "DI":
				return "DIÁRIA";
			case "IN":
				return "INATIVO";
			case "VI":
				return "VISITANTE";
			case "TP":
				return "TOTALPASS";
			case "TV":
				return "VENCIDO";
			case "TR":
				return "TRANCADO";
			case "GY":
				return "GYMPASS";
			case "GD":
				return "GOGOOD";
			case "PE":
				return "FREEPASS";
			case "DEP":
				return "DEPENDENTE";
			default:
				return "OUTRO";
		}
	}

	voltarAntiga(event: any) {
		if (event.origem === "ZW") {
			const menu: PlatformMenuItem = {
				id: "Clientes",
				route: {
					queryParams: {
						moduloGoBackRedirect: this.sessionService.goBackModule,
						urlRedirect: "uriClientes",
						openAsPopup: false,
						windowWidth: 1070,
						windowHeight: 600,
					},
				},
			};
			this.layoutNavigationService.makeRedirect(
				this.layoutNavigationService.redirectToModule(
					PlataformModuleConfig.ADM_LEGADO,
					menu
				) as Observable<string>
			);
		} else {
			this.router.navigate(["cadastros", "alunos", "listagem"]);
		}
	}

	// limparPactoCatTolltip() {
	// 	try {
	// 		const elementsByClassName = document.getElementsByClassName('pacto-cat-tolltip');
	// 		const array = Array.from(elementsByClassName);
	// 		for (const element of array) {
	// 			document.getElementById(element.id).style.visibility = 'hidden';
	// 		}
	// 	} catch (e) {
	// 		console.log(e);
	// 	}
	// }

	// openFilter() {
	// 	 this.tableDataRef.openSideFilter();
	// }
	// closeSideFilter(){
	// this.tableDataRef.closeSideFilter();
	// }

	openFilter() {
		this.dialogService.open(this.filterContentRef, {
			position: {
				right: "0px",
			},
			width: "30%",
			height: "100%",
			autoFocus: false,
			disableClose: true,
			panelClass: "side-filter-panel",
		});
		this.isFiltersOpen = true;
	}

	closeSideFilter() {
		this.dialogService.closeAll();
		this.isFiltersOpen = false;
	}

	get getSelectedFilters() {
		const formValues = this.formGroup.getRawValue();
		const activeFiltersCount = Object.values(formValues).filter(
			(value) => value !== null && value !== undefined && value !== ""
		).length;
		return activeFiltersCount;
	}

	updateResumes() {
		const captalizePipe = new CaptalizePipe();
		this.appliedFilters = Object.entries(this.formGroup.controls)
			.map(([key, control]) => {
				const controlValue = control.value;
				if (controlValue) {
					let value;

					if (Array.isArray(controlValue)) {
						value = controlValue
							.map((item) => captalizePipe.transform(item.label || item))
							.join(", ");
					} else if (typeof controlValue === "object" && controlValue.label) {
						value = captalizePipe.transform(controlValue.label);
					} else {
						value = captalizePipe.transform(controlValue);
					}

					const label = captalizePipe.transform(key);
					return { label, value };
				}
				return null;
			})
			.filter((filter) => filter !== null);

		this.totalize = this.tableDataRef
			? this.tableDataRef.totalItems
			: this.totalize;
		this.cd.detectChanges();
	}
}
