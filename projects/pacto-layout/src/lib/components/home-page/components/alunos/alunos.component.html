<div class="alunos">
	<p class="alunos-title">
		Aparecem os 3 últimos alunos que acessaram a academia e que tenha vínculo de
		“Carteira” com o usuário que está logado
	</p>
	<ds3-diviser></ds3-diviser>
	<div *ngIf="!!alunos; else emptyStateAluno" class="content">
		<ng-container *ngFor="let aluno of alunos">
			<div class="aluno">
				<span class="timestamp">
					{{ aluno.hora }}
				</span>
				<div class="entry">
					<div
						[ngStyle]="{ 'background-color': aluno.cor }"
						class="bar {{ aluno.cor }}"></div>
					<div class="data">
						<div class="texts">
							<div class="avatar">
								<img
									src="{{
										aluno.foto
											? aluno.foto
											: 'pacto-ui/images/user-image-default.svg'
									}}" />
							</div>
							<div class="values">
								<a
									(click)="goToAluno(aluno.matricula)"
									class="nome"
									ds3-text-button>
									{{ aluno?.nome }}
								</a>
								<span class="plano">
									<strong>Plano:</strong>
									{{ aluno?.plano }}
								</span>
								<span class="parq">
									<strong>Par-q:</strong>
									{{ aluno.parq ? aluno.parq : "Positivo, Não Assinado" }}
								</span>
							</div>
							<div class="chips">
								<div *ngIf="!!aluno?.situacao">
									<ds3-status class="sit-{{ aluno.situacao }}" color="info">
										{{ aluno?.situacao | uppercase }}
									</ds3-status>
								</div>
								<div *ngIf="!!aluno?.situacaoContrato">
									<ds3-status
										class="sit-{{ aluno.situacaoContrato }}"
										color="info">
										{{ aluno?.situacaoContrato | uppercase }}
									</ds3-status>
								</div>
							</div>
						</div>

						<div class="status">
							<span *ngIf="!aluno.aniversariante" class="info">
								<i class="pct pct-info"></i>
								{{ aluno?.aviso?.descricao }}
							</span>

							<span *ngIf="aluno.aniversariante" class="aniversariante">
								<i class="pct pct-cake"></i>
								Hoje é aniversário do cliente
							</span>
							<span
								*ngIf="aluno.avisos && aluno.avisos.length > 0"
								[ds3Tooltip]="tooltip"
								class="acesso-mais-info">
								+{{
									aluno.avisos.length +
										(aluno.aviso && aluno.aniversariante ? 1 : 0)
								}}
							</span>
						</div>
						<ng-template #tooltip>
							<div *ngIf="aluno.aviso && aluno.aniversariante">
								{{ aluno?.aviso?.descricao }}
							</div>
							<div *ngFor="let a of aluno.avisos">{{ a?.descricao }}</div>
						</ng-template>
					</div>
				</div>
			</div>
		</ng-container>
	</div>
	<div *ngIf="!!alunos" class="actions">
		<button (click)="abrirLista()" ds3-text-button>Ver todos os acessos</button>
	</div>
</div>

<ng-template #emptyStateAluno>
	<div class="emptyState">
		<img alt="" src="pacto-ui/images/empty-access.svg" />
		<span class="empty-title">Sem acessos no momento</span>
		<span class="empty-info">
			Aqui você poderá acompanhar os acessos dos alunos na sua academia
		</span>
	</div>
</ng-template>
