import { Location } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	HostBinding,
	OnDestroy,
	OnInit,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";

import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoAutorizarAcessoService, Recurso } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { AlunoColaboradorUsuarioService } from "pessoa-ms-api";
import { BehaviorSubject } from "rxjs";
import { delay, map, switchMap, tap } from "rxjs/operators";
import { PerfilRecursoPermissoTipo } from "sdk";
import {
	CatTabsTransparentComponent,
	LoaderService,
	TraducoesXinglingComponent,
} from "ui-kit";
import { CobrancaService } from "../../../cobranca/cobranca.service";
import { ConfigCliFormValidationService } from "./config-cli-form-validation.service";
import { FormGroupAcessoCatraca } from "./forms/acesso-catraca-form";
import { FormGroupAcesso } from "./forms/acesso-form";
import { FormGroupDadosBasicos } from "./forms/dados-basicos-form";
import { FormGroupDadosFinanceiros } from "./forms/dados-financeiros-form";
import { FormGroupDependentes } from "./forms/dependentes-form";
import { FormGroupFamiliares } from "./forms/familiares-form";
import { FormGroupRh } from "./forms/rh-form";
import { FormGroupVinculos } from "./forms/vinculos-form";

declare var moment;

enum ABA {
	DADOS_BASICOS,
	ACESSO_A_CATRACA,
	DADOS_FINANCEIROS,
	FAMILIARES,
	VINCULOS,
	RH,
	ACESSO,
	REPLICAR_EMPRESA,
	DEPENDENTES,
	PACTO,
}

@Component({
	selector: "pacto-configuracoes-cliente",
	templateUrl: "./configuracoes-cliente.component.html",
	styleUrls: ["./configuracoes-cliente.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ConfiguracoesClienteComponent
	implements OnInit, OnDestroy, AfterViewInit
{
	@ViewChild("tabs", { static: true })
	public tabs: CatTabsTransparentComponent;

	@HostBinding("class.pacto-configuracoes-cliente")
	styleEncapsulation = true;

	matricula: string;
	codigo: number;
	codigoCliente: number;
	dadosPessoais: ClienteDadosPessoais;
	showErrorMessages: boolean = false;
	recursoFamiliar: Recurso = undefined;
	recursoVinculo: Recurso = undefined;
	recursoCliente: Recurso = undefined;

	form: FormGroup = new FormGroup({
		abaAcessoCatraca: FormGroupAcessoCatraca,
		abaDadosBasicos: FormGroupDadosBasicos,
		abaFamiliares: FormGroupFamiliares,
		abaDependentes: FormGroupDependentes,
		abaDadosFinanceiros: FormGroupDadosFinanceiros,
		abaVinculos: FormGroupVinculos,
		abaRh: FormGroupRh,
		abaAcesso: FormGroupAcesso,
	});

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	loadData$ = new BehaviorSubject<null>(null);
	loading = true;

	get userIsOnlyClient(): boolean {
		const tiposDePessoa = this.form
			.get("abaDadosBasicos")
			.get("tipoDePessoa").value;

		if (!tiposDePessoa || tiposDePessoa.length === 0) {
			return true;
		}

		if (tiposDePessoa && tiposDePessoa.length > 1) {
			return false;
		}

		const isClientOnly = tiposDePessoa.find((v) => v.sigla === "CI");

		return isClientOnly;
	}

	constructor(
		private route: ActivatedRoute,
		private pessoaService: AlunoColaboradorUsuarioService,
		private router: Router,
		private msAdmCoreService: AdmCoreApiClienteService,
		private snotify: SnotifyService,
		private cobrancaService: CobrancaService,
		private location: Location,
		private el: ElementRef,
		private cd: ChangeDetectorRef,
		private loaderService: LoaderService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		private configClienteFormValidation: ConfigCliFormValidationService
	) {}

	ngOnInit() {
		this.recursoFamiliar =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.12"
			);
		this.recursoVinculo =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.29"
			);
		this.recursoCliente =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.04"
			);
		document.querySelector("main").scrollTop = 0;
		this.loading = true;
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("codPessoa");
		this.codigo =
			this.route.snapshot.params["codigo-pessoa"] ||
			sessionStorage.getItem("codCliente");

		if (!this.matricula || !this.codigo) {
			this.router.navigate(["/cadastros", "alunos", "listagem"]);
		}
		this.loadData$
			.pipe(
				tap(() => this.loaderService.initForce()),
				delay(1),
				switchMap(() => {
					return this.pessoaService.getConfigData(this.codigo);
				}),
				map((d) => {
					if (d.abaDadosBasicos.dataNasc) {
						d.abaDadosBasicos.dataNasc = d.abaDadosBasicos.dataNasc.match(
							/\d{4}-\d{2}-\d{2}/gm
						)
							? d.abaDadosBasicos.dataNasc
							: this.convertDateStringToMilliseconds(
									d.abaDadosBasicos.dataNasc
							  );
					}
					d.abaRh = {
						modalidadesColaboradorTrabalha: null,
						departamento: {
							codigo: null,
							nome: null,
						},
						cargaHoraria: null,
						valorSalario: null,
						observacao: null,
						tamanhoCamisa: null,
						tamanhoCalca: null,
						documentos: null,
						afastamentoFerias: null,
						tipoColaborador: null,
						autenticarGoogle: null,
						...d.abaRh,
					};
					if (
						d.abaDadosBasicos.endereco &&
						Array.isArray(d.abaDadosBasicos.endereco)
					) {
						const enderecosFormArray = this.form.get(
							"abaDadosBasicos.enderecos"
						) as FormArray;

						// Limpe todos os controles existentes no FormArray
						while (enderecosFormArray.length > 0) {
							enderecosFormArray.removeAt(0);
						}

						// Crie e adicione controles para cada endereço na resposta
						d.abaDadosBasicos.endereco.forEach((endereco) => {
							const enderecoFormGroup = new FormGroup({
								codigo: new FormControl(endereco.codigo),
								endereco: new FormControl(endereco.endereco),
								bairro: new FormControl(endereco.bairro),
								numero: new FormControl(endereco.numero),
								complemento: new FormControl(endereco.complemento),
								tipoEndereco: new FormControl({
									codigo: endereco.tipoEndereco,
								}),
								// cidade: new FormControl(endereco.cidade),
								// estado: new FormControl(endereco.estado),
								cep: new FormControl(endereco.cep),
								pessoa: new FormControl(endereco.pessoa),
							});

							enderecosFormArray.push(enderecoFormGroup);
						});
					}
					if (d.abaDadosBasicos) {
						if (d.abaDadosBasicos.genero) {
							d.abaDadosBasicos.genero = {
								codigo: d.abaDadosBasicos.genero,
							};
						}
						if (d.abaDadosBasicos.sexo) {
							d.abaDadosBasicos.sexo = {
								codigo: d.abaDadosBasicos.sexo,
							};
						}
						if (d.abaDadosBasicos.estadoCivil) {
							d.abaDadosBasicos.estadoCivil = {
								codigo: d.abaDadosBasicos.estadoCivil,
							};
						}
						if (d.abaDadosBasicos.rgUf) {
							d.abaDadosBasicos.rgUf = {
								id: d.abaDadosBasicos.rgUf,
							};
						}

						// atualizando os select fields sesi-ce com dados do backend e exibindo na tela
						if (d.abaDadosBasicos.necessidadesEspeciaisSesiCe) {
							d.abaDadosBasicos.necessidadesEspeciaisSesiCe = {
								id: d.abaDadosBasicos.necessidadesEspeciaisSesiCe,
							};
						}
						if (d.abaDadosBasicos.statusMatriculaSesiCe) {
							d.abaDadosBasicos.statusMatriculaSesiCe = {
								id: d.abaDadosBasicos.statusMatriculaSesiCe,
							};
						}
						///// CARREGANDO CONFIG EMPRESA FORNECEDOR NA TELA
						if (d.abaDadosBasicos.empresaFornecedor) {
							d.abaDadosBasicos.empresaFornecedor = {
								codigo: d.abaDadosBasicos.empresaFornecedor,
								razaoSocial: d.abaDadosBasicos.razaoSocialEmpresaSesiCe,
							};
						}

						if (
							d.abaDadosBasicos.telefone &&
							d.abaDadosBasicos.telefone.value &&
							Array.isArray(d.abaDadosBasicos.telefone.value)
						) {
							d.abaDadosBasicos.telefone.value.forEach(
								(v) => (v.descricaoTelefone = v.descricao)
							);
						}
						if (d.abaDadosBasicos.dataCadastro) {
							d.abaAcessoCatraca.dataCadastro = d.abaDadosBasicos.dataCadastro;
						}
					}
					if (d.abaFamiliares) {
						if (d.abaFamiliares.familiares) {
							d.abaFamiliares.familiares.forEach((f) => {
								f.familiar = {
									familiar: f.familiar || f.cliente,
									nome: f.nome,
									codAcesso: f.codAcesso,
								};
							});
						}
					}
					if (d.abaDependentes) {
						if (d.abaDependentes.dependentes) {
							d.abaDependentes.dependentes.forEach((f) => {
								f.dependente = {
									codigo: f.codigo,
									dataLancamento: f.dataLancamento,
									clienteTitular: f.clienteTitular,
									cliente: f.cliente,
									codigoCliente: f.codigoCliente,
									nome: f.cliente,
								};
							});
						}
					}
					this.form.patchValue({
						...d,
						estado: { id: d.estado },
						cidade: { id: d.cidade },
						pais: { id: d.pais },
					});
				}),
				switchMap((response) => {
					return this.msAdmCoreService.dadosPessoais(this.matricula);
				}),
				map(
					(dadosPessoais: ClienteDadosPessoais) =>
						(this.dadosPessoais = dadosPessoais)
				)
			)
			.subscribe((d) => {
				this.loadConfiguracoesCampos();
				this.loading = false;
				this.loaderService.stopForce();
				// console.log(this.dadosPessoais);
				this.cobrancaService.definirTitleTreino(this.dadosPessoais.nome);
				this.cd.detectChanges();
			});

		this.form
			.get("abaDadosBasicos.tipoDePessoa")
			.valueChanges.subscribe((responseTipoUsuario) => {
				this.configClienteFormValidation.verifyFormsToBeLoaded(this.form);
				this.cd.detectChanges();
			});
		this.loadPermissoes();
	}

	ngOnDestroy(): void {
		this.form.reset();
		this.snotify.clear();
		this.cobrancaService.definirTitleTreino("Sistema Pacto");
	}

	ngAfterViewInit() {
		this.ativarAba(this.route.snapshot.queryParams["aba"] || 0);
		this.fecharMenu();
	}

	fecharMenu() {
		try {
			const menu = document.getElementById("sidebar-menu-toggle");
			if (menu.classList.contains("opened")) {
				menu.click();
			}
		} catch (e) {
			console.log(e);
		}
	}

	camelize(str) {
		return str
			.replace(/(?:^\w|[A-Z]|\b\w)/g, function (word, index) {
				return index === 0 ? word.toLowerCase() : word.toUpperCase();
			})
			.replace(/\s+/g, "");
	}

	loadPermissoes() {}

	convertDateStringToMilliseconds(dateString: string): number {
		const months = [
			"Jan",
			"Feb",
			"Mar",
			"Apr",
			"May",
			"Jun",
			"Jul",
			"Aug",
			"Sep",
			"Oct",
			"Nov",
			"Dec",
		];

		const parts = dateString.split(" ");
		const monthIndex = months.indexOf(parts[1]);
		const year = parseInt(parts[5], 10);
		const month = monthIndex >= 0 ? monthIndex : 0;
		const day = parseInt(parts[2], 10);
		const timeParts = parts[3].split(":");
		const hours = parseInt(timeParts[0], 10);
		const minutes = parseInt(timeParts[1], 10);
		const seconds = parseInt(timeParts[2], 10);

		return new Date(year, month, day, hours, minutes, seconds).getTime();
	}

	saveConfig(): void {
		const data = this.form.getRawValue();
		const regex = new RegExp(
			'\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/"\\+]\\d*'
		);
		if (data && data.abaDadosBasicos && regex.test(data.abaDadosBasicos.nome)) {
			this.snotify.error(
				this.traducoes.getLabel("nome-caracter-especial"),
				undefined,
				{
					timeout: 2000,
				}
			);
			return;
		}
		const invalidTabs = [];
		let abaAtivar = 0;

		this.verifyFieldsEstrangeiraTipoPessoa(data.abaDadosBasicos.tipoPessoa);

		let listErrors = [];
		Object.keys(data).forEach((key) => {
			if (!this.form.get(key).valid) {
				listErrors[key] = [];
				this.showErrorMessages = true;
				this.form.get(key).markAllAsTouched();
				invalidTabs.push(key);
				const form = this.form.get(key) as FormGroup;
				Object.keys(form.controls).forEach((control) => {
					if (form.get(control).invalid) {
						listErrors[key].push(control);
					}
				});
				if (form.contains("email") && form.get("email").invalid) {
					this.snotify.error(
						this.traducoes.getLabel("cli-email-obrigatorio"),
						undefined,
						{
							timeout: 2000,
						}
					);
					if (!invalidTabs.includes("abaDadosBasicos")) {
						invalidTabs.push("abaDadosBasicos");
					}
				}
				if (form.contains("enderecos") && form.get("enderecos").invalid) {
					this.snotify.error(
						this.traducoes.getLabel("cli-enderecos-obrigatorio"),
						undefined,
						{
							timeout: 2000,
						}
					);

					if (!invalidTabs.includes("abaDadosBasicos")) {
						invalidTabs.push("abaDadosBasicos");
					}
				}
				if (form.contains("endereco") && form.get("endereco").invalid) {
					this.snotify.error(
						this.traducoes.getLabel("cli-endereco-obrigatorio"),
						undefined,
						{
							timeout: 2000,
						}
					);

					if (!invalidTabs.includes("abaDadosBasicos")) {
						invalidTabs.push("abaDadosBasicos");
					}
				}
				if (form.contains("telefone") && form.get("telefone").invalid) {
					this.snotify.error(
						this.traducoes.getLabel("cli-telefone-obrigatorio"),
						undefined,
						{
							timeout: 2000,
						}
					);

					if (!invalidTabs.includes("abaDadosBasicos")) {
						invalidTabs.push("abaDadosBasicos");
					}
				}
				console.error(listErrors);
			}
		});
		const abaDadosBasicos = this.form.get("abaDadosBasicos") as FormGroup;
		if (abaDadosBasicos) {
			const enderecos = this.form
				.get("abaDadosBasicos")
				.get("enderecos") as FormArray;
			if (
				(this.configClienteFormValidation.configForms["endereco"].obrigatorio ||
					this.configClienteFormValidation.configForms["cep"].obrigatorio ||
					this.configClienteFormValidation.configForms["bairro"].obrigatorio ||
					this.configClienteFormValidation.configForms["complemento"]
						.obrigatorio ||
					this.configClienteFormValidation.configForms["numero"].obrigatorio) &&
				enderecos.length === 0
			) {
				enderecos.setValidators([Validators.required]);
				enderecos.updateValueAndValidity();
				enderecos.markAllAsTouched();
				this.showErrorMessages = true;
				this.snotify.error(
					this.traducoes.getLabel("cli-nenhum-endereco-informado"),
					undefined,
					{
						timeout: 2000,
					}
				);
				if (!invalidTabs.includes("abaDadosBasicos")) {
					invalidTabs.push("abaDadosBasicos");
				}
			}

			const telefones = this.form
				.get("abaDadosBasicos")
				.get("telefone") as FormArray;
			if (
				(this.configClienteFormValidation.configForms["telefone"].obrigatorio ||
					this.configClienteFormValidation.configForms["descricaoTelefone"]
						.obrigatorio) &&
				telefones &&
				telefones.value &&
				telefones.value.length === 0
			) {
				telefones.setValidators([Validators.required]);
				telefones.updateValueAndValidity();
				telefones.markAllAsTouched();
				this.showErrorMessages = true;
				this.snotify.error(
					this.traducoes.getLabel("cli-telefone-obrigatorio"),
					undefined,
					{
						timeout: 2000,
					}
				);
				if (!invalidTabs.includes("abaDadosBasicos")) {
					invalidTabs.push("abaDadosBasicos");
				}
			}
			if (abaDadosBasicos.get("tipoPessoa").value == 2) {
				const cpfValue = abaDadosBasicos.get("cpf").value;
				const passaporteValue = abaDadosBasicos.get("passaporte").value;
				const rneValue = abaDadosBasicos.get("rne").value;

				if (!cpfValue && !passaporteValue && !rneValue) {
					this.snotify.error(
						this.traducoes.getLabel("cli-cpf-rne-passport-um-obrigatorio"),
						undefined,
						{
							timeout: 2000,
						}
					);
					invalidTabs.push("abaDadosBasicos");
					this.ativarAba(ABA.DADOS_BASICOS);
					return;
				}
			}
		}
		if (invalidTabs.length > 0) {
			if (invalidTabs.includes("abaDadosBasicos")) {
				abaAtivar = ABA.DADOS_BASICOS;
			} else if (invalidTabs.includes("abaAcessoCatraca")) {
				abaAtivar = ABA.ACESSO_A_CATRACA;
			} else if (invalidTabs.includes("abaDadosFinanceiros")) {
				abaAtivar = ABA.DADOS_FINANCEIROS;
			} else if (invalidTabs.includes("abaFamiliares")) {
				abaAtivar = ABA.FAMILIARES;
			} else if (invalidTabs.includes("abaDependentes")) {
				abaAtivar = ABA.DEPENDENTES;
			} else if (invalidTabs.includes("abaVinculos")) {
				abaAtivar = ABA.VINCULOS;
			} else if (invalidTabs.includes("abaRh")) {
				abaAtivar = ABA.RH;
			} else if (invalidTabs.includes("abaAcesso")) {
				abaAtivar = ABA.ACESSO;
			} else if (invalidTabs.includes("abaReplicarEmpresa")) {
				abaAtivar = ABA.REPLICAR_EMPRESA;
			}
			this.ativarAba(abaAtivar);
			document.querySelector("main").scrollTop = 0;
			return;
		}
		this.cd.detectChanges();

		if (this.form.invalid) {
			return;
		}
		this.showErrorMessages = false;

		if (data.abaVinculos) {
			if (
				!data.abaVinculos.vinculos ||
				data.abaVinculos.vinculos.length === 0
			) {
				this.snotify.error(
					this.traducoes.getLabel("cli-sem-vinculo-consultor"),
					undefined,
					{
						timeout: 2000,
					}
				);
				return;
			}
			const consultores = data.abaVinculos.vinculos.filter(
				(v) => v.tipoVinculo === "CO" || v.tipoVinculo.codigo === "CO"
			);
			if (consultores.length == 0) {
				this.snotify.error(
					this.traducoes.getLabel("cli-sem-vinculo-consultor"),
					undefined,
					{
						timeout: 2000,
					}
				);
				return;
			}
			data.abaVinculos.vinculos.forEach((v) => {
				if (typeof v.tipoVinculo === "object") {
					v.tipoVinculo = v.tipoVinculo.codigo;
				}
				if (typeof v.tipoVinculo === "object") {
					v.tipoVinculo = v.tipoVinculo.codigo;
				}
				if (typeof v.nomeColaborador === "object") {
					v.colaborador = v.nomeColaborador.codigo;
					v.nomeColaborador = v.nomeColaborador.nome;
				}
			});
		}

		if (data.abaFamiliares) {
			if (data.abaFamiliares.familiares) {
				if (Array.isArray(data.abaFamiliares.familiares)) {
					data.abaFamiliares.familiares.forEach((f) => {
						if (typeof f.familiar === "object") {
							f.familiar = f.familiar.familiar;
						}
					});
				}
			}
		}

		if (data.abaDependentes) {
			if (data.abaDependentes.dependentes) {
				if (Array.isArray(data.abaDependentes.dependentes)) {
					data.abaDependentes.dependentes.forEach((f) => {
						// if (typeof f.familiar === 'object') {
						// 	f.familiar = f.familiar.familiar
						// }
					});
				}
			}
		}

		if (data.abaDadosBasicos) {
			//// ATUALIZANDO CONFIG EMPRESA FORNECEDOR
			if (
				data.abaDadosBasicos.empresaFornecedor &&
				data.abaDadosBasicos.empresaFornecedor.codigo !== null
			) {
				data.abaDadosBasicos.razaoSocialEmpresaSesiCe =
					data.abaDadosBasicos.empresaFornecedor.razaoSocial;
				data.abaDadosBasicos.empresaFornecedor =
					data.abaDadosBasicos.empresaFornecedor.codigo;
			} else {
				data.abaDadosBasicos.razaoSocialEmpresaSesiCe = null;
				data.abaDadosBasicos.empresaFornecedor = null;
			}
			data.abaDadosBasicos.enderecos.forEach((v) => {
				v.tipoEndereco =
					v.tipoEndereco && v.tipoEndereco.codigo
						? v.tipoEndereco.codigo
						: null;
			});
		}

		let hasInvalidPhones = false;
		data.abaDadosBasicos.telefone.forEach((item, index) => {
			let telefone = item.telefone || item.numero;
			if (telefone) {
				telefone = telefone.replace("_", "");
				if (item.telefone) {
					item.numero = item.telefone = telefone;
				}
				if (!item.tipoTelefone) {
					this.snotify.error(
						`Tipo de telefone não informado para ${telefone}`,
						undefined,
						{
							timeout: 2000,
						}
					);
					hasInvalidPhones = true;
					return;
				}

				if (typeof item.tipoTelefone === "object") {
					item.tipoTelefone = item.tipoTelefone.codigo;
				}
			}
		});

		if (hasInvalidPhones) {
			return;
		}

		const formatarCamposSesiCe = (campo: string) => {
			switch (campo) {
				case "razaoSocialEmpresaSesiCe":
					return data.abaDadosBasicos[campo].toUpperCase().trim();
				case "statusMatriculaSesiCe":
					return data.abaDadosBasicos[campo].id;
				case "necessidadesEspeciaisSesiCe":
					return data.abaDadosBasicos[campo].id;
				case "dataValidadeCadastroSesiCe":
					const dtValidadeCadastro =
						data.abaDadosBasicos.dataValidadeCadastroSesiCe;
					if (typeof dtValidadeCadastro === "number") {
						return moment(new Date(dtValidadeCadastro)).format("YYYY-MM-DD");
					} else {
						return dtValidadeCadastro;
					}

				default:
					break;
			}
		};

		const jsonData = {
			...data,
			abaDadosBasicos: {
				...data.abaDadosBasicos,
				dataNasc:
					typeof data.abaDadosBasicos.dataNasc === "number"
						? moment(new Date(data.abaDadosBasicos.dataNasc)).format(
								"YYYY-MM-DD"
						  )
						: data.abaDadosBasicos.dataNasc,
				dataNascimentoResponsavel:
					typeof data.abaDadosBasicos.dataNascimentoResponsavel === "number"
						? moment(
								new Date(data.abaDadosBasicos.dataNascimentoResponsavel)
						  ).format("YYYY-MM-DD")
						: data.abaDadosBasicos.dataNascimentoResponsavel,
				grupoClientes: Array.isArray(data.abaDadosBasicos.grupoClientes)
					? data.abaDadosBasicos.grupoClientes.map((i) => ({
							...i,
							cliente: this.codigoCliente,
					  }))
					: [],
				classificacaoCliente: Array.isArray(
					data.abaDadosBasicos.classificacaoCliente
				)
					? data.abaDadosBasicos.classificacaoCliente.map((i) => ({
							...i,
							cliente: this.codigoCliente,
					  }))
					: [],
				dataCadastro: moment(new Date()).format("YYYY-MM-DD"),
				// ---------campos sesi-ce
				dataValidadeCadastroSesiCe: formatarCamposSesiCe(
					"dataValidadeCadastroSesiCe"
				),
				necessidadesEspeciaisSesiCe: formatarCamposSesiCe(
					"necessidadesEspeciaisSesiCe"
				),
				statusMatriculaSesiCe: formatarCamposSesiCe("statusMatriculaSesiCe"),

				// ----------------------
				pais:
					data.abaDadosBasicos.pais && data.abaDadosBasicos.pais.codigo
						? data.abaDadosBasicos.pais
						: null,
				cidade:
					data.abaDadosBasicos.cidade && data.abaDadosBasicos.cidade.codigo
						? data.abaDadosBasicos.cidade
						: null,
				estado:
					data.abaDadosBasicos.estado && data.abaDadosBasicos.estado.codigo
						? data.abaDadosBasicos.estado
						: null,
				profissao:
					data.abaDadosBasicos.profissao &&
					data.abaDadosBasicos.profissao.codigo
						? data.abaDadosBasicos.profissao
						: null,
				rgUf:
					data.abaDadosBasicos.rgUf && data.abaDadosBasicos.rgUf.id
						? data.abaDadosBasicos.rgUf.id
						: null,
				grauInstrucao:
					data.abaDadosBasicos.grauInstrucao &&
					data.abaDadosBasicos.grauInstrucao.codigo
						? data.abaDadosBasicos.grauInstrucao
						: null,
				endereco: data.abaDadosBasicos.enderecos,
				tipoDePessoa: this.form.get("abaDadosBasicos").get("tipoDePessoa")
					.value,
				sexo: data.abaDadosBasicos.sexo
					? data.abaDadosBasicos.sexo.codigo
					: null,
				genero: data.abaDadosBasicos.genero
					? data.abaDadosBasicos.genero.codigo
					: null,
				estadoCivil: data.abaDadosBasicos.estadoCivil
					? data.abaDadosBasicos.estadoCivil.codigo
					: null,
				categoria:
					data.abaDadosBasicos.categoria &&
					data.abaDadosBasicos.categoria.codigo
						? data.abaDadosBasicos.categoria
						: null,
				cnpjClienteSesi: data.abaDadosBasicos.cnpjClienteSesi,
				situacaoCliente: this.dadosPessoais.situacao,
			},
			abaAcessoCatraca: {
				...data.abaAcessoCatraca,
			},
		};

		delete jsonData.abaDadosBasicos.enderecos;
		if (jsonData.abaDadosBasicos.emails) {
			jsonData.abaDadosBasicos.emails.forEach((email) => {
				if (typeof email.pessoa === "object") {
					email.pessoa = email.pessoa.codigo;
				}
			});
		}
		this.pessoaService.postConfigData(jsonData).subscribe(
			(response) => {
				this.snotify.success("Configurações salvas com sucesso.");
				setTimeout(() => {
					this.router.navigate(
						[this.form.get("abaAcessoCatraca").get("matricula").value],
						{
							relativeTo: this.route.parent,
						}
					);
				}, 1500);
			},
			(error) => {
				if (error.meta && error.meta.messageValue) {
					this.notifyRequestError(error.meta.messageValue);
				} else {
					this.notifyRequestError(error);
				}
			}
		);
	}

	permiteEditarCliente(): any {
		const permition = this.recursoCliente;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		return isPermited;
	}

	private ativarAba(aba: number | string) {
		if (typeof aba === "string") {
			aba = ABA[aba];
			if (
				(!this.recursoFamiliar && aba === ABA.FAMILIARES) ||
				(!this.recursoVinculo && aba === ABA.VINCULOS)
			) {
				aba = 0;
			}
		}
		if (aba !== undefined && typeof aba === "number") {
			if (!this.recursoFamiliar) {
				if (aba !== 0) {
					aba -= 1;
				}
			}
			if (!this.recursoVinculo) {
				if (aba !== 0) {
					aba -= 1;
				}
			}
			this.tabs.tabIndex = aba;
			this.cd.detectChanges();
		}
	}

	private loadConfiguracoesCampos() {
		this.pessoaService
			.getConfiguracoesFormulario()
			.subscribe((responseFormularioConfig) => {
				this.configClienteFormValidation.configuracoesCampoCliente =
					responseFormularioConfig.configuracaoSistemaCadastroClienteDTO.filter(
						(v) => !v.visitante
					);
				this.configClienteFormValidation.configuracoesCampoVisitante =
					responseFormularioConfig.configuracaoSistemaCadastroClienteDTO.filter(
						(v) => v.visitante
					);
				this.configClienteFormValidation.configuracoesCampoColaborador =
					responseFormularioConfig.cadastroDinamicoItemDTO.filter(
						(v) => v.mostrarCampo
					);
				// console.log(this.dadosPessoais)
				this.configClienteFormValidation.situacaoCliente =
					this.dadosPessoais.situacao;
				this.configClienteFormValidation.verifyFormsToBeLoaded(this.form);
				this.configClienteFormValidation.updated(true);
				const enderecos = this.form
					.get("abaDadosBasicos")
					.get("enderecos") as FormArray;
				if (enderecos && enderecos.controls) {
					enderecos.controls.forEach((form: FormGroup) =>
						this.configClienteFormValidation.verifyFormsToBeLoaded(
							form,
							this.form.get("abaDadosBasicos")
						)
					);
				}
				this.verifyFieldsEstrangeiraTipoPessoa(
					this.form.get("abaDadosBasicos").get("tipoPessoa").value
				);
				this.cd.detectChanges();
			});
	}

	private verifyFieldsEstrangeiraTipoPessoa(tipoPessoa) {
		if (tipoPessoa === 0 || tipoPessoa === 2) {
			// 0 PF 1 PJ 2 estrangeira
			this.form.get("abaDadosBasicos").get("cnpj").clearValidators();
			if (tipoPessoa === 2) {
				this.form.get("abaDadosBasicos").get("cpf").clearValidators();
				this.form.get("abaDadosBasicos").get("rg").clearValidators();
			} else {
				this.configClienteFormValidation.verifyForm(this.form, "cpf");
				this.configClienteFormValidation.verifyForm(this.form, "rg");
			}
		} else {
			this.configClienteFormValidation.verifyForm(this.form, "cnpj");
			this.form.get("abaDadosBasicos").get("cpf").clearValidators();
			this.form.get("abaDadosBasicos").get("rg").clearValidators();
		}
		this.form.get("abaDadosBasicos").get("cpf").markAsTouched();
		this.form.get("abaDadosBasicos").get("rg").markAsTouched();
		this.form.get("abaDadosBasicos").get("cnpj").markAsTouched();
		this.form.get("abaDadosBasicos").get("cnpj").updateValueAndValidity();
		this.form.get("abaDadosBasicos").get("cpf").updateValueAndValidity();
		this.form.get("abaDadosBasicos").get("rg").updateValueAndValidity();
	}

	private notifyRequestError(error) {
		if (error.error) {
			const meta = error.error.meta;
			if (meta.messageID) {
				this.snotify.error(this.traducoes.getLabel(meta.messageID), undefined, {
					timeout: 2000,
				});
				return;
			}
			if (meta.messageValue) {
				this.snotify.error(meta.messageValue, undefined, {
					timeout: 2000,
				});
				return;
			}
			if (meta.message) {
				this.snotify.error(meta.message, undefined, {
					timeout: 2000,
				});
				return;
			}
			this.snotify.error(this.traducoes.getLabel("server-error"), undefined, {
				timeout: 2000,
			});
		}
	}
}
