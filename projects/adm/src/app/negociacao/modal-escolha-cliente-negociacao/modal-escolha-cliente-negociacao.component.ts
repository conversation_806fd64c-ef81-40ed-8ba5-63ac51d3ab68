import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";
import { debounceTime } from "rxjs/operators";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "adm-modal-escolha-cliente-negociacao",
	templateUrl: "./modal-escolha-cliente-negociacao.component.html",
	styleUrls: ["./modal-escolha-cliente-negociacao.component.scss"],
})
export class ModalEscolhaClienteNegociacaoComponent implements OnInit {
	clienteFc = new FormControl("");
	clientes = Array<any>();

	constructor(
		private service: AdmCoreApiNegociacaoService,
		private dialog: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.clienteFc.valueChanges.pipe(debounceTime(300)).subscribe((v) => {
			this.service.clientes(v).subscribe((result) => {
				this.clientes = result;
				this.cd.detectChanges();
			});
		});
	}

	escolher(cliente) {
		this.dialog.close(cliente);
	}
}
