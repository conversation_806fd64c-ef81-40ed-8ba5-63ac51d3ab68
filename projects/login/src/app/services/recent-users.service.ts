import { Injectable } from "@angular/core";
import { CookieService } from "ngx-cookie-service";
import { environment } from "../../environments/environment";
import { LoginAppApiDataUserService } from "login-app-api";
import { DomSanitizer, SafeStyle } from "@angular/platform-browser";
import * as CryptoJS from "crypto-js";

export interface RecentUsers {
	id: string;
	email: string;
	name: string;
	photoUrl: string;
	celular: string;
}

@Injectable({
	providedIn: "root",
})
export class RecentUsersService {
	constructor(
		private loginAppDataUser: LoginAppApiDataUserService,
		private domSanitizer: DomSanitizer,
		private cookieService: CookieService
	) {}

	getRecentUsers(): Array<RecentUsers> {
		if (this.cookieService.check(environment.coockieCredentials)) {
			return JSON.parse(
				CryptoJS.AES.decrypt(
					this.cookieService.get(environment.coockieCredentials),
					environment.cookieCredentialsPsw
				).toString(CryptoJS.enc.Utf8)
			);
		}
	}

	updateRecentUsers() {
		const userData = this.loginAppDataUser.userData;
		const userInfo: RecentUsers = {
			id: userData.id,
			email: userData.email,
			name: userData.nome.toLowerCase(),
			photoUrl: userData.fotokey,
			celular: userData.telefone ? userData.telefone.split("|")[1] : "",
		};
		let recentUsers: Array<RecentUsers> = this.getRecentUsers();
		if (!recentUsers) {
			recentUsers = new Array<RecentUsers>();
		}
		const recentUserIndex = recentUsers.findIndex((r) => r.id === userInfo.id);
		if (recentUserIndex === -1) {
			recentUsers.push(userInfo);
		} else {
			recentUsers[recentUserIndex] = userInfo;
		}
		const encriptedUser = CryptoJS.AES.encrypt(
			JSON.stringify(recentUsers),
			environment.cookieCredentialsPsw
		);
		this.cookieService.set(
			environment.coockieCredentials,
			encriptedUser,
			24 * 60 * 60,
			undefined,
			undefined,
			true
		);
	}
}
