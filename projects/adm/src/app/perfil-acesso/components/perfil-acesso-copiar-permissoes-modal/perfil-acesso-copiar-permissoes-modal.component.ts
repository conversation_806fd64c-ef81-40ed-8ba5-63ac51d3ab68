import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PerfilAcessoUnificadoService } from "adm-ms-api";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "pacto-perfil-acesso-copiar-permissoes-modal",
	templateUrl: "./perfil-acesso-copiar-permissoes-modal.component.html",
	styleUrls: ["./perfil-acesso-copiar-permissoes-modal.component.scss"],
})
export class PerfilAcessoCopiarPermissoesModalComponent implements OnInit {
	@ViewChild("notificacao", { static: true })
	notificacao: TraducoesXinglingComponent;

	@Output()
	codigo: EventEmitter<number> = new EventEmitter<number>();

	form = new FormGroup({
		listaPerfil: new FormControl(null, Validators.required),
	});

	constructor(
		private openModal: NgbActiveModal,
		private rest: AdmRestService,
		private perfilService: PerfilAcessoUnificadoService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	listarPerfis: Array<any> = [];

	buscarPerfis() {
		return this.perfilService.listarPerfis().subscribe(
			(response) => {
				this.listarPerfis = response.content;
				this.cd.detectChanges();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	ngOnInit() {
		this.buscarPerfis();
	}

	dismiss() {
		this.openModal.dismiss();
	}

	copiarPermissoes() {
		const codigo = this.form.get("listaPerfil").value;

		this.codigo.emit(codigo);
		this.openModal.dismiss();
	}
}
