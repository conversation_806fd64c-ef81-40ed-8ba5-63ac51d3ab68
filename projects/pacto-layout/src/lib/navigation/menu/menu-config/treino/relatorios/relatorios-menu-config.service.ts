import { Injectable } from "@angular/core";
import { MenuConfigService } from "../../menu-config.service";
import { Observable, of } from "rxjs";
import { PlatformMenuItem } from "../../../../models";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PermissaoService } from "../../../../permissao/permissao.service";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";

@Injectable({
	providedIn: "root",
})
export class RelatoriosMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		protected permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.relatorioParentItem]);
	}

	get relatorioParentItem(): PlatformMenuItem {
		const permitido: boolean = this.permissaoService.temFuncionalidadeTreino(
			PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
		);
		const menu: PlatformMenuItem = {
			id: "relatorios",
			inativo: !permitido,
			configMenuSidebar: {
				submenus: [
					this.professoresAlunosAvisoMedicoMenuItem,
					this.andamentoProgramasMenuItem,
					this.execucoesTreinoMenuItem,
					this.alunosAppMenuItem,
					this.atividadeProfessoresMenuItem,
					this.carteiraMenuItem,
					this.gestaoCredito,
					this.professoresSubstituidosMenuItem,
					this.alunosFaltososMenuItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.professoresAlunosAvisoMedicoMenuItem,
					this.andamentoProgramasMenuItem,
					this.execucoesTreinoMenuItem,
					this.alunosAppMenuItem,
					this.atividadeProfessoresMenuItem,
					this.carteiraMenuItem,
					this.gestaoCredito,
					this.professoresSubstituidosMenuItem,
					this.alunosFaltososMenuItem,
				],
			},
		};

		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get alunosAppMenuItem(): PlatformMenuItem {
		return {
			id: "alunosapp",
			funcionalidadeTreino: PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido: this.permissaoService.temFuncionalidadeTreino(
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
			),
			favoriteIdentifier: "TREINO_APLICATIVOS_ATIVOS",
			route: {
				internalLink: "/treino/bi/alunos-app",
			},
		};
	}

	get carteiraMenuItem(): PlatformMenuItem {
		return {
			id: "carteira",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_CARTEIRA",
			route: {
				internalLink: "/treino/gestao/carteira-professores",
			},
		};
	}

	get atividadeProfessoresMenuItem(): PlatformMenuItem {
		return {
			id: "atividade-professores",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_ATIVIDADES_PROFESSORES",
			route: {
				internalLink: "/treino/gestao/atividade-professores",
			},
		};
	}

	get professoresAlunosAvisoMedicoMenuItem(): PlatformMenuItem {
		return {
			id: "professores-alunos-aviso-medico",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_PROFESSORES_ALUNOS_AVISO_MEDICO",
			route: {
				internalLink: "/treino/gestao/professores-alunos-aviso-medico",
			},
		};
	}

	get professoresSubstituidosMenuItem(): PlatformMenuItem {
		return {
			id: "professores-substituidos",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_PROFESSORES_SUBSTITUIDOS",
			route: {
				internalLink: "/treino/gestao/professores-substituidos",
			},
		};
	}

	get andamentoProgramasMenuItem(): PlatformMenuItem {
		return {
			id: "andamento-programas",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_ANDAMENTO",
			route: {
				internalLink: "/treino/gestao/andamento-programas",
			},
		};
	}

	get execucoesTreinoMenuItem(): PlatformMenuItem {
		return {
			id: "execucoes-treino",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "EXECUCOES_TREINO",
			route: {
				internalLink: "/treino/gestao/execucoes-treino",
			},
		};
	}

	get personaisMenuItem(): PlatformMenuItem {
		return {
			id: "relatorio-personais",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.GESTAO_PERSONAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.GESTAO_PERSONAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_PERSONAL",
			route: {
				internalLink: "/treino/gestao/personais",
			},
		};
	}

	get gestaoCredito(): PlatformMenuItem {
		return {
			id: "relatorio-gestao-credito",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.GESTAO_CREDITOS_PERSONAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.GESTAO_CREDITOS_PERSONAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_GESTAO_CREDITOS",
			route: {
				internalLink: "/treino/gestao/gestao-credito",
			},
		};
	}

	get alunosFaltososMenuItem(): PlatformMenuItem {
		return {
			id: "alunos-faltosos",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL &&
				PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_GESTAO_GERAL
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO
				),
			favoriteIdentifier: "TREINO_PROFESSORES_ALUNOS_FALTOSOS",
			route: {
				internalLink: "/treino/gestao/alunos-faltosos",
			},
		};
	}
}
