@import "dist/ui-kit/assets/import.scss";
@import "dist/ui-kit/assets/ds3/ds3.scss";

$titulo: #494b50;

.alunos {
	display: flex;
	height: 100%;
	flex-direction: column;

	.alunos-title {
		@extend .pct-title5;
		color: $titulo;
	}

	.content {
		display: flex;
		flex-wrap: nowrap;
		flex-direction: column;
		padding: 16px 0;
		gap: 8px;

		.aluno {
			.values {
				.nome {
					text-transform: capitalize;
				}
			}

			display: flex;
			align-items: center;
			flex-direction: row;

			.timestamp {
				@extend .pct-overline2-regular;
				padding-right: 4px;
				width: 42px;
				color: var(--color-action-default-disabled-2);
			}

			.entry {
				width: 100%;
				border-radius: 8px;
				border: 1px solid var(--color-support-gray-3);
				background-color: var(--color-background-plane-3);
				display: flex;
				align-items: center;
				flex-wrap: nowrap;
				flex-direction: row;

				.bar {
					margin: 8px;
					width: 6px;
					border-radius: 8px;
					height: 82px;
				}

				.data {
					display: flex;
					flex-wrap: nowrap;
					flex-direction: column;
					width: 100%;
					justify-content: space-between;

					.texts {
						display: flex;
						width: 100%;
						flex-direction: row;
						justify-content: space-around;

						.avatar {
							display: flex;
							width: 10%;
							justify-content: center;
							align-items: center;

							img {
								height: 32px;
								width: 32px;
								border-radius: 50%;
							}
						}

						.values {
							display: flex;
							padding-left: 6px;
							flex-direction: column;
							align-items: flex-start;
							width: 60%;

							a {
								padding: 0px;
							}

							span {
								@extend .pct-overline2-regular;
								color: var(--color-support-black-3);

								strong {
									@extend .pct-overline2-bold;
									color: var(--color-support-black-3);
								}
							}
						}

						.chips {
							width: 30%;
							display: flex;
							justify-content: flex-end;
							padding: 1px 8px;

							ds3-status {
								margin: 6px;
							}
						}
					}

					.status {
						.info {
							@extend .pct-body2;
							color: $titulo;
						}

						.aniversariante {
							@extend .pct-body2-bold;
							color: $titulo;
						}
					}
				}
			}
		}
	}

	.actions {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: row;
	}
}

.acesso-mais-info {
	color: var(--color-action-default-able-4);
	padding-left: 8px;
	//styleName: pct - button/Default/1;
	font-family: Poppins;
	font-size: 14px;
	font-weight: 600;
	line-height: 14px;
	letter-spacing: 0.25px;
	text-align: left;
}

.emptyState {
	width: 100%;
	display: flex;
	height: 200px;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	img {
		padding-bottom: 20px;
		width: 104px;
		height: 104px;
	}

	.empty-title {
		@extend .pct-title4;
		color: #55585e;
		padding-bottom: 16px;
	}

	.empty-info {
		@extend .pct-body1;
		color: $titulo;
	}
}

.vermelho-acesso {
	background-color: #af0404;
}

.amarelo-acesso {
	background-color: #afaf04;
}

.azul-acesso {
	background-color: #b4cafd;
}

.sit-vi {
	color: #163e9c;
	background-color: #bccdf5;
}

.sit-at {
	color: #037d03;
	background-color: #b4fdb4;
}

.sit-in {
	color: #7d0303;
	background-color: #fdb4b4;
}

.sit-tr {
	color: #797d86;
	background-color: #e4e5e7;
}

.sit-no {
	color: #0a4326;
	background-color: #8fefbf;
}

.sit-di {
	color: #0a4326;
	background-color: #63e9a6;
}

.sit-pe {
	color: #0a4326;
	background-color: #1dc973;
}

.sit-av {
	color: #705810;
	background-color: #efd78f;
}

.sit-ve {
	color: #705810;
	background-color: #e9c763;
}

.sit-tv {
	color: #705810;
	background-color: #e2b736;
}

.sit-ca {
	color: #701028;
	background-color: #f5bcca;
}

.sit-de {
	color: #701028;
	background-color: #ef8fa7;
}

.sit-in {
	color: #701028;
	background-color: #e96384;
}

.sit-ae {
	color: #105870;
	background-color: #63c7e9;
}

.sit-cr {
	color: #105870;
	background-color: #8fd7ef;
}

.sit-gympass {
	color: #9c5316;
	background-color: #f5d6bc;
}

.sit-totalPass {
	color: #9c5316;
	background-color: #efba8f;
}

::ng-deep.banner_bg,
::ng-deep.banner_frente {
	border-radius: 8px;
}
