import { Injectable } from "@angular/core";

import { Observable, of } from "rxjs";
import { AulaFiltro } from "./aula-coletiva.model";
import {
	AlunoVinculoTurmaZWEnum,
	HorarioTurma,
	NivelTurma,
	TurmaCreateEdit,
	TurmaZW,
} from "./turma.model";
import {
	ApiResponseList,
	ApiResponseSingle,
	ResultadoPorDia,
} from "./base.model";
import { catchError, map } from "rxjs/operators";
import { AgendaView } from "./agenda.model";
import { TreinoApiBaseService } from "./treino-api-base.service";
import { TreinoApiModule } from "./treino-api.module";
import { AgendaMetaCards } from "./agenda-meta-card.model";
import { Ambiente } from "./aula.model";

export enum AcaoRequeridaMarcarAluno {
	AULA_EXPERIMENTAL = "AULA_EXPERIMENTAL",
	MARCACAO = "MARCACAO",
	REPOSICAO = "REPOSICAO",
	SELECIONAR_ALUNO = "SELECIONAR_ALUNO",
}

export enum StatusMarcarAlunoEnum {
	TURMA_CHEIA = "TURMA_CHEIA",
	ALUNO_SEM_CONTRATO = "ALUNO_SEM_CONTRATO",
	CONTRATO_FUTURO = "CONTRATO_FUTURO",
	DIARIA_CONTRATO_FUTURO = "DIARIA_CONTRATO_FUTURO",
	CREDITOS_PARA_MARCACAO = "CREDITOS_PARA_MARCACAO",
	CREDITOS_EXTRA = "CREDITOS_EXTRA",
	MARCAR_REPOSICAO = "MARCAR_REPOSICAO",
	AULA_EXPERIMENTAL = "AULA_EXPERIMENTAL",
	ALUNO_SEM_CADASTRO_ZW = "ALUNO_SEM_CADASTRO_ZW",
	ALUNO_TOTAL_PASS = "ALUNO_TOTALPASS",
	SUCESSO = "SUCESSO",
}

export interface DadosInserirAluno {
	matricula: number;
	dia: string;
	acao: AcaoRequeridaMarcarAluno;
	autorizado?: boolean;
	produtoId?: number;
	aulaDesmarcarId?: number;
	diaAulaDesmarcar?: number;
}

export interface ResponseMarcarAluno {
	status: StatusMarcarAlunoEnum;
	conteudo: any;
	messageID: string;
	messageValue: string;
}

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiTurmaService {
	constructor(private restService: TreinoApiBaseService) {}

	/**
	 *
	 * @param dia aaaammdd - 20190102
	 */
	obterTurmas(
		dia: string,
		periodo: AgendaView,
		filtro: AulaFiltro
	): Observable<ResultadoPorDia<TurmaZW>> {
		const params: any = {
			ref: dia,
			periodo,
		};
		if (filtro) {
			params.filtros = JSON.stringify(filtro);
		}
		return this.restService.get(`agenda/turmas/all`, { params }).pipe(
			map((response: ApiResponseSingle<ResultadoPorDia<TurmaZW>>) => {
				return response.content;
			})
		);
	}

	agendaCards(
		dia: string,
		periodo: AgendaView,
		filtro: AulaFiltro
	): Observable<AgendaMetaCards> {
		const params: any = {
			ref: dia,
			periodo,
		};
		if (filtro) {
			params.filtros = JSON.stringify(filtro);
		}
		return this.restService.get("agenda-cards", { params }).pipe(
			map((response: ApiResponseSingle<AgendaMetaCards>) => {
				return response.content;
			})
		);
	}

	confirmarPresencaTurma(
		horarioId: number,
		dia: string,
		matriculaAluno: number,
		codigoPassivo?: number,
		codigoIndicado?: number,
		alunoVinculoAula?: AlunoVinculoTurmaZWEnum
	): Observable<any> {
		const params: any = {
			dia,
		};
		if (matriculaAluno) {
			params.matricula = matriculaAluno;
		}
		if (codigoPassivo) {
			params.codigoPassivo = codigoPassivo;
		}
		if (codigoIndicado) {
			params.codigoIndicado = codigoIndicado;
		}
		if (alunoVinculoAula) {
			params.alunoVinculoAula = alunoVinculoAula;
		}
		return this.restService
			.put(`agenda/turmas/${horarioId}/confirmar-presenca`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	confirmarTodasPresencas(horioId: number, dia: string): Observable<any> {
		const params: any = {
			dia,
		};
		return this.restService
			.put(`agenda/turmas/${horioId}/confirmar-todas-presencas`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	aulaDetalhada(horarioId: number, dia: string): Observable<any> {
		const params: any = {
			dia,
			completo: true,
		};

		return this.restService
			.get(`agenda/turmas/${horarioId}/aula-detalhada`, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	atualizarRankingSelfloops(horarioId: number, dia: string): Observable<any> {
		const params: any = {
			dia,
			completo: true,
		};

		return this.restService
			.get(`agenda/selfloops/atividades/${horarioId}/${dia}`, { params })
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response;
				})
			);
	}

	aulaDetalhadaSemClientes(horarioId: number, dia: string): Observable<any> {
		const params: any = {
			dia,
			completo: false,
		};

		return this.restService
			.get(`agenda/turmas/${horarioId}/aula-detalhada`, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	desconfirmarPresencaTurma(
		horarioId: number,
		dia: string,
		matriculaAluno: number,
		alunoVinculoAula?: AlunoVinculoTurmaZWEnum
	): Observable<any> {
		const params: any = {
			dia,
			matricula: matriculaAluno,
		};
		if (alunoVinculoAula) {
			params.alunoVinculoAula = alunoVinculoAula;
		}
		return this.restService
			.put(`agenda/turmas/${horarioId}/desconfirmar-presenca`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	marcarAlunoTurma(
		horarioId: number,
		dadosMarcarAluno: DadosInserirAluno
	): Observable<ResponseMarcarAluno> {
		const params: any = dadosMarcarAluno;
		return this.restService
			.put(`agenda/turmas/${horarioId}/marcar-aluno`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<ResponseMarcarAluno>) => {
					return response.content;
				}),
				catchError((err) => {
					return of(err.error.meta);
				})
			);
	}

	desmarcarAlunoTurma(
		horarioId: number,
		dia: string,
		alunoVinculoAula: string,
		matriculaAluno: number,
		codigoPassivo: number,
		codigoIndicado: number,
		justificativa: string
	): Observable<any> {
		const params: any = {
			dia,
		};
		if (matriculaAluno) {
			params.matricula = matriculaAluno;
		}
		if (codigoPassivo) {
			params.codigoPassivo = codigoPassivo;
		}
		if (codigoIndicado) {
			params.codigoIndicado = codigoIndicado;
		}
		if (alunoVinculoAula) {
			params.alunoVinculoAula = alunoVinculoAula;
		}
		params.justificativa = justificativa;
		return this.restService
			.delete(`agenda/turmas/${horarioId}/desmarcar-aluno`, { params })
			.pipe(
				map((response: ApiResponseSingle<ResponseMarcarAluno>) => {
					return response.content;
				}),
				catchError((err) => {
					return of(err.error.meta.message);
				})
			);
	}

	substituirProfessor(
		horarioId: number,
		dia: string,
		novoProfessor: { professorId: number; justificativa: string }
	): Observable<any> {
		const params = { dia };
		return this.restService
			.put(`agenda/turmas/${horarioId}/substituir-professor`, novoProfessor, {
				params,
			})
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return true;
				})
			);
	}

	excluirAulaCheia(
		horarioId: number,
		dia: string,
		justificativa: string
	): Observable<boolean> {
		const params = {
			dia,
			justificativa,
		};
		return this.restService
			.delete(`agenda/turmas/${horarioId}`, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return true;
				})
			);
	}

	justificarFaltaAulaCheia(
		horarioId: number,
		dia: string,
		matriculaAluno: number,
		justificativa: string
	): Observable<any> {
		const params: any = {
			dia,
			matricula: matriculaAluno,
			justificativa,
		};
		return this.restService
			.put(`agenda/turmas/${horarioId}/justificativa`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	obterTurmasZWPorDia(
		matriculaAluno: number,
		dia: string
	): Observable<Array<any>> {
		const params: any = {
			data: dia,
		};
		return this.restService
			.get(`agenda/turmas-zw/aluno/${matriculaAluno}`, { params })
			.pipe(
				map((response: ApiResponseList<Array<any>>) => {
					return response.content;
				})
			);
	}

	obterProdutosZW(): Observable<Array<any>> {
		return this.restService.get(`agenda/produtos-zw/all`).pipe(
			map((response: ApiResponseList<Array<any>>) => {
				return response.content;
			})
		);
	}

	saveOrUpdateHorario(
		dados: HorarioTurma[]
	): Observable<{ erro?: string; retorno?: any }> {
		return this.restService.post("turmas/saveOrUpdateHorario", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.error });
			})
		);
	}

	criarHorarioTurma(
		dados: HorarioTurma
	): Observable<{ erro?: string; retorno?: any }> {
		return this.restService.post("turmas/saveHorario", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.error });
			})
		);
	}

	editarHorarioTurma(
		dados: HorarioTurma
	): Observable<{ erro?: string; retorno?: any }> {
		return this.restService.post("turmas/updateHorario", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.error });
			})
		);
	}

	criarTurma(
		dados: TurmaCreateEdit
	): Observable<{ erro?: string; retorno?: any }> {
		return this.restService.post("turmas/save", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.error });
			})
		);
	}

	editarTurma(
		dados: TurmaCreateEdit
	): Observable<{ erro?: string; retorno?: any }> {
		return this.restService.post("turmas/update", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.message });
			})
		);
	}

	syncTurmaMgb(codTurma: number): Observable<{ erro?: string; retorno?: any }> {
		return this.restService
			.post(`turmas/sync-turma-mgb/${codTurma}`, null)
			.pipe(
				map((response: any) => {
					return { retorno: response.content };
				}),
				catchError((error) => {
					return new Observable((observer) => {
						observer.error(error);
						observer.complete();
					});
				})
			);
	}

	obterTurma(id): Observable<TurmaCreateEdit> {
		return this.restService.get(`turmas/${id}`).pipe(
			map(
				(response: ApiResponseSingle<TurmaCreateEdit>) => {
					return response.content;
				},
				catchError((error) => {
					return of(null);
				})
			)
		);
	}

	criarAmbienteHorarioTurma(
		dados: Ambiente
	): Observable<{ erro?: string; message?: string; retorno?: any }> {
		return this.restService.post("turmas/saveAmbiente", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				const erro = error.error.meta.error;
				const message = error.error.meta.message;
				return of({ erro, message });
			})
		);
	}

	criarNivelTurmaHorarioTurma(
		dados: NivelTurma
	): Observable<{ erro?: string; retorno?: any }> {
		return this.restService.post("turmas/saveNivelTurma", dados).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.error });
			})
		);
	}

	removerHorarioTurma(id: number): Observable<string> {
		return this.restService.get(`turmas/horarioTurma/remover/${id}`).pipe(
			map(
				(response: ApiResponseSingle<string>) => {
					return response.content;
				},
				catchError((error) => {
					return of(null);
				})
			)
		);
	}

	validarHorarioTurma(id: number): Observable<string> {
		return this.restService.get(`turmas/horarioTurma/validar/${id}`).pipe(
			map(
				(response: ApiResponseSingle<string>) => {
					return response.content;
				},
				catchError((error) => {
					return of(null);
				})
			)
		);
	}

	fixarAlunoAula(
		horarioId: number,
		matricula: string,
		tipo: string,
		dataAula: string,
		dataFinal: string,
		validar: boolean
	): Observable<any> {
		const params = {
			dataFinal,
			dataAula,
			matricula,
			tipo,
			validarAulaCheia: validar.toString(),
		};
		return this.restService
			.put(`agenda/turmas/${horarioId}/fixar-aluno`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				}),
				catchError((err) => {
					return of({ erro: err });
				})
			);
	}

	desafixarAlunoAula(
		horarioId: number,
		matricula: string,
		dataDesafixar: string
	): Observable<any> {
		const params = {
			dataDesafixar,
			matricula,
		};
		return this.restService
			.put(`agenda/turmas/${horarioId}/desafixar-aluno`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				}),
				catchError((err) => {
					return of({ erro: err });
				})
			);
	}

	inserirNaFilaTurmaCrm(
		codigoHorarioTurma: number,
		codigoAluno: number,
		passivo: any,
		codigoPassivo: number
	): Observable<{ erro?: string; retorno?: any }> {
		let url = `aulas/inserirNaFilaTurmaCrm?codigoHorarioTurma=${codigoHorarioTurma}`;
		if (codigoAluno) {
			url = url.concat(`&codigoAluno=${codigoAluno}`);
		}
		if (codigoPassivo) {
			url = url.concat(`&codigoPassivo=${codigoPassivo}`);
		}
		return this.restService.post(url, passivo).pipe(
			map((response: any) => {
				return { retorno: response.content };
			}),
			catchError((error) => {
				return of({ erro: error.error.meta.message });
			})
		);
	}

	incluirHorarioEquipamentoAluno(
		horarioId: number,
		alunoId: number,
		equipamento: string,
		dia: string
	): Observable<any> {
		const params: any = {
			equipamento,
		};
		return this.restService
			.put(`agenda/equipamento/${horarioId}/${alunoId}/${dia}`, {}, { params })
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}
}
