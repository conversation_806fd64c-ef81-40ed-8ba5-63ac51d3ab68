import { Injectable } from "@angular/core";

import { Observable } from "rxjs";

import { CadastroAuxApiModule } from "./cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "./cadastro-aux-api-base.service";
import { Classificacao } from "./classificacao.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiClassificacaoService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`classificacao/${id}`);
	}

	public save(classificacao: Classificacao): Observable<any> {
		return this.restApi.post(`classificacao`, classificacao);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`classificacao/${id}`);
	}
}
