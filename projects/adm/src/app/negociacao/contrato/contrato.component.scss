@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

.negociacao-contrato {
	.negociacao-contrato-scroll {
		overflow-y: auto;
		overflow-x: hidden;
		width: 100%;
		height: 100%;
		padding-bottom: 100px;
	}

	overflow: hidden;
	width: 100%;
	height: 100%;
	position: relative;

	adm-pacote-modalidades-negociacao {
		margin-top: 30px;
		margin-bottom: 30px;
		display: block;
	}

	pacto-cat-accordion {
		margin-bottom: 15px;
		display: block;
	}

	pacto-cat-form-select {
		margin: 0;
	}

	pacto-cat-accordion::ng-deep {
		.header-section {
			background-color: #fafafa;
			padding: 5px 15px;
		}
	}

	pacto-cat-card-plain::ng-deep {
		padding: 16px;
		min-height: 20vh;
	}

	pacto-cat-card-plain {
		margin-bottom: 16px;
	}

	.fixed {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;

		::ng-deep .info {
			border-radius: 0px;
			padding: 12px 21px;
		}
	}

	.hidden {
		display: none;
	}

	.botoes-negociacao {
		.btn-add-observacao {
			color: #1998fc;
			font-size: 16px;
			line-height: 16px;
			font-weight: 600;
			padding: 10px;
			cursor: pointer;

			i {
				margin-right: 6px;
			}
		}

		margin-top: 30px;

		pacto-cat-button {
			margin-left: 15px;
		}

		.botoes-negociacao-row {
			display: flex;

			.botoes-negociacao-row-left {
				margin-right: auto;
			}
		}
	}

	::ng-deep pacto-cat-accordion {
		.header-section.closed {
		}

		.body-section.ng-trigger.ng-trigger-openClose {
			background-color: #fafafa;
		}
	}

	.btns-right {
		text-align: right;
		padding-top: 32px;

		* {
			display: inline-block;
			margin-left: 10px;

			.opt-dropdown {
				font-style: normal;
				font-weight: 600;
				font-size: 12px;
				line-height: 100%;
				text-align: right;
				color: #1e60fa;
				flex-grow: 0;
				margin-left: 0;
				width: 100%;
				cursor: pointer;

				span {
					border-bottom: 1px solid #d7d8db;
					margin-right: 5%;
					margin-left: 5%;
					width: 90%;
					padding: 10px 0px;
				}

				&:last-child {
					span {
						border-bottom: none;
					}
				}

				&:hover {
					background: #b4cafd;
				}
			}
		}
	}

	::ng-deep .modal-negociacao-grande {
		width: 1000px !important;
		max-width: 1000px !important;
		height: 570px !important;
	}

	.accordion-body-content {
		padding-top: 16px;
	}

	.aviso-parcelas {
		color: #e10505;
		background-color: #fee6e6;
		padding: 8px 16px;
		font-size: 16px;
		border-radius: 5px;
		margin-bottom: 16px;
		i {
			font-size: 16px;
			margin-right: 8px;
		}
	}
}

.aviso-beta {
	color: #797d86;
	font-size: 14px;
	margin: 16px 16px 0 16px;
	background-color: #ffffff;
	padding: 8px 16px;
	border-radius: 5px;
	line-height: 16px;
	align-items: center;
	display: flex;
	justify-content: space-between;

	i {
		margin-right: 5px;
	}

	a {
		margin-left: 8px;
		color: #1e60fa;
		cursor: pointer;
		font-weight: 600;
		padding: 8px 13px;
		border-radius: 4px;

		&:hover {
			text-decoration: none;
			background: #b4cafd;
		}
	}
}

.linha-superior {
	display: grid;
	grid-template-columns: 1fr 0.5fr 0.5fr;
	gap: 16px;

	&.credito-treino {
		grid-template-columns: 1fr 0.5fr 0.5fr 0.5fr;
	}
}

.aviso-importante {
	i {
		margin-right: 8px;
		line-height: 20px;
	}

	span {
		text-align: left;
		margin-top: 0;
	}

	margin-top: 16px;
	display: block;
	align-items: center;
	color: #7d7d03;
	font-size: 14px;
	line-height: 20px;
	padding: 8px 16px;
	background-color: #fdfdb4;
	border-radius: 5px;
}

.info-top {
	background-color: #fafafa;
	padding: 8px 16px 8px 16px;
	border-radius: 8px;
	margin-bottom: 16px;
	display: flex;
	justify-content: space-between;

	div {
		padding-top: 3px;
	}

	span {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $supportGray07;
		border: 1px solid $supportGray03;
		border-radius: 20px;
		background-color: #ffffff;
		padding: 2px 12px;
		margin-right: 10px;
	}
}

.small-info {
	display: flex;
	color: #797d86;
	font-size: 12px;
	font-weight: 400;
	line-height: 16px;
	margin-bottom: 32px;

	span {
		margin: 0 8px;
	}

	i {
		font-size: 16px;
	}
}

.toast {
	i {
		margin-right: 10px;
		line-height: 20px;
	}
	&.cima {
		margin-bottom: 15px;
		margin-top: 15px;
	}

	margin-top: 26px;
	justify-content: center;
	align-items: center;
	color: #037d03;
	background-color: #b4fdb4;

	&.warning {
		color: #7d7d03;
		background-color: #fdfdb4;
	}

	font-size: 14px;
	line-height: 20px;
	padding: 8px 16px;
	border-radius: 5px;
}

::ng-deep.mdl-bv-negociacao {
	.mat-dialog-container {
		padding: unset;
	}
}

::ng-deep.mdl-obrg-auto-cobranca {
	.mat-dialog-container {
		padding: unset;
	}
}
