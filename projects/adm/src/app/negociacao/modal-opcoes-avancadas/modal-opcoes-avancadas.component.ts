import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Negociacao } from "adm-core-api";
import { NegociacaoService } from "../negociacao.service";

@Component({
	selector: "adm-modal-opcoes-avancadas",
	templateUrl: "./modal-opcoes-avancadas.component.html",
	styleUrls: ["./modal-opcoes-avancadas.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalOpcoesAvancadasComponent implements OnInit {
	negociacao: Negociacao;
	formGroup: FormGroup = new FormGroup({
		cobrarMatricula: new FormControl(false),
		vezesCobrarMatricula: new FormControl(1),
		cobrarProdutosSeparados: new FormControl(false),
		vezesCobrarProdutosSeparados: new FormControl(1),
		diaPrimeiraParcela: new FormControl(),
		dividirProdutoParcela: new FormControl(false),
		escolherDiaPrimeiraParcela: new FormControl(false),
		escolherDiaProrata: new FormControl(false),
		diaProrata: new FormControl(),
	});

	constructor(
		public dialog: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private negociacaoState: NegociacaoService
	) {}

	fillOutForm() {
		if (this.negociacaoState.getConfigsContrato().configuracoesAvancadas) {
			const config =
				this.negociacaoState.getConfigsContrato().configuracoesAvancadas;
			this.formGroup.get("cobrarMatricula").setValue(config.cobrarMatricula);
			this.formGroup
				.get("vezesCobrarMatricula")
				.setValue(config.vezesCobrarMatricula);
			this.formGroup
				.get("cobrarProdutosSeparados")
				.setValue(config.cobrarProdutosSeparados);
			this.formGroup
				.get("vezesCobrarProdutosSeparados")
				.setValue(config.vezesCobrarProdutosSeparados);
			this.formGroup
				.get("diaPrimeiraParcela")
				.setValue(config.diaPrimeiraParcela);
			this.formGroup
				.get("dividirProdutoParcela")
				.setValue(config.dividirProdutoParcela);
			this.formGroup
				.get("escolherDiaPrimeiraParcela")
				.setValue(config.escolherDiaPrimeiraParcela);
			this.formGroup
				.get("escolherDiaProrata")
				.setValue(config.escolherDiaProrata);
			this.formGroup.get("diaProrata").setValue(config.diaProrata);
		}
	}

	checkEnabled() {
		if (this.formGroup.get("cobrarMatricula").value === true) {
			this.formGroup.get("vezesCobrarMatricula").enable();
		} else {
			this.formGroup.get("vezesCobrarMatricula").disable();
		}
		if (this.formGroup.get("cobrarProdutosSeparados").value === true) {
			this.formGroup.get("vezesCobrarProdutosSeparados").enable();
		} else {
			this.formGroup.get("vezesCobrarProdutosSeparados").disable();
		}
		if (this.formGroup.get("escolherDiaPrimeiraParcela").value === true) {
			this.formGroup.get("diaPrimeiraParcela").enable();
		} else {
			this.formGroup.get("diaPrimeiraParcela").disable();
		}
		if (this.formGroup.get("escolherDiaProrata").value === true) {
			this.formGroup.get("diaProrata").enable();
		} else {
			this.formGroup.get("diaProrata").setValue(null);
			this.formGroup.get("diaProrata").disable();
		}
	}

	convertToFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	ngOnInit() {
		this.formGroup
			.get("cobrarProdutosSeparados")
			.valueChanges.subscribe((v) => {
				if (v) {
					this.formGroup
						.get("dividirProdutoParcela")
						.disable({ emitEvent: false });
				} else {
					this.formGroup
						.get("dividirProdutoParcela")
						.enable({ emitEvent: false });
				}
			});
		this.formGroup.get("dividirProdutoParcela").valueChanges.subscribe((v) => {
			if (this.negociacao.cobrarProdutoSeparado) {
				if (v) {
					this.formGroup
						.get("cobrarProdutosSeparados")
						.disable({ emitEvent: false });
				} else {
					this.formGroup
						.get("cobrarProdutosSeparados")
						.enable({ emitEvent: false });
				}
			}
		});
		if (this.negociacao.cobrarAdesaoSeparada === true) {
			this.formGroup.get("cobrarMatricula").setValue(true);
		} else {
			this.formGroup.get("cobrarMatricula").setValue(false);
			this.formGroup.get("cobrarMatricula").disable();
		}
		if (this.negociacao.cobrarProdutoSeparado === false) {
			this.formGroup.get("cobrarProdutosSeparados").setValue(false);
			this.formGroup.get("cobrarProdutosSeparados").disable();
		}
		if (
			!this.negociacaoState.getConfigsContrato().dataLancamento &&
			!this.negociacaoState.getConfigsContrato().diaPrimeiraParcela
		) {
			this.negociacaoState.getConfigsContrato().diaPrimeiraParcela =
				new Date().getTime();
		} else if (!this.negociacaoState.getConfigsContrato().diaPrimeiraParcela) {
			this.negociacaoState.getConfigsContrato().diaPrimeiraParcela =
				this.negociacaoState.getConfigsContrato().dataLancamento;
		}
		this.formGroup
			.get("diaPrimeiraParcela")
			.setValue(this.negociacaoState.getConfigsContrato().diaPrimeiraParcela);
		this.fillOutForm();
		this.checkEnabled();
		this.formGroup.get("cobrarMatricula").valueChanges.subscribe((c) => {
			this.checkEnabled();
		});
		this.formGroup
			.get("cobrarProdutosSeparados")
			.valueChanges.subscribe((c) => {
				this.formGroup.get("dividirProdutoParcela").setValue(false);
				this.checkEnabled();
			});
		this.formGroup
			.get("escolherDiaPrimeiraParcela")
			.valueChanges.subscribe((c) => {
				this.checkEnabled();
			});
		this.formGroup.get("escolherDiaProrata").valueChanges.subscribe((c) => {
			this.checkEnabled();
		});
	}

	salvar() {
		this.dialog.close(this.formGroup.getRawValue());
	}

	get nrVezesParcelarAdesao() {
		const ds = [];
		for (let i = 1; i <= this.negociacao.nrVezesParcelarAdesao; i++) {
			ds.push({ codigo: i, descricao: `${i}x` });
		}
		return ds;
	}

	get nrVezesParcelarProduto() {
		const ds = [];
		for (let i = 1; i <= this.negociacao.nrVezesparcelarProduto; i++) {
			ds.push({ codigo: i, descricao: `${i}x` });
		}
		return ds;
	}

	saibaMais(url) {
		window.open(url, "_blank");
	}
}
