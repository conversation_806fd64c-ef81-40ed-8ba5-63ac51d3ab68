import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { CryptoService } from "sdk";
import { catchError, map } from "rxjs/operators";
import { Observable, throwError } from "rxjs";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class AdmLegadoAutorizarAcessoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService,
							private criptoService: CryptoService,
	) {
	}

	validarPermissao(
		chave,
		username,
		senha,
		funcionalidade,
		permissao,
		nomeEmpresa,
	): Observable<any> {
		const json = JSON.stringify({
			username,
			senha,
			funcionalidade,
			permissao,
			empresa: nomeEmpresa,
		});
		const bodyCrip = this.criptoService.encrypt(json);
		return this.admZwBootApiBaseService.post(
			`permissao/validar-permissao-username`,
			{ data: bodyCrip },
		).pipe(
			map((response: any) => {
				return response;
			}),
			catchError((error) => {
				if (error.error.meta && error.error.meta.message) {
					const msgDesc = this.criptoService.decrypt(error.error.meta.message);
					if (msgDesc) {
						error.error.meta.message = msgDesc;
					}
				}
				return throwError(error);
			})
		);
	}

	validarPermissaoUsuarioLogado(
		chave,
		codigoUsuario,
		codigoEmpresa,
		funcionalidade,
		permissao,
		operacaoString: string = null,
	): Observable<any> {
		const json = JSON.stringify({
			codigoUsuario,
			codigoEmpresa,
			funcionalidade,
			permissao,
			operacaoString,
		});
		const bodyCrip = this.criptoService.encrypt(json);
		return this.admZwBootApiBaseService.post(
			`permissao/validar-permissao-usuario`,
			{ data: bodyCrip },
		).pipe(
			map((response: any) => {
				return response;
			}),
			catchError((error) => {
				if (error.error.meta && error.error.meta.message) {
					const msgDesc = this.criptoService.decrypt(error.error.meta.message);
					if (msgDesc) {
						error.error.meta.message = msgDesc;
					}
				}
				return throwError(error);
			})
		);
	}
}
