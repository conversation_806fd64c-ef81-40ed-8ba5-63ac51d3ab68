import { Injectable } from "@angular/core";
import {
	ActivatedRouteSnapshot,
	CanActivate,
	Router,
	RouterStateSnapshot,
} from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { Observable } from "rxjs";
import { PerfilAcessoRecursoNome } from "treino-api";

@Injectable({
	providedIn: "root",
})
export class FichasPreDefinidasGuard implements CanActivate {
	constructor(
		private session: SessionService,
		private snotifyService: SnotifyService,
		private router: Router
	) {}

	canActivate(
		route: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> | Promise<boolean> | boolean {
		const perfilPermissao = this.session.recursos.get(
			PerfilAcessoRecursoNome.FICHAS_PRE_DEFINIDAS
		);
		if (state.url.indexOf("adicionar") !== -1) {
			if (perfilPermissao && perfilPermissao.incluir) {
				return true;
			} else {
				this.snotifyService.warning(
					"Seu usuário não possui permissão, procure seu administrador"
				);
				return this.router.navigate(["treino/bi"]);
			}
		} else {
			if (perfilPermissao && perfilPermissao.editar) {
				return true;
			} else {
				this.snotifyService.warning(
					"Seu usuário não possui permissão, procure seu administrador"
				);
				return this.router.navigate(["treino/bi"]);
			}
		}
		return true;
	}
}
