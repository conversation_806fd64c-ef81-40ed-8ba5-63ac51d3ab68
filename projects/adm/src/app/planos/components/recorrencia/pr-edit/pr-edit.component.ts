import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { Plano, PlanoEmpresa } from "../../../plano.model";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { PerfilAcessoRecurso } from "sdk";
import { TraducoesXinglingComponent } from "ui-kit";
import { PlanoConfiguracaoSistema } from "../../../services/plano-configuracao-sistema.model";

@Component({
	selector: "adm-pr-edit",
	templateUrl: "./pr-edit.component.html",
	styleUrls: ["./pr-edit.component.scss"],
})
export class PrEditComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	isSaving = false;
	vezesSemanaOptions: Array<any> = new Array<any>();
	plano: Plano;
	recurso: PerfilAcessoRecurso;
	configuracaoSistema: PlanoConfiguracaoSistema;
	nomePlano: string = "";
	constructor(
		public planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private planoService: PlanoService,
		private cd: ChangeDetectorRef
	) {
		this.recurso = this.planoCommonsService.recurso;
	}

	ngOnInit() {
		this.initConfiguracaoSistema();
		this.plano = this.planoStateService.updatePlanoObj();
		this.vezesSemanaOptions = this.planoCommonsService.vezesSemanaOptions();
		this.plano = this.planoCommonsService.populateModalidadeAux(this.plano);
		this.plano = this.planoCommonsService.populateHorarioValor(this.plano);
		this.planoStateService.updateState(this.plano);
	}

	initConfiguracaoSistema() {
		this.planoCommonsService
			.initConfiguracaoSistema()
			.subscribe((data: PlanoConfiguracaoSistema) => {
				this.configuracaoSistema = data;
				this.cd.detectChanges();
			});
	}

	goBack() {
		this.planoCommonsService.voltarParaListagem();
	}

	save() {
		if (!this.isSaving) {
			const plano = this.planoCommonsService.convertPlano(
				this.planoStateService.updatePlanoObj()
			);
			if (plano.planoRecorrencia) {
				if (!plano.planoRecorrencia.anuidadeNaParcela) {
					plano.planoRecorrencia.parcelarAnuidade = undefined;
					plano.planoRecorrencia.parcelasAnuidade = undefined;
					plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					plano.planoRecorrencia.diaAnuidade = undefined;
					plano.planoRecorrencia.mesAnuidade = undefined;
				}
				if (plano.planoRecorrencia.parcelarAnuidade) {
					plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					plano.planoRecorrencia.parcelasAnuidade = undefined;
				}
				if (plano.planoRecorrencia.parcelasAnuidade) {
					plano.planoRecorrencia.parcelasAnuidade.forEach((parcelaAnuidade) => {
						if (parcelaAnuidade.parcela && parcelaAnuidade.parcela.value) {
							parcelaAnuidade.parcela = parcelaAnuidade.parcela.value;
						}
					});
				}
			}
			if (!this.plano.apresentarVendaRapida) {
				this.plano.empresas = new Array<PlanoEmpresa>();
			}
			if (!plano.bolsa && plano.modalidades) {
				let modalidadeValorZerado = false;
				for (const d of plano.modalidades) {
					if (d.modalidade.valorMensal > 0) {
						modalidadeValorZerado = true;
						break;
					}
				}
				if (!modalidadeValorZerado) {
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-hint"),
						{
							timeout: 10000,
						}
					);
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-error"),
						{
							timeout: 10000,
						}
					);
					return;
				}
			}
			this.isSaving = true;
			this.planoService.save(plano).subscribe(
				(response) => {
					if ((this.plano && this.plano.codigo && this.plano.codigo > 0) && (this.configuracaoSistema && this.configuracaoSistema.permitirreplicarplanoredeempresa)) {
						this.planoService.replicarAutomaticoTodosReplicados(this.plano.codigo).subscribe((ret) => {
						});
					}
					this.isSaving = true;
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					this.goBack();
				},
				(responseError) => {
					this.isSaving = false;
					this.cd.detectChanges();
					if (responseError.error) {
						if (responseError.error.meta) {
							this.notificationService.error(
								responseError.error.meta.messageValue
							);
						}
					}
				}
			);
		}
	}
}
