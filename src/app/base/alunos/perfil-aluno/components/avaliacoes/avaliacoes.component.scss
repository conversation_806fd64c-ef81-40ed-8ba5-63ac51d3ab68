@import "src/assets/scss/pacto/plataforma-import.scss";

.column-wrapper {
	display: flex;
	flex-wrap: wrap;
}

.avaliacao-column {
	flex-basis: 24%;
	flex-grow: 1;

	@media (max-width: $plataforma-breakpoint-medium) {
		flex-basis: 49%;
		padding-bottom: 30px;
		padding-top: 30px;
	}

	@media (max-width: $plataforma-breakpoint-small) {
		flex-basis: 100%;
		padding-bottom: 30px;
		padding-top: 30px;
	}
}

.rating {
	text-align: center;
	@extend .type-h6;
	color: $pretoPri;
	margin-top: 5px;

	span {
		cursor: pointer;
	}

	.pct-info {
		font-size: 16px;
		position: relative;
		top: 2px;
		padding: 0px 5px;
	}
}

.column-title {
	@extend .type-h5;
	color: $pretoPri;
	text-align: center;
	margin-bottom: 25px;
}

/* BASIC INFO */
.basic-info {
	.bit:not(:last-child) {
		margin-bottom: 32px;
	}

	.bit .value {
		@extend .type-h6;
		color: $gelo04;

		&.objetivos {
			text-transform: capitalize;
		}
	}

	.bit .bit-label {
		@extend .type-p-small-rounded;
		color: $pretoPri;
	}
}

/* IMC */
.imc {
	text-align: center;
	display: flex;
	flex-direction: column;

	.flex-aux {
		flex-grow: 1;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;

		> * {
			flex-basis: 100%;
		}

		.info-title {
			@extend .type-p-small-rounded;
			color: $gelo04;
		}

		.info-value {
			@extend .type-hero-bold;
			margin: 5px 0px;
			color: $pretoPri;
		}

		.small-info {
			flex-basis: 50%;

			.info-label {
				@extend .type-h6;
				color: $pretoPri;
			}

			.value {
				@extend .type-p-small-rounded;
				color: $gelo04;
			}
		}
	}
}

/* LISTA */
.lista-avaliacoes {
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;

	.column-title {
		flex-grow: 1;
		text-align: left;
	}

	pacto-cat-lista-avaliacoes {
		flex-grow: 1;
		display: block;
		flex-basis: 100%;
	}

	pacto-cat-lista-avaliacao-integrada {
		flex-grow: 1;
		display: block;
		flex-basis: 100%;
	}

	.btn-footer-av-integrada {
		width: 100%;
		display: flex;
		margin-top: 16px;

		.btn-print-av-integradas {
			margin-right: 16px;
		}
	}
}

.list-avaliacao-integrada {
	margin-top: 30px;
	height: 420px;
}

.empty-avaliacao-integrada {
	::ng-deep pacto-cat-card-plain {
		margin-top: 30px;
	}
}
