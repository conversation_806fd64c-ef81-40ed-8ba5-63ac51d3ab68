import {
	Component,
	Input,
	OnInit,
	forwardRef,
	ChangeDetectorRef,
	Output,
	EventEmitter,
	ChangeDetectionStrategy,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, NG_VALUE_ACCESSOR } from "@angular/forms";
import { BaseControlValueAccessor } from "@base-shared/BaseControlValueAccessor";
import { PerfilRecursoPermissoTipo } from "treino-api";
import { CatToggleInputComponent } from "ui-kit";

@Component({
	selector: "pacto-perfil-permissao-item",
	templateUrl: "./perfil-permissao-item.component.html",
	styleUrls: ["./perfil-permissao-item.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => PerfilPermissaoItemComponent),
			multi: true,
		},
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PerfilPermissaoItemComponent
	extends BaseControlValueAccessor<PerfilRecursoPermissoTipo[]>
	implements OnInit
{
	@Input() label: string;
	@Input() id: string;
	@Output() enable: EventEmitter<PerfilRecursoPermissoTipo> =
		new EventEmitter();
	@Output() disable: EventEmitter<PerfilRecursoPermissoTipo> =
		new EventEmitter();
	@ViewChild("consultarInput", { static: true })
	consultarInput: CatToggleInputComponent;
	@ViewChild("incluirInput", { static: true })
	incluirInput: CatToggleInputComponent;
	@ViewChild("editarInput", { static: true })
	editarInput: CatToggleInputComponent;
	@ViewChild("excluirInput", { static: true })
	excluirInput: CatToggleInputComponent;
	@ViewChild("totalExcetoExcluirInput", { static: true })
	totalExcetoExcluirInput: CatToggleInputComponent;
	@ViewChild("totalInput", { static: true })
	totalInput: CatToggleInputComponent;

	constructor(private cd: ChangeDetectorRef) {
		super();
	}

	loading = false;
	excluirMarcado;
	totalMarcado;
	totalExcetoExcluirMarcado;
	_fg = new FormGroup({
		INCLUIR: new FormControl(false),
		CONSULTAR: new FormControl(false),
		EDITAR: new FormControl(false),
		EXCLUIR: new FormControl(false),
		TOTAL_EXCETO_EXCLUIR: new FormControl(false),
		TOTAL: new FormControl(false),
	});

	ngOnInit() {
		if (this._fg) {
			this.setMarcados();
		}
		this._fg.valueChanges.subscribe((value) => {
			this.onChange(this.convertData());
			this.cd.detectChanges();
		});

		const controls = this._fg.controls;
		for (const item in controls) {
			if (controls.hasOwnProperty(item)) {
				this._fg.get(item).valueChanges.subscribe((value) => {
					if (value) {
						this.enable.emit(item as PerfilRecursoPermissoTipo);
					} else {
						this.disable.emit(item as PerfilRecursoPermissoTipo);
					}
				});
			}
		}
	}

	get PermissaoTipo() {
		return PerfilRecursoPermissoTipo;
	}

	get value() {
		return this.convertData();
	}

	set value(value) {
		this.setValue(value);
		this.onChange(value);
	}

	private setMarcados() {
		const dto = this._fg.getRawValue();
		this.totalExcetoExcluirMarcado =
			dto[PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR];
		this.excluirMarcado = dto[PerfilRecursoPermissoTipo.EXCLUIR];
		this.totalMarcado = dto[PerfilRecursoPermissoTipo.TOTAL];
	}

	private convertData(): PerfilRecursoPermissoTipo[] {
		this.loading = true;
		this.cd.detectChanges();
		let dto = this._fg.getRawValue();
		let result = [];
		const alterouExcluir =
			this.excluirMarcado !== dto[PerfilRecursoPermissoTipo.EXCLUIR];
		const alterouTotal =
			this.totalMarcado !== dto[PerfilRecursoPermissoTipo.TOTAL];
		const alterouTotalExcetoExcluir =
			this.totalExcetoExcluirMarcado !==
			dto[PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR];

		// caso ação de desmarcar total, desmarcar todos
		if (!dto[PerfilRecursoPermissoTipo.TOTAL] && alterouTotal) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.CONSULTAR,
				this.consultarInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.INCLUIR,
				this.incluirInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EDITAR,
				this.editarInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EXCLUIR,
				this.excluirInput,
				false
			);
		}
		// caso ação de desmarcar total exceto exluir, desmarcar consultar incluir editar
		if (
			!dto[PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR] &&
			alterouTotalExcetoExcluir
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.CONSULTAR,
				this.consultarInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.INCLUIR,
				this.incluirInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EDITAR,
				this.editarInput,
				false
			);
		}
		dto = this._fg.getRawValue();
		for (const item in dto) {
			if (dto[item]) {
				result.push(item);
			}
		}
		if (
			result.includes(PerfilRecursoPermissoTipo.CONSULTAR) &&
			result.includes(PerfilRecursoPermissoTipo.INCLUIR) &&
			result.includes(PerfilRecursoPermissoTipo.EDITAR) &&
			alterouTotal
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL,
				this.totalInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				this.totalExcetoExcluirInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EXCLUIR,
				this.excluirInput,
				true
			);
		} else if (
			result.includes(PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR) &&
			result.includes(PerfilRecursoPermissoTipo.EXCLUIR) &&
			!alterouTotal &&
			!alterouTotalExcetoExcluir
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				this.totalExcetoExcluirInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL,
				this.totalInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EXCLUIR,
				this.excluirInput,
				true
			);
		} else if (
			result.includes(PerfilRecursoPermissoTipo.CONSULTAR) &&
			result.includes(PerfilRecursoPermissoTipo.INCLUIR) &&
			result.includes(PerfilRecursoPermissoTipo.EDITAR) &&
			result.includes(PerfilRecursoPermissoTipo.EXCLUIR) &&
			!alterouTotalExcetoExcluir &&
			!alterouTotal
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL,
				this.totalInput,
				true
			);
		} else if (
			result.includes(PerfilRecursoPermissoTipo.CONSULTAR) &&
			result.includes(PerfilRecursoPermissoTipo.INCLUIR) &&
			result.includes(PerfilRecursoPermissoTipo.EDITAR) &&
			alterouExcluir
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL,
				this.totalInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				this.totalExcetoExcluirInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EXCLUIR,
				this.excluirInput,
				false
			);
		} else if (
			result.includes(PerfilRecursoPermissoTipo.CONSULTAR) &&
			result.includes(PerfilRecursoPermissoTipo.INCLUIR) &&
			result.includes(PerfilRecursoPermissoTipo.EDITAR) &&
			!alterouTotalExcetoExcluir
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				this.totalExcetoExcluirInput,
				true
			);
		} else if (
			result.includes(PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR) &&
			alterouTotalExcetoExcluir
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.CONSULTAR,
				this.consultarInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.INCLUIR,
				this.incluirInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EDITAR,
				this.editarInput,
				true
			);

			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EXCLUIR,
				this.excluirInput,
				false
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL,
				this.totalInput,
				false
			);
		} else if (
			result.includes(PerfilRecursoPermissoTipo.TOTAL) &&
			alterouTotal
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.CONSULTAR,
				this.consultarInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.INCLUIR,
				this.incluirInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EDITAR,
				this.editarInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.EXCLUIR,
				this.excluirInput,
				true
			);
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				this.totalExcetoExcluirInput,
				false
			);
		} else if (
			(!result.includes(PerfilRecursoPermissoTipo.CONSULTAR) ||
				!result.includes(PerfilRecursoPermissoTipo.INCLUIR) ||
				!result.includes(PerfilRecursoPermissoTipo.EDITAR)) &&
			!result.includes(PerfilRecursoPermissoTipo.EXCLUIR) &&
			!alterouTotalExcetoExcluir
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				this.totalExcetoExcluirInput,
				false
			);
		} else if (
			(!result.includes(PerfilRecursoPermissoTipo.CONSULTAR) ||
				!result.includes(PerfilRecursoPermissoTipo.INCLUIR) ||
				!result.includes(PerfilRecursoPermissoTipo.EDITAR) ||
				!result.includes(PerfilRecursoPermissoTipo.EXCLUIR)) &&
			!alterouTotal
		) {
			this.changeInput(
				result,
				PerfilRecursoPermissoTipo.TOTAL,
				this.totalInput,
				false
			);
		}
		result = result.filter((item, index) => result.indexOf(item) === index);
		this.setMarcados();
		this.loading = false;
		this.cd.detectChanges();
		return result;
	}

	private changeInput(
		result: Array<PerfilRecursoPermissoTipo>,
		permissao: PerfilRecursoPermissoTipo,
		child: CatToggleInputComponent,
		value: boolean
	) {
		if (value) {
			result.push(permissao);
		} else {
			const index = result.indexOf(permissao, 0);
			if (index > -1) {
				result.splice(index, 1);
			}
		}
		this._fg.get(permissao).patchValue(value, { emitEvent: false });
		if (child) {
			child.forceChange();
		}
	}

	private setValue(value: PerfilRecursoPermissoTipo[]) {
		for (const tipo in PerfilRecursoPermissoTipo) {
			if (isNaN(Number(tipo))) {
				const exists =
					value === null
						? false
						: value.indexOf(tipo as PerfilRecursoPermissoTipo) >= 0;
				this._fg.get(tipo).setValue(exists);
			}
		}
	}
}
