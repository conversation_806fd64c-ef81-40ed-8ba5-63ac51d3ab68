import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AutorizacaoAcessoFormComponent } from "./components/autorizacao-acesso-form/autorizacao-acesso-form.component";
import { AutorizacaoAcessoListaComponent } from "./components/autorizacao-acesso-lista/autorizacao-acesso-lista.component";

const routes: Routes = [
	{
		path: "",
		component: AutorizacaoAcessoListaComponent,
	},
	{
		path: "nova-autorizacao-acesso",
		component: AutorizacaoAcessoFormComponent,
	},
	{
		path: "editar/:id",
		component: AutorizacaoAcessoFormComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class AutorizacaoAcessoRoutingModule {}
