@import "projects/ui/assets/import";

@media (min-width: 1050px) {
	::ng-deep .modal-integracao .modal-lg {
		max-width: 1050px;
	}
}

pacto-cat-form-input {
	margin: 0px;
}

.main-content {
	padding: 27px 68px 27px 68px;
}

.check-box {
	margin-top: 16px;
}

.row-check-box {
	font-family: "Nunito Sans";
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	line-height: 22px;
	color: $cinza05;
}

.description {
	margin-top: 34px;

	p {
		font-family: "Nunito Sans";
		font-style: normal;
		font-weight: 400;
		font-size: 16px;
		line-height: 26px;
		color: $cinza05;
	}

	span {
		color: $hellboyPri;
	}
}

.row-add-produto {
	display: flex;
	align-items: center;
}

.descricao-produto {
	margin-bottom: 0px;
}

.produto-select {
	margin-top: -46px;
	width: 274px;
	height: 42px;
}

.btn-adicionar {
	margin-left: -37px;
	margin-right: 37px;
}

.btns-ativar-inativar {
	margin-top: 38px;
}

h3 {
	font-family: "Nunito Sans";
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	line-height: 22px;
	font-weight: 600;
	font-size: 16px;
	line-height: 22px;
	color: $preto01;
	margin-bottom: 19px;
}

.line-1 {
	width: 100%;
	height: 1px;
	left: 68px;
	top: 883px;
	background: $cinza03;
	transform: matrix(1, 0, 0, -1, 0, 0);
}

.line-2 {
	width: 100%;
	height: 1px;
	left: 68px;
	top: 883px;
	background: $cinza03;
	transform: matrix(1, 0, 0, -1, 0, 0);
	margin-bottom: 15px;
}

.row-table-dotz {
	display: flex;
	align-items: baseline;
	justify-content: space-between;
	margin-top: 72px;
}

#table-data-dotz {
	margin-top: 23px;
}

.row-excluir-tabela-acumular-dotz {
	display: flex;
	align-items: baseline;
	margin-top: 63px;
}

#table-dotz,
#table-valores,
#tabelas-acumular-dotz {
	//margin: 0px -30px 0px -30px;
}

#btn-nova-tabela {
	margin: 0px 15px 40px 0px;
}
