import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../../models";
import { MenuConfigService } from "../../menu-config.service";
import { FeatureManagerService } from "sdk";
import { PactoLayoutSDKWrapper } from "../../../../../sdk-wrapper/sdk-wrappers";

@Injectable({
	providedIn: "root",
})
export class PactopayOperacoesMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private readonly featureManagerService: FeatureManagerService,
		private sdkWrapper: PactoLayoutSDKWrapper
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([
			this.cartaoCreditoMenuItem,
			this.cartaoCreditoOnlineMenuItem,
			this.reguaCobrancaDashboardMenuItem,
			this.reguaCobrancaConfigEmailMenuItem,
			this.reguaCobrancaConfigFasesMenuItem,
			this.gestaoRemessaMenuItem,
			this.pixMenuItem,
			this.operacoesParentItem,
			this.reguaCobrancaParentItem,
		]);
	}

	get operacoesParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes",
			configMenuExplorar: {
				submenus: [
					this.cartaoCreditoMenuItem,
					this.cartaoCreditoOnlineMenuItem,
					this.reguaCobrancaDashboardMenuItem,
					this.reguaCobrancaConfigEmailMenuItem,
					this.reguaCobrancaConfigFasesMenuItem,
					this.gestaoRemessaMenuItem,
					this.pixMenuItem,
					this.reguaCobrancaParentItem,
				],
			},
		};

		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cartaoCreditoMenuItem(): PlatformMenuItem {
		return {
			id: "cartao",
			permitido: true,
			favoriteIdentifier: "PACTOPAY_CARTAO_CREDITO",
			route: {
				internalLink: "/pactopay/cartao",
			},
		};
	}

	get cartaoCreditoOnlineMenuItem(): PlatformMenuItem {
		return {
			id: "cartaoCreditoOnline",
			permitido: true,
			favoriteIdentifier: "PACTOPAY_CARTAO_CREDITO_ONLINE",
			route: {
				internalLink: "/pactopay/transacoes/credito-online",
			},
		};
	}

	permitidoReguaCobranca(): boolean {
		return (
			this.sdkWrapper &&
			this.sdkWrapper.sessionService.modulosHabilitados.includes(
				PlataformModuleConfig.FAC.idUppercase
			) &&
			this.layoutNavigationService.faciliteConfig.reguaCobranca
		);
	}

	permitidoBIReguaCobranca(): boolean {
		const ehPactoBR =
			"PACTOBR" ===
			this.sdkWrapper.sessionService.loggedUser.username.toUpperCase();
		return (
			this.sdkWrapper &&
			this.sdkWrapper.sessionService.modulosHabilitados.includes(
				PlataformModuleConfig.FAC.idUppercase
			) &&
			this.layoutNavigationService.faciliteConfig.reguaCobranca &&
			ehPactoBR
		);
	}

	permitidoReguaCobrancaConfiguracaoEmail(): boolean {
		return (
			this.sdkWrapper &&
			this.sdkWrapper.sessionService.modulosHabilitados.includes(
				PlataformModuleConfig.FAC.idUppercase
			) &&
			this.layoutNavigationService.modules.includes(
				PlataformModuleConfig.FAC
			) &&
			this.layoutNavigationService.faciliteConfig.reguaCobrancaConfiguracaoEmail
		);
	}

	permitidoReguaConciliacaoContasPagar(): boolean {
		return (
			this.sdkWrapper &&
			this.sdkWrapper.sessionService.modulosHabilitados.includes(
				PlataformModuleConfig.FAC.idUppercase
			) &&
			this.layoutNavigationService.modules.includes(
				PlataformModuleConfig.FAC
			) &&
			this.layoutNavigationService.faciliteConfig.conciliacaoContasPagar
		);
	}

	get reguaCobrancaParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "regua",
			permitido: this.permitidoReguaCobranca(),
			configMenuExplorar: {
				submenus: [
					this.reguaCobrancaConfigEmailMenuItem,
					this.reguaCobrancaConfigFasesMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get reguaCobrancaDashboardMenuItem(): PlatformMenuItem {
		return {
			id: "dashboard_da_regua",
			favoriteIdentifier: "PACTOPAY_REGUA_COBRANCA",
			permitido: this.permitidoBIReguaCobranca(),
			route: {
				internalLink: "/pactopay/regua/dashboard",
			},
		};
	}

	get reguaCobrancaConfigEmailMenuItem(): PlatformMenuItem {
		return {
			id: "configuracao_de_email",
			favoriteIdentifier: "PACTOPAY_CONFIGURACAO_EMAIL",
			permitido: this.permitidoReguaCobrancaConfiguracaoEmail(),
			route: {
				internalLink: "/pactopay/regua/configuracao-de-email",
			},
		};
	}

	get reguaCobrancaConfigFasesMenuItem(): PlatformMenuItem {
		return {
			id: "configuracao_de_fases",
			favoriteIdentifier: "PACTOPAY_CONFIGURACAO_FASES",
			permitido: this.permitidoReguaCobranca(),
			route: {
				internalLink: "/pactopay/regua/configuracao-de-fases",
			},
		};
	}

	get gestaoRemessaMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoRemessas",
			favoriteIdentifier: "GESTAO_DE_REMESSAS",
			module: PlataformModuleConfig.ADM_LEGADO,
			permitido: true,
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_REMESSAS",
					jspPage: "gestaoRemessas.jsp",
				},
			},
		};
	}

	get pixMenuItem(): PlatformMenuItem {
		return {
			id: "pix",
			permitido: true,
			favoriteIdentifier: "PACTOPAY_PIX",
			route: {
				internalLink: "/pactopay/transacoes/pix",
			},
		};
	}
}
