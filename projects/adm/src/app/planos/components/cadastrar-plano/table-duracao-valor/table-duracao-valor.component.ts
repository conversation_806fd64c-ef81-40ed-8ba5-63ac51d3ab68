import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Plano, TipoPlano } from "../../../plano.model";
import { AdmRestService } from "../../../../adm-rest.service";
import { PlanoStateService } from "../plano-state.service";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TableData,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { CadastrarModalidadePlanoComponent } from "../../cadastrar-modalidade-plano/cadastrar-modalidade-plano.component";
import { CadastrarModalidadeHorarioComponent } from "../../cadastrar-modalidade-horario/cadastrar-modalidade-horario.component";
import { CadastrarModalidadeRepeticaoComponent } from "../../cadastrar-modalidade-repeticao/cadastrar-modalidade-repeticao.component";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";
import { PlanoService } from "../plano.service";

@Component({
	selector: "adm-table-duracao-valor",
	templateUrl: "./table-duracao-valor.component.html",
	styleUrls: ["./table-duracao-valor.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableDuracaoValorComponent implements OnInit {
	@Input() formDadosBasicos: FormGroup;
	@Input() planoDTO: Plano;
	@Output() afterAdd: EventEmitter<any> = new EventEmitter<any>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("tableDuracaoValorComponent", { static: false })
	tableDuracaoValorComponent: CatTableEditableComponent;
	tableDuracaoValor: PactoDataGridConfig;
	excecoes: Array<any> = new Array<any>();

	@ViewChild("columnPacote", { static: true }) columnPacote: TemplateRef<any>;
	@ViewChild("columnModalidade", { static: true })
	columnModalidade: TemplateRef<any>;
	@ViewChild("columnHorario", { static: true }) columnHorario: TemplateRef<any>;
	@ViewChild("columnVezesSemana", { static: true })
	columnVezesSemana: TemplateRef<any>;
	@ViewChild("columnDuracao", { static: true }) columnDuracao: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	constructor(
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private state: PlanoStateService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe,
		private planoService: PlanoService
	) {}

	ngOnInit() {
		if (this.state.plano) {
			this.planoDTO = this.state.plano;
		} else {
			if (!this.planoDTO) {
				this.planoDTO = new Plano();
			}
		}
		this.initTableDuracaoValor();
		this.tableDuracaoValor.dataAdapterFn = (serverData): TableData<any> => {
			if (this.planoDTO.excecoes) {
				this.excecoes = this.planoDTO.excecoes;
			}
			serverData.content = this.excecoes;
			return serverData;
		};
		if (this.formDadosBasicos) {
			let horarioColumn;
			if (this.planoDTO.codigo) {
				horarioColumn = this.tableDuracaoValor.columns.find(
					(column) => column.nome === "horario"
				);
				horarioColumn.visible =
					this.formDadosBasicos.get("tipoPlano").value === "PLANO_NORMAL" ||
					this.formDadosBasicos.get("tipoPlano").value ===
						"PLANO_RECORRENCIA" ||
					(this.formDadosBasicos.get("tipoPlano").value === "PLANO_CREDITO" &&
						this.formDadosBasicos.get("gerarParcelasValorDiferente").value);
			}
			this.formDadosBasicos.get("tipoPlano").valueChanges.subscribe((value) => {
				// TODO Lucas Rezende: Uma solução não muito boa, mas tá funcionando.
				this.initTableDuracaoValor();
			});
			this.formDadosBasicos
				.get("gerarParcelasValorDiferente")
				.valueChanges.subscribe((value) => {
					// TODO Lucas Rezende: Uma solução não muito boa, mas tá funcionando.
					if (!this.planoDTO.codigo) {
						// TODO Lucas Rezende: Uma solução não muito boa, mas tá funcionando.
						this.initTableDuracaoValor();
					} else {
						horarioColumn.visible = value;
					}
				});
		}
	}

	initTableDuracaoValor() {
		this.tableDuracaoValor = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				modalidade: new FormControl(),
				pacote: new FormControl(),
				horario: new FormControl(),
				vezesSemana: new FormControl(""),
				duracao: new FormControl(""),
				valor: new FormControl(""),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "30px",
				},
				{
					nome: "pacote",
					titulo: this.columnPacote,
					visible:
						this.planoDTO.tipoPlano === "PLANO_NORMAL" ||
						this.planoDTO.tipoPlano === "PLANO_AVANCADO",
					ordenavel: false,
					editable: true,
					showSelectFilter: true,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					showAddSelectBtn: false,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					width: "150px",
					endpointUrl:
						this.admRest.buildFullUrlPlano("pacotes") + "/only-cod-name",
					addEmptyOption: true,
					selectOptionChange: (option, form, row) =>
						this.onPacoteChange(option, form, row),
				},
				{
					nome: "modalidade",
					titulo: this.columnModalidade,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: true,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					showAddSelectBtn: false,
					inputType: "select",
					dialogComponent: CadastrarModalidadePlanoComponent,
					idSelectKey: "codigo",
					objectAttrLabelName: "nome",
					labelSelectKey: "nome",
					width: "150px",
					endpointUrl:
						this.admRest.buildFullUrlPlano("modalidades") + "/only-cod-name",
					addEmptyOption: true,
				},
				{
					nome: "horario",
					titulo: this.columnHorario,
					visible:
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_NORMAL" ||
						this.formDadosBasicos.get("tipoPlano").value ===
							"PLANO_RECORRENCIA" ||
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_AVANCADO" ||
						(this.formDadosBasicos.get("tipoPlano").value === "PLANO_CREDITO" &&
							this.formDadosBasicos.get("gerarParcelasValorDiferente").value),
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					dialogComponent: CadastrarModalidadeHorarioComponent,
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					width: "200px",
					showSelectFilter: true,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term || "",
								quicksearchFields: ["descricao"],
							}),
							size: "1000",
						};
					},
					showAddSelectBtn: false,
					endpointUrl:
						this.admRest.buildFullUrlPlano("horarios") + "/only-ativos",
				},
				{
					nome: "vezesSemana",
					titulo: this.columnVezesSemana,
					visible:
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_NORMAL" ||
						this.formDadosBasicos.get("tipoPlano").value ===
							"PLANO_RECORRENCIA" ||
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_AVANCADO" ||
						(this.formDadosBasicos.get("tipoPlano").value === "PLANO_CREDITO" &&
							this.formDadosBasicos.get("gerarParcelasValorDiferente").value),
					ordenavel: false,
					editable: true,
					valueTransform: (v) => (v ? v + "x" : ""),
					dialogComponent: CadastrarModalidadeRepeticaoComponent,
					inputType: "number",
					showAddSelectBtn: false,
					showSelectFilter: false,
					width: "100px",
				},
				{
					nome: "duracao",
					titulo: this.columnDuracao,
					visible:
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_NORMAL" ||
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_AVANCADO",
					ordenavel: false,
					editable: true,
					inputType: "number",
					minValue: 0,
					width: "100px",
					errorMessage: "Valor Obrigatório",
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					visible:
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_NORMAL" ||
						this.formDadosBasicos.get("tipoPlano").value === "PLANO_AVANCADO" ||
						(this.formDadosBasicos.get("tipoPlano").value === "PLANO_CREDITO" &&
							this.formDadosBasicos.get("gerarParcelasValorDiferente").value),
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimal: true,
					width: "100px",
					errorMessage: "Valor Obrigatório",
				},
			],
		});
		this.cd.detectChanges();
	}

	confirm(event) {
		if (
			this.excecoes &&
			event &&
			event.row &&
			event.row.codigo &&
			event.row.codigo !== "" &&
			event.row.codigo !== null &&
			event.row.codigo !== undefined
		) {
			this.excecoes.forEach((e) => {
				if (e.codigo === event.row.codigo) {
					e.edit = false;
				}
			});
		}

		this.planoDTO.excecoes = this.excecoes;
		this.planoDTO.excecoes.forEach((excecao) => {
			if (excecao.modalidade && excecao.modalidade.codigo === null) {
				excecao.modalidade = undefined;
			}
			if (excecao.pacote && excecao.pacote.codigo === null) {
				excecao.pacote = undefined;
			}
		});
		this.state.plano = this.planoDTO;
		this.afterAdd.emit({ planoDTO: this.planoDTO });
	}

	async delete(event) {
		const retorno = await this.validarExisteContratoDuracao(event);
		if (retorno) {
			this.planoDTO = this.state.plano;
			let index2;
			if (this.excecoes) {
				let index;
				if (event.row.codigo) {
					const excecao = this.excecoes.find((ex, i) => {
						if (ex.codigo === event.row.codigo) {
							index = i;
							return ex;
						}
					});
					if (excecao && index !== undefined) {
						if (this.planoDTO.duracoes) {
							this.planoDTO.duracoes.find((duracao, i) => {
								if (duracao.numeroMeses === event.row.duracao) {
									index2 = i;
								}
							});
						}
						this.excecoes.splice(index, 1);
					}
				} else {
					if (this.planoDTO.duracoes) {
						this.planoDTO.duracoes.find((duracao, i) => {
							if (duracao.numeroMeses === event.row.duracao) {
								index2 = i;
							}
						});
					}
					this.excecoes.splice(event.index, 1);
				}
				if (index2 !== undefined) {
					this.planoDTO.duracoes.splice(index2, 1);
				}
				this.planoDTO.excecoes = this.excecoes;
				this.afterAdd.emit({ planoDTO: this.planoDTO });
				this.state.plano = this.planoDTO;
			}
			this.ngOnInit();
		}
	}

	validarExisteContratoDuracao(event): Promise<boolean> {
		return new Promise((resolve) => {
			if (event && event.row.duracao && this.planoDTO.codigo) {
				const codidoDoPlano: number = this.planoDTO.codigo;
				const duracao: number = event.row.duracao;
				this.planoService
					.validarExisteContratoDuracao(codidoDoPlano, duracao)
					.subscribe({
						next: () => {
							console.log("Duração pode ser excluída");
							resolve(true);
						},
						error: (error) => {
							console.log(error);
							if (error.error) {
								this.notificationService.error(error.error);
							}
							resolve(false);
						},
					});
			}
		});
	}

	editDuracaoValor(event) {
		if (event && event.row.pacote) {
			if (event.row.pacote.modalidades) {
				this.onPacoteChange(event.row.pacote, event.form);
			}
		}
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this.planoDTO = this.state.plano;
		let result = !this.verifyIfNewExists(row, data, rowIndex);
		if (result) {
			if (this.state.plano.tipoPlano === TipoPlano.PLANO_NORMAL) {
				result = this.validatePlanoNormal(row, form);
			} else if (this.state.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
				result = this.validatePlanoRecorrencia(row, form);
			}
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("dado-duplicado-table")
			);
		}
		return result;
	}

	verifyIfNewExists(row, dataTable, rowIndex) {
		let exists = false;
		dataTable.find((data, index) => {
			if (index !== rowIndex) {
				exists = this.compareObjects(row, data, "codigo", "edit", "codigo");
				if (exists) {
					return;
				}
			}
		});
		return exists;
	}

	compareObjects(obj1, obj2, uniqueKey?, ...keysAvoidCompare) {
		let equals = true;
		Object.keys(obj1).forEach((key) => {
			if (keysAvoidCompare.find((v) => v === key)) {
				return;
			}
			if (
				obj1[key] !== undefined &&
				obj2[key] !== undefined &&
				obj1[key] !== null &&
				obj2[key] !== null
			) {
				if (typeof obj1[key] !== "object") {
					if (obj1[key].toString() !== obj2[key].toString()) {
						equals = false;
						return;
					}
				} else {
					if (uniqueKey) {
						if (obj1[key][uniqueKey] !== obj2[key][uniqueKey]) {
							equals = false;
							return;
						}
					} else {
						equals = this.compareObjects(obj1[key], obj2[key]);
					}
				}
			}
		});
		return equals;
	}

	validatePlanoNormal(row, form) {
		const result = true;
		const duracao = form.get("duracao").value;
		const valor = form.get("valor").value;
		const vezesSemana = form.get("vezesSemana").value;

		const pacote = form.get("pacote").value;
		const modalidade = form.get("modalidade").value;
		const horario = form.get("horario").value;
		if (
			(!pacote || pacote === "" || pacote.descricao === "-") &&
			(!modalidade || modalidade === "" || modalidade.nome === "-")
		) {
			this.notificationService.error(
				this.traducoes.getLabel("error-modalidade-pacote-vazio")
			);
			return false;
		}

		if (!pacote || pacote === "" || pacote.descricao === "-") {
			if (
				vezesSemana === null ||
				vezesSemana === undefined ||
				vezesSemana === "" ||
				vezesSemana === 0
			) {
				this.notificationService.error(
					this.traducoes.getLabel("error-modalidade-vezes-semana-vazio")
				);
				return false;
			}
		}

		if (!horario) {
			this.notificationService.error(
				this.traducoes.getLabel("error-horario-vazio")
			);
			return false;
		}

		if (
			!(
				valor !== null &&
				valor !== undefined &&
				valor !== "" &&
				duracao !== null &&
				duracao !== undefined &&
				duracao !== "" &&
				duracao !== 0
			)
		) {
			this.notificationService.error(
				this.traducoes.getLabel("error-valor-duracao-vazio")
			);
			return false;
		}

		return result;
	}

	validatePlanoRecorrencia(row, form) {
		let result;
		const modalidade = form.get("modalidade").value;
		const horario = form.get("horario").value;
		const vezesSemana = form.get("vezesSemana").value;

		result =
			modalidade !== undefined &&
			modalidade !== null &&
			modalidade.nome !== "-" &&
			modalidade.nome !== "" &&
			horario !== undefined &&
			horario !== null &&
			horario.descricao !== "-" &&
			horario.descricao !== "" &&
			vezesSemana !== undefined &&
			vezesSemana !== null &&
			vezesSemana !== "";

		return result;
	}

	private onPacoteChange(option?: any, form?: any, row?: any) {
		const modalidade = this.tableDuracaoValor.columns.find(
			(v) => v.nome === "modalidade"
		);
		if (
			option != null &&
			option.codigo != null &&
			option.modalidades &&
			option.modalidades.length > 0
		) {
			modalidade.endpointUrl = undefined;
			const options = new Array<any>();
			option.modalidades.forEach((value) => {
				options.push(value.modalidade);
			});
			modalidade.inputSelectData = options;
		} else {
			modalidade.endpointUrl =
				this.admRest.buildFullUrlPlano("modalidades") + "/only-cod-name";
			if (form) {
				form.get("modalidade").setValue({ nome: "-" });
			}
			modalidade.inputSelectData = [];
		}
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
		this.onPacoteChange();
	}
}
