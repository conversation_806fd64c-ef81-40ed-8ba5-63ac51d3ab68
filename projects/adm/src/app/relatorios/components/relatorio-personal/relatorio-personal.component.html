<!--<div class=""></div>-->
<adm-layout
	(goBack)="voltarAdm()"
	addBtnLabel="Relatório de personal"
	i18n-addBtnLabel="@@relatorio-personal:addBtnLabel"
	i18n-modulo="@@relatorio-personal:modulo"
	i18n-pageTitle="@@relatorio-personal:pageTitle"
	modulo="Administrativo / Relatórios"
	pageTitle="Relatório de personal">
	<pacto-cat-card-plain>
		<div>
			<h1 i18n="@@relatorio-personal:title">Dados para consulta</h1>
			<div class="row">
				<div *ngIf="exibirEmpresa" class="col-md-2">
					<pacto-cat-form-select-filter
						[addEmptyOption]="true"
						[control]="formGroup.get('empresa')"
						[endpointUrl]="getUrlEmpresa()"
						[paramBuilder]="empresaSelectBuilder"
						errorMsg="Selecione uma empresa"
						i18n-errorMsg="@@relatorio-personal-empresa-error"
						i18n-label="@@relatorio-personal-empresa"
						idKey="codigo"
						label="Empresa"
						labelKey="nome"></pacto-cat-form-select-filter>
				</div>
				<div
					class="col-md-3 mr-auto"
					id="mes-referencia"
					style="margin-left: 0px">
					<pacto-cat-form-datepicker
						[control]="formGroup.get('mesReferencia')"
						[monthYearOnly]="true"
						errorMsg="Forneça uma data válida."
						i18n-errorMsg="@@relatorio-personal:error-msg-mes-referencia"
						i18n-label="@@relatorio-personal:label-mes-referencia"
						label="Mês referência"></pacto-cat-form-datepicker>
				</div>
				<div *ngIf="mostrarTablePersonais" class="col-auto">
					<div class="cards-valores">
						<div class="titulo-cards">
							<i class="pct pct-activity"></i>
							<h3 i18n="@@relatorio-personal:total-lancado">Total lancado</h3>
						</div>
						<div class="card-valor">
							<div>
								{{ totalLancadoApresentar }}
							</div>
						</div>
					</div>
				</div>
				<div *ngIf="mostrarTablePersonais" class="col-auto">
					<div class="cards-valores">
						<div class="titulo-cards">
							<i class="pct pct-dollar-sign"></i>
							<h3 i18n="@@relatorio-personal:total-pago">Total pago</h3>
						</div>
						<div
							[ngClass]="
								totalPago > 0.0 ? 'card-valor total-pago' : 'card-valor'
							">
							<div>
								{{ totalPagoApresentar }}
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="row filtros-situacao-row">
				<div class="col-md-12">
					<span
						class="filtros-situacao"
						i18n="@@relatorioPersonal:label-situacao">
						Situação:
					</span>
				</div>
			</div>
			<div class="row">
				<div class="col-md-1">
					<pacto-cat-checkbox
						[control]="formGroup.get('negociado')"
						i18n-label="@@relatorioPersonal:label-negociado"
						label="Negociado"></pacto-cat-checkbox>
				</div>
				<div class="col-md-1">
					<pacto-cat-checkbox
						[control]="formGroup.get('pago')"
						i18n-label="@@relatorioPersonal:label-pago"
						label="Pago"></pacto-cat-checkbox>
				</div>
				<div class="col-md-1">
					<pacto-cat-checkbox
						[control]="formGroup.get('vencido')"
						i18n-label="@@relatorioPersonal:label-vencido"
						label="Vencido"></pacto-cat-checkbox>
				</div>
				<div class="col-md-1">
					<pacto-cat-checkbox
						[control]="formGroup.get('livre')"
						i18n-label="@@relatorioPersonal:label-livre"
						label="Livre"></pacto-cat-checkbox>
				</div>
			</div>
			<h2 i18n="@@relatorio-personal:title-personal">Personal</h2>
			<pacto-filtro-checkbox
				#filtroPersonais
				(checkAllEvent)="checkAllPersonais($event)"
				(checksBoxChange)="checksBoxChange($event)"
				(pageChangeEvent)="pageChangePersonal($event)"
				(pageSizeChangeEvent)="pageSizeChangePersonais($event)"
				(searchEvent)="searchEvent($event)"
				[data]="personaisData"
				[formGroup]="formGroup"
				[formsControlsChecksBox]="formsControlsChecksBox"
				i18n-placeHolderInputBusca="@@relatorio-personal:busque-nome"
				idKey="codigo"
				labelKey="pessoa.nome"
				placeHolderInputBusca="Busque por nome"></pacto-filtro-checkbox>
			<div class="row justify-content-end btns">
				<pacto-cat-button
					(click)="cleanFilters()"
					class="btn-limpar-filtros"
					i18n-label="@@relatorios:limpar-filtros_label"
					label="Limpar filtros"
					style="margin-right: 20px"
					type="OUTLINE"></pacto-cat-button>
				<pacto-cat-button
					(click)="consultarRelatorioPersonais()"
					[icon]="'pct pct-search'"
					class="btn-consultar"
					i18n-label="@@consultar:consultar_label"
					label="Consultar"
					style="margin-right: 0px"
					type="PRIMARY"></pacto-cat-button>
			</div>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain *ngIf="mostrarTablePersonais" id="table-personais">
		<div class="table-wrapper pacto-shadow">
			<pacto-relatorio
				#tablePersonaisComponent
				(iconClick)="iconClick($event)"
				(rowClick)="openModalDetalhes($event)"
				[baseFilter]="baseFilter"
				[table]="tablePersonais"
				actionTitulo="Ações"
				i18n-actionTitulo="@@label-acoes"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@relatorio-personal:informar-mes-referencia"
		xingling="informar-mes-referencia">
		Por favor, informe o Mês referência!
	</span>
	<span
		i18n="@@relatorio-personal:usuario-sem-permissao"
		xingling="usuario-sem-permissao">
		Seu usuário não possui a permissão: 9.42 - Permitir visualizar o Relatório
		de Personal
	</span>
</pacto-traducoes-xingling>

<ng-template #columnPersonal>
	<span i18n="@@relatorio-personais:column-personal">Personal</span>
</ng-template>
<ng-template #columnAluno>
	<span i18n="@@relatorio-personais:column-aluno">Aluno</span>
</ng-template>
<ng-template #columnProduto>
	<span i18n="@@relatorio-personais:column-produto">Produto</span>
</ng-template>
<ng-template #columnValorProduto>
	<span i18n="@@relatorio-personais:column-valor-produto">Valor produto</span>
</ng-template>
<ng-template #columnDesconto>
	<span i18n="@@relatorio-personais:column-desconto">Desconto {{ moeda }}</span>
</ng-template>
<ng-template #columnValorFinal>
	<span i18n="@@relatorio-personais:column-valor-final">
		Valor final {{ moeda }}
	</span>
</ng-template>
<ng-template #columnSituacao>
	<span i18n="@@relatorio-personais:column-situacao">Situação</span>
</ng-template>

<ng-template #situacaoTaxa let-i="item">
	<ng-container [ngSwitch]="i.situacao">
		<div *ngSwitchCase="'LIVRE'" class="situacao livre">
			{{ i.situacao }}
		</div>
		<div *ngSwitchCase="'NEGOCIADO'" class="situacao negociado">
			{{ i.situacao }}
		</div>
		<div *ngSwitchCase="'VENCIDO'" class="situacao vencido">
			{{ i.situacao }}
		</div>
		<div *ngSwitchCase="'PAGO'" class="situacao pago">{{ i.situacao }}</div>
	</ng-container>
</ng-template>
