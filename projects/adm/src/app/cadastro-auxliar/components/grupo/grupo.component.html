<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@grupo-desconto-modulo"
	i18n-pageTitle="@@grupo-desconto-pageTitle"
	modulo="Administrativo"
	pageTitle="Grupo com desconto">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<pacto-relatorio
				#tableData
				(btnAddClick)="novoGrupo()"
				(iconClick)="iconClickFn($event)"
				(rowClick)="editarGrupo($event)"
				[actionTitulo]="traducao.getLabel('table-column-acoes')"
				[filterConfig]="filterConfig"
				[showBtnAdd]="true"
				[showShare]="true"
				[table]="table"
				i18n-labelBtnAdd="@@label-add-btn"
				idSuffix="grupo"
				labelBtnAdd="Adicionar"
				telaId="grupoComDesconto"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>
	<ng-template #celulaInativo let-grupo="item">
		<div class="pill-inativo {{ grupo?.status }}">
			{{ grupo?.status }}
		</div>
	</ng-template>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@grupo:percentual" xingling="GRUPO_PERCENTUAL">Percentual</span>
	<span i18n="@@grupo:valor" xingling="GRUPO_VALOR">Valor</span>
	<span i18n="@@grupo:bonus" xingling="GRUPO_BONUS">Bônus</span>
	<span i18n="@@grupo:editar" xingling="GRUPO_EDITAR">Editar um grupo</span>
	<span i18n="@@grupo:excluir" xingling="GRUPO_EXCLUIR">Excluir um grupo</span>
	<span i18n="@@grupo:excluido" xingling="GRUPO_EXCLUIDO_SUCESSO">
		Grupo excluído com sucesso!
	</span>
	<span i18n="@@mensagem-sem-permissao" xingling="mensagem-sem-permissao">
		Seu usuário não possui permissão, procure seu administrador
	</span>
	<span i18n="@@grupo:editarGrupo" xingling="editar">Editar</span>
	<span i18n="@@grupo:excluirGrupo" xingling="excluir">Excluir</span>
	<span i18n="@@grupo:table-column-acoes" xingling="table-column-acoes">
		Ações
	</span>
	<span i18n="@@adm:filtro-inativo" xingling="filtro-inativo">Status</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@adm:table-column-codigo-grupo">Código</span>
</ng-template>

<ng-template #columnDescricao>
	<span i18n="@@adm:table-column-descricao-grupo">Descrição</span>
</ng-template>
<ng-template #columnTipo>
	<span i18n="@@adm:table-column-tipo-grupo">Tipo</span>
</ng-template>
<ng-template #columnGrupoInativo>
	<span i18n="@@adm:table-column-grupo-inativo">Status</span>
</ng-template>
