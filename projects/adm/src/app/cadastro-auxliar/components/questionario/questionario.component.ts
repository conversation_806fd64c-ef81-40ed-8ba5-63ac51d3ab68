import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Api } from "sdk";
import { AdmRestService } from "../../../adm-rest.service";
import {
	CadastroAuxApiQuestionarioService,
	Questionario,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-questionario",
	templateUrl: "./questionario.component.html",
	styleUrls: ["./questionario.component.scss"],
})
export class QuestionarioComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: any;
	@ViewChild("columnNome", { static: true }) columnNome: any;

	table: PactoDataGridConfig;

	constructor(
		private router: Router,
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private questionarioService: CadastroAuxApiQuestionarioService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.initTable();
	}

	novoQuestionario() {
		this.router.navigate(["adm", "cad-aux", "novo-questionario"]);
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/questionario",
					false,
					Api.MSCADAUX
				),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nomeinterno",
						titulo: this.columnNome,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: this.traducao.getLabel("action-edit"),
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("QUESTIONARIO_EDITAR_TOOLTIP"),
						actionFn: (row) => this.editQuestionario(row),
					},
					{
						nome: this.traducao.getLabel("action-delete"),
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("QUESTIONARIO_EXCLUIR_TOOLTIP"),
						actionFn: (row) => this.editQuestionario(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	deleteQuestionario(questionario: Questionario) {
		this.questionarioService.delete(questionario.codigo).subscribe(
			(response) => {
				this.notificationService.success(
					this.traducao.getLabel("QUESTIONARIO_EXCLUIDA_SUCESSO")
				);
				this.tableData.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	editQuestionario(questionario) {
		this.router.navigate([
			"adm",
			"cad-aux",
			"questionario",
			questionario.codigo,
		]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editQuestionario(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteQuestionario(event.row);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}
}
