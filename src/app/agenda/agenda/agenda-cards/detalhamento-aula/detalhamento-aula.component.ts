import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import {
	AcaoRequeridaMarcarAluno,
	DadosInserirAluno,
	PerfilAcessoFuncionalidadeNome,
	StatusMarcarAlunoEnum,
	TreinoApiColaboradorService,
	TreinoApiTurmaService,
	TreinoApiAulaService,
	PerfilAcessoRecursoNome,
} from "treino-api";
import {
	LoaderService,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { SituacaoAlunoEnum } from "../../../../base/alunos/components/alunos-lista/alunos-lista.component";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { TurmaMarcacaoModalComponent } from "../../agenda-turma/turma-marcacao-modal/turma-marcacao-modal.component";
import {
	TipoProdutoZW,
	TurmaAulaExperimentalModalComponent,
} from "../../agenda-turma/turma-aula-experimental-modal/turma-aula-experimental-modal.component";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { TurmaReposicaoModalComponent } from "../../agenda-turma/turma-reposicao-modal/turma-reposicao-modal.component";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { ListaAlunosDetalhamentoComponent } from "./lista-alunos-detalhamento/lista-alunos-detalhamento.component";
import { TurmaSubstituirProfessorModalComponent } from "../../agenda-turma/turma-substituir-professor-modal/turma-substituir-professor-modal.component";
import { TurmaRemoverAulaCheiaModalComponent } from "../../agenda-turma/turma-remover-aula-cheia-modal/turma-remover-aula-cheia-modal.component";
import { ModalAgendaEditarAulaComponent } from "../../agenda-turma/modal-agenda-editar-aula/modal-agenda-editar-aula.component";
import { ReservaEquipamentoModalComponent } from "./reserva-equipamento-modal/reserva-equipamento-modal.component";

declare var moment;

@Component({
	selector: "pacto-detalhamento-aula",
	templateUrl: "./detalhamento-aula.component.html",
	styleUrls: ["./detalhamento-aula.component.scss"],
})
export class DetalhamentoAulaComponent implements OnInit {
	aula: any;
	id;
	data;
	alunoFc = new FormControl();
	podeRemoverAluno = false;
	podeInserirAluno = false;
	loading = false;
	integracaoZW = false;
	podeExcluirAulaCheia = false;
	podeEditarAulaCheia = false;
	podeSubstituirProfesor = false;
	@ViewChild("mensagemNotificacao", { static: true })
	mensagemNotificacao: TraducoesXinglingComponent;
	@ViewChild("listaAlunosDetalhamentoComponent", { static: false })
	listaAlunosDetalhamentoComponent: ListaAlunosDetalhamentoComponent;
	@ViewChild("vinculoAulaTraducao", { static: true })
	vinculoAulaTraducao: TraducoesXinglingComponent;
	@ViewChild("notificacoes", { static: true })
	notificacoes: TraducoesXinglingComponent;

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private rest: RestService,
		private modalService: ModalService,
		private loader: LoaderService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private colaboradorService: TreinoApiColaboradorService,
		private agendaStateService: AgendaCardsStateService,
		private turmaService: TreinoApiTurmaService,
		private aulaService: TreinoApiAulaService
	) {}

	ngOnInit() {
		this.hideTooltip();
		this.init();
	}

	hideTooltip() {
		try {
			const elemento = document.getElementById("cat-tooltip-slot-card-item");
			elemento.style.display = "none";
		} catch (e) {
			console.error(e);
		}
	}

	ngAfterViewInit() {
		if (!this.vinculoAulaTraducao) {
			this.vinculoAulaTraducao = {} as TraducoesXinglingComponent;
		}
		if (!this.notificacoes) {
			this.notificacoes = {} as TraducoesXinglingComponent;
		}
	}

	init() {
		this.loader.show();
		this.id = this.route.snapshot.paramMap.get("id");
		this.data = this.route.snapshot.paramMap.get("dia");
		if (this.id && this.data) {
			this.turmaService
				.aulaDetalhadaSemClientes(this.id, this.data)
				.subscribe((response) => {
					this.aula = response;
					this.podeRemoverAluno = this.permitirRemoverAluno();
					this.podeInserirAluno = this.permitirInserirAluno();
					this.integracaoZW = this.sessionService.integracaoZW;
					this.podeExcluirAulaCheia = this.permitirExcluirAulaCheia();
					this.podeEditarAulaCheia = this.permitirEditarAulaCheia();
					this.podeSubstituirProfesor = this.permitirSubstituirProfessor();
					this.alunoFcChanges();
					this.loader.hide();
					this.cd.detectChanges();
				});
		}
	}

	private alunoFcChanges() {
		this.alunoFc.valueChanges.subscribe((aluno) => {
			if (this.aula.aulaCheia) {
				this.addAlunoHandler(aluno);
			} else if (
				this.aula.permitirAulaExperimental ||
				(!this.aula.permitirAulaExperimental &&
					aluno.situacaoAluno === SituacaoAlunoEnum.ATIVO)
			) {
				this.addAlunoHandler(aluno);
			} else if (
				!this.aula.aulaCheia &&
				!this.aula.permitirAulaExperimental &&
				aluno.situacaoAluno !== SituacaoAlunoEnum.ATIVO
			) {
				this.snotifyService.error(this.notificacoes.getLabel("validPermissao"));
			}
			this.alunoFc.setValue(null, { emitEvent: false });
		});
	}

	private permitirSubstituirProfessor(): boolean {
		const horarioInicio = this.aula.horarioInicio.split(":");
		const dataTurma = moment(this.aula.dia)
			.add(horarioInicio[0], "hours")
			.add(horarioInicio[1], "minutes")
			.toDate();
		const dataHoje = new Date();
		return (
			(moment(dataHoje).isBefore(dataTurma) &&
				this.sessionService.funcionalidades.get(
					PerfilAcessoFuncionalidadeNome.ALTERAR_PROFESSOR_AULA
				)) ||
			((moment(dataHoje).isAfter(dataTurma) || dataHoje === dataTurma) &&
				this.sessionService.funcionalidades.get(
					PerfilAcessoFuncionalidadeNome.ALTERAR_PROFESSOR_AULA_INICIADA
				))
		);
	}

	get ocupacao(): string {
		if (!this.aula) {
			return "00/00";
		}
		let ocupacao = this.aula.numeroAlunos < 10 ? "0" : "";
		ocupacao += this.aula.numeroAlunos + "/";
		ocupacao += this.aula.capacidade < 10 ? "0" : "";
		ocupacao += this.aula.capacidade;
		return ocupacao;
	}

	get periodo(): string {
		return (
			this.aula.horarioInicio.replace(":", "h") +
			" - " +
			this.aula.horarioFim.replace(":", "h")
		);
	}

	get dia() {
		return moment(this.aula.dia).toDate();
	}

	get vagas(): number {
		if (this.aula) {
			return this.aula.capacidade - this.aula.numeroAlunos;
		} else {
			return 0;
		}
	}

	atualizar(turma) {
		this.aula = turma;
		this.updateTurma(turma);
		this.cd.detectChanges();
	}

	alunoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
			permiteAlunoOutraEmpresa:
				this.aula.permiteAlunoOutraEmpresa || this.aula.aulaCheia
					? "TRUE"
					: "FALSE",
			incluirAutorizado: this.aula.aulaCheia ? "TRUE" : "FALSE",
		};
	};

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	private permitirRemoverAluno() {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_ALUNO
		);
	}

	private permitirExcluirAulaCheia(): boolean {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_AULAS_DIA
		);
	}

	private permitirEditarAulaCheia(): boolean {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EDITAR_AULAS_DIA
		);
	}

	private permitirInserirAluno() {
		const horarioInicio = this.aula.horarioInicio.split(":");
		const dataTurma = moment(this.aula.dia)
			.add(horarioInicio[0], "hours")
			.add(horarioInicio[1], "minutes")
			.toDate();
		const dataHoje = new Date();
		return (
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.INSERIR_ALUNO
			) &&
			(this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.INSERIR_ALUNO_AULA_INICIADA
			)
				? true
				: moment(dataHoje).isBefore(dataTurma))
		);
	}

	get liberado() {
		return this.aula && this.aula.bloqueado === false;
	}

	updateTurma(turma) {
		this.listaAlunosDetalhamentoComponent.reload();
		this.aula = turma;
		this.agendaStateService.updateTurma(turma);
		this.cd.detectChanges();
	}

	private addAlunoHandler(aluno) {
		const dadosInserirAluno: DadosInserirAluno = {
			matricula: aluno.matriculaZW,
			dia: this.aula.dia,
			autorizado: aluno.autorizado,
			acao: AcaoRequeridaMarcarAluno.SELECIONAR_ALUNO,
		};
		this.turmaService
			.marcarAlunoTurma(this.aula.horarioTurmaId, dadosInserirAluno)
			.subscribe((result) => {
				if (result.messageID) {
					if (result.messageID.includes("aulaEstaCheia")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("aulaEstaCheia")
						);
						return;
					}
					if (result.messageID.includes("jaMatriculouNaAula")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("jaMatriculouNaAula")
						);
						return;
					}

					if (result.messageID.includes("aposInicioAula")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("soPodeMarcar") +
								result.messageValue +
								this.notificacoes.getLabel("aposInicioAula")
						);
						return;
					}
					if (result.messageID.includes("antesInicioAula")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("soPodeMarcarAte") +
								result.messageValue +
								this.notificacoes.getLabel("antesInicioAula")
						);
						return;
					}
				}
				switch (result.status) {
					case StatusMarcarAlunoEnum.SUCESSO:
						this.updateTurma(result.conteudo.turma);
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("inseridoSucesso")
						);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
						);
						this.openModalReservaEquipamento(aluno.codigoCliente);
						break;
					case StatusMarcarAlunoEnum.TURMA_CHEIA:
						this.snotifyService.error(
							this.vinculoAulaTraducao.getLabel("turmaCheia")
						);
						break;
					case StatusMarcarAlunoEnum.MARCAR_REPOSICAO:
						this.openReposicaoModalHandler(
							dadosInserirAluno,
							aluno,
							result.conteudo
						);
						break;
					case StatusMarcarAlunoEnum.CREDITOS_PARA_MARCACAO:
						this.openMarcacaoModalHandler(
							dadosInserirAluno,
							aluno,
							result.conteudo,
							true
						);
						break;
					case StatusMarcarAlunoEnum.CREDITOS_EXTRA:
						this.openMarcacaoModalHandler(
							dadosInserirAluno,
							aluno,
							result.conteudo,
							false
						);
						break;
					case StatusMarcarAlunoEnum.ALUNO_SEM_CONTRATO:
						this.openAulaExperimentalProdutoZWModal(dadosInserirAluno, aluno);
						break;
					case StatusMarcarAlunoEnum.AULA_EXPERIMENTAL:
						this.openAulaCheiaExperimental(dadosInserirAluno, aluno, result);
						break;
					case StatusMarcarAlunoEnum.ALUNO_SEM_CADASTRO_ZW:
						this.snotifyService.error(
							this.vinculoAulaTraducao.getLabel("naoTemcadastroZW")
						);
						break;
					case StatusMarcarAlunoEnum.CONTRATO_FUTURO:
						this.snotifyService.error(
							this.vinculoAulaTraducao.getLabel("CONTRATO_FUTURO")
						);
						break;
					case StatusMarcarAlunoEnum.DIARIA_CONTRATO_FUTURO:
						this.openAulaExperimentalProdutoZWModal(dadosInserirAluno, aluno);
						break;
				}
			});
	}

	private openModalReservaEquipamento(alunoId: number) {
		if (this.aula.mapaEquipamentos) {
			const ref = this.modalService.open(
				"Mapa dos equipamentos",
				ReservaEquipamentoModalComponent,
				PactoModalSize.LARGE,
				"modal-reserva-equip"
			);
			ref.componentInstance.alunoId = alunoId;
			ref.componentInstance.aula = this.aula;
			ref.result.then(
				(response) => {
					this.aula.equipamentosOcupados = response;
					this.listaAlunosDetalhamentoComponent.turma.equipamentosOcupados =
						response;
					this.listaAlunosDetalhamentoComponent.mapSelectionComponent.listEquipamentosOcupados =
						response;
					this.listaAlunosDetalhamentoComponent.mapSelectionComponent.initializeMaps(
						null
					);
					this.listaAlunosDetalhamentoComponent.acaoHandler(
						this.listaAlunosDetalhamentoComponent.turma
					);
					this.cd.detectChanges();
				},
				() => {}
			);
		}
	}

	private openReposicaoModalHandler(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any,
		conteudo: any
	) {
		const modal = this.modalService.open(
			this.vinculoAulaTraducao.getLabel("editarReposicaoAula"),
			TurmaReposicaoModalComponent
		);
		modal.componentInstance.aluno = aluno;
		modal.componentInstance.turma = this.aula;
		const aulaApresentar = [];
		if (conteudo) {
			conteudo.aulasDesmarcadas.forEach((aula) => {
				const dia = moment(aula.data).toDate();
				aulaApresentar.push({
					id: aula.id + "-" + moment(dia).format("YYYYMMDD"),
					nome:
						aula.modalidadeNome +
						" - " +
						moment(dia).format("DD/MM/YYYY") +
						" | " +
						aula.horarioInicio,
					dia: moment(dia).format("YYYYMMDD"),
				});
			});
		}
		modal.componentInstance.setup(aulaApresentar);
		modal.result.then((retorno) => {
			dadosInserirAluno.aulaDesmarcarId = retorno.id;
			dadosInserirAluno.diaAulaDesmarcar = retorno.dia;
			dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.REPOSICAO;
			this.turmaService
				.marcarAlunoTurma(this.aula.horarioTurmaId, dadosInserirAluno)
				.subscribe((resultReposicao) => {
					if (resultReposicao.messageID) {
						if (resultReposicao.messageID.includes("aulaEstaCheia")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("aulaEstaCheia")
							);
							return;
						}
						if (resultReposicao.messageID.includes("jaMatriculouNaAula")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("jaMatriculouNaAula")
							);
							return;
						}
					}
					if (resultReposicao.status === StatusMarcarAlunoEnum.SUCESSO) {
						this.updateTurma(resultReposicao.conteudo.turma);
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("inseridoSucesso")
						);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
						);
					}
				});
		});
	}

	private openMarcacaoModalHandler(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any,
		conteudo: any,
		marcacao: boolean
	) {
		if (conteudo.aulasMarcada >= conteudo.saldoCredito) {
			this.snotifyService.error(this.notificacoes.getLabel("validCreditos"));
		} else {
			const modal = this.modalService.open(
				this.vinculoAulaTraducao.getLabel("marcacaoAula"),
				TurmaMarcacaoModalComponent
			);
			modal.componentInstance.aluno = aluno;
			modal.componentInstance.turma = this.aula;
			modal.componentInstance.aulasMarcada = conteudo.aulasMarcada;
			modal.componentInstance.saldoCredito = conteudo.saldoCredito;
			modal.componentInstance.marcacao = marcacao;
			modal.result.then(() => {
				dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.MARCACAO;
				this.turmaService
					.marcarAlunoTurma(this.aula.horarioTurmaId, dadosInserirAluno)
					.subscribe((resultMarcacao) => {
						if (resultMarcacao.messageID) {
							if (resultMarcacao.messageID.includes("aulaEstaCheia")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("aulaEstaCheia")
								);
								return;
							}
							if (resultMarcacao.messageID.includes("jaMatriculouNaAula")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("jaMatriculouNaAula")
								);
								return;
							}
						}
						if (resultMarcacao.status === StatusMarcarAlunoEnum.SUCESSO) {
							this.updateTurma(resultMarcacao.conteudo.turma);
							this.snotifyService.success(
								this.vinculoAulaTraducao.getLabel("inseridoSucesso")
							);
							this.sessionService.notificarRecursoEmpresa(
								RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
							);
							this.openModalReservaEquipamento(aluno.codigoCliente);
						}
					});
			});
		}
	}

	private openAulaExperimentalProdutoZWModal(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any
	) {
		this.turmaService.obterProdutosZW().subscribe((produtos) => {
			const modalLabel =
				this.vinculoAulaTraducao && this.vinculoAulaTraducao.getLabel
					? this.vinculoAulaTraducao.getLabel("editarExperimental")
					: "Editar Aula Experimental";
			const modal = this.modalService.open(
				modalLabel,
				TurmaAulaExperimentalModalComponent
			);
			modal.componentInstance.aluno = aluno;
			modal.componentInstance.carregarProdutos(produtos, [
				{ id: TipoProdutoZW.DIARIA },
				{ id: TipoProdutoZW.FREEPASS },
			]);
			modal.result.then((produtoId) => {
				dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.AULA_EXPERIMENTAL;
				dadosInserirAluno.produtoId = produtoId;
				this.turmaService
					.marcarAlunoTurma(this.aula.horarioTurmaId, dadosInserirAluno)
					.subscribe((resultAulaExperimental) => {
						if (resultAulaExperimental.messageID) {
							if (resultAulaExperimental.messageID.includes("aulaEstaCheia")) {
								this.snotifyService.error(
									this.notificacoes && this.notificacoes.getLabel
										? this.notificacoes.getLabel("aulaEstaCheia")
										: "Aula está cheia"
								);
								return;
							}
							if (
								resultAulaExperimental.messageID.includes("jaMatriculouNaAula")
							) {
								this.snotifyService.error(
									this.notificacoes && this.notificacoes.getLabel
										? this.notificacoes.getLabel("jaMatriculouNaAula")
										: "Já matriculou na aula"
								);
								return;
							}
						}
						if (
							resultAulaExperimental.conteudo &&
							resultAulaExperimental.conteudo.messageID &&
							resultAulaExperimental.conteudo.messageID.includes(
								"semPermissaoDiariaFreePass"
							)
						) {
							this.snotifyService.error(
								this.notificacoes.getLabel("semPermissaoDiariaFreePass")
							);
							return;
						}
						if (
							resultAulaExperimental.status === StatusMarcarAlunoEnum.SUCESSO
						) {
							this.updateTurma(resultAulaExperimental.conteudo.turma);
							this.snotifyService.success(
								this.vinculoAulaTraducao.getLabel("inseridoSucesso")
							);
							this.sessionService.notificarRecursoEmpresa(
								RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
							);
							this.openModalReservaEquipamento(aluno.codigoCliente);
						}
					});
			});
		});
	}

	private openAulaCheiaExperimental(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any,
		result: any
	) {
		if (!result.conteudo.messageID) {
			this.openAulaExperimentalProdutoZWModal(dadosInserirAluno, aluno);
			return;
		}
		const modal = this.modalService.open(
			this.vinculoAulaTraducao.getLabel("editarExperimental"),
			TurmaAulaExperimentalModalComponent
		);
		modal.componentInstance.aluno = aluno;
		if (
			result.conteudo.messageID.toString().includes("aulaExperimentalHorario")
		) {
			modal.componentInstance.carregarBodyAulaCheiaExperimental(
				this.notificacoes.getLabel("foraHorarioPlano") +
					result.conteudo.messageValue +
					this.notificacoes.getLabel("foraHorarioPlano2")
			);
		}
		if (
			result.conteudo.messageID
				.toString()
				.includes("aulaExperimentalModalidade")
		) {
			modal.componentInstance.carregarBodyAulaCheiaExperimental(
				this.notificacoes.getLabel("naoTemModalidadeExperimental") +
					result.conteudo.messageValue +
					this.notificacoes.getLabel("naoTemModalidadeExperimental2")
			);
		}
		modal.result.then(() => {
			dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.AULA_EXPERIMENTAL;
			this.turmaService
				.marcarAlunoTurma(this.aula.horarioTurmaId, dadosInserirAluno)
				.subscribe((resultAulaExperimental) => {
					if (resultAulaExperimental.messageID) {
						if (resultAulaExperimental.messageID.includes("aulaEstaCheia")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("aulaEstaCheia")
							);
							return;
						}
						if (
							resultAulaExperimental.messageID.includes("jaMatriculouNaAula")
						) {
							this.snotifyService.error(
								this.notificacoes.getLabel("jaMatriculouNaAula")
							);
							return;
						}
					}
					if (resultAulaExperimental.status === StatusMarcarAlunoEnum.SUCESSO) {
						this.updateTurma(resultAulaExperimental.conteudo.turma);
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("inseridoSucesso")
						);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
						);
						this.openModalReservaEquipamento(aluno.codigoCliente);
					}
				});
		});
	}

	get urlLog() {
		return this.rest.buildFullUrl(
			`log/aula/${this.aula.horarioTurmaId}/${this.aula.dia}`
		);
	}

	substituirProfessor() {
		if (this.podeSubstituirProfesor && this.aula && this.aula.aulaCheia) {
			this.colaboradorService
				.obterTodosColaboradoresAptosAAula(false)
				.subscribe((response) => {
					const modal = this.modalService.open(
						this.mensagemNotificacao.getLabel("substituirProfessor"),
						TurmaSubstituirProfessorModalComponent
					);
					modal.componentInstance.professores = response.content;
					modal.result.then((result) => {
						this.turmaService
							.substituirProfessor(
								this.aula.horarioTurmaId,
								this.aula.dia,
								result
							)
							.subscribe(() => {
								this.snotifyService.success(
									this.mensagemNotificacao.getLabel("substituirProfessorSucess")
								);
								this.turmaService
									.aulaDetalhada(this.aula.horarioTurmaId, this.aula.dia)
									.subscribe((turma) => {
										this.updateTurma(turma);
										this.agendaStateService.stopLoad();
									});
							});
					});
				});
		}
	}

	removeHandler() {
		if (this.aula.numeroAlunos === 0) {
			const modal = this.modalService.open(
				"Excluir aula",
				TurmaRemoverAulaCheiaModalComponent
			);
			modal.result.then((result) => {
				this.turmaService
					.excluirAulaCheia(this.aula.horarioTurmaId, this.aula.dia, result)
					.subscribe(() => {
						this.snotifyService.success(
							this.mensagemNotificacao.getLabel("aulaCheiaExcluida")
						);
						this.router.navigate(["agenda", "painel", "cards"]);
						this.agendaStateService.forceLoad$.next(true);
					});
			});
		} else {
			this.snotifyService.error(
				this.mensagemNotificacao.getLabel("existeAlunosNaAula")
			);
		}
	}

	editAulaCheia() {
		const modal = this.modalService.open(
			"Editar aula",
			ModalAgendaEditarAulaComponent,
			PactoModalSize.LARGE,
			"mdl-agenda-editar-aula-window"
		);

		modal.componentInstance.aulaId = this.id;
		modal.componentInstance.dataAula = this.data;
		modal.componentInstance.horarioInicial = this.aula.horarioInicio;
		modal.componentInstance.horarioFinal = this.aula.horarioFim;
		modal.result
			.then((result) => {
				this.init();
			})
			.catch((e) => {});
	}

	get podeEditarAula() {
		return (
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.EDITAR_AULAS_DIA
			) === true
		);
	}
}
