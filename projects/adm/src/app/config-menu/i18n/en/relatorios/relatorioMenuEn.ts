import { NavModuleI18n } from "ui-kit";
import { relatorioMenuParentItem } from "../../../navigation/relatorios/relatorio-menu-parents-nav-config.service";
import {
	relatorioArmario,
	relatorioClientesVisitantes,
	relatorioContratosPorDuracao,
	relatorioConvidado,
	relatorioGympass,
	relatorioIndicadorAcesso,
	relatorioListaAcesso,
	relatorioProdutoComVigencia,
	relatorioSaldoCredito,
	relatorioTotalizadorAcesso,
	relatorioPersonal,
	relatorioCupomFiscal,
	relatorioClientesComRestricoes,
} from "../../../navigation/relatorios/relatorio-menu-nav-config.service";
import {
	relarioClientesAniversariantes,
	relatorioAgendamentos,
	relatorioBvs,
	relatorioCardex,
	relatorioCliente,
	relatorioClienteComCobrancaAutomaticaBloqueada,
	relatorioClientesCancelados,
	relatorioClientesComAtestado,
	relatorioClientesComBonus,
	relatorioClienteSimplificado,
	relatorioClientesSemSessao,
	relatorioClientesTrancados,
	relatorioComissao,
	relatorioComissaoConsultor,
	relatorioComissaoProfessor,
	relatorioCompetenciaMensal,
	relatorioConsolidadoParcelas,
	relatorioConsultaRecibos,
	relatorioConsultaTurma,
	relatorioControleLogs,
	relatorioDescontoOcupacao,
	relatorioDiario,
	relatorioFaturamento,
	relatorioFaturamentoRecebido,
	relatorioFechamentoAcesso,
	relatorioFechamentoCaixa,
	relatorioFrequenciaOcupacao,
	relatorioFrequenciaTurmas,
	relatorioGeralCliente,
	relatorioHistoricoPonto,
	relatorioListaChamada,
	relatorioMapaTurmas,
	relatorioMovimentoProduto,
	relatorioOrcamento,
	relatorioParcelas,
	relatorioPesquisa,
	relatorioPrevisaoRenovacao,
	relatorioReceitaPorPeriodo,
	relatorioRepasse,
	relatorioSaldoContaCorrente,
	relatorioTotalizadorTickets,
	relatorioTransacoesPix,
} from "../../../navigation/relatorios/relatorio-menu-nav-legado-config.service";

const relatorioGeralClientePt: NavModuleI18n = {
	[relatorioGeralCliente.id]: {
		name: "Customer General",
		description: "Customer General Report",
		searchTokens: "Customer General Report, Customer General",
	},
};

const relatorioClientePt: NavModuleI18n = {
	[relatorioCliente.id]: {
		name: "Client",
		description: "Clients Report",
		searchTokens: "Clients Report, Clients, Report",
	},
};

const relatorioClienteSimplificadoPt: NavModuleI18n = {
	[relatorioClienteSimplificado.id]: {
		name: "Simplified Client",
		description: "Simplified Client",
		searchTokens: "Simplified Client, Simplified, Client",
	},
};

const relatorioClientesVisitantesPt: NavModuleI18n = {
	[relatorioClientesVisitantes.id]: {
		name: "Visitors",
		description: "Visitors Report",
		searchTokens: "Visitors, report",
	},
};

const relatorioClientesCanceladosPt: NavModuleI18n = {
	[relatorioClientesCancelados.id]: {
		name: "Canceled Customers",
		description: "Canceled Customers report",
		searchTokens: "Canceled Customers, Canceled Customers report",
	},
};

const relatorioClientesTrancadosPt: NavModuleI18n = {
	[relatorioClientesTrancados.id]: {
		name: "Paused members",
		description: "Paused members",
		searchTokens: "Paused members, Paused, members",
	},
};

const relatorioClientesComBonusPt: NavModuleI18n = {
	[relatorioClientesComBonus.id]: {
		name: "Bonus members",
		description: "Bonus members report",
		searchTokens: "bonus members report, bonus, members, report",
	},
};

const relatorioClientesComAtestadoPt: NavModuleI18n = {
	[relatorioClientesComAtestado.id]: {
		name: "Members with medical certificate",
		description: "Members with medical certificate report",
		searchTokens:
			"rMembers with medical certificate report, Members medical certificate, report",
	},
};

const relarioClientesAniversariantesPt: NavModuleI18n = {
	[relarioClientesAniversariantes.id]: {
		name: "Birthdays",
		description: "Birthdays report",
		searchTokens: "Birthdays report, Birthdays, report",
	},
};

const relatorioContratosPorDuracaoPt: NavModuleI18n = {
	[relatorioContratosPorDuracao.id]: {
		name: "Period contracts",
		description: "Period contracts report",
		searchTokens: "Period contracts report",
	},
};

const relatorioGympassPt: NavModuleI18n = {
	[relatorioGympass.id]: {
		name: "Gympass",
		description: "Gympass report",
		searchTokens: "Gympass report, Gympass, report",
	},
};

const relatorioSaldoCreditoPt: NavModuleI18n = {
	[relatorioSaldoCredito.id]: {
		name: "Credit balance",
		description: "Credit balance report",
		searchTokens: "Credit balance, Credit, balance, report",
	},
};

const relatorioHistoricoPontoPt: NavModuleI18n = {
	[relatorioHistoricoPonto.id]: {
		name: "Points History",
		description: "Points History",
		searchTokens: "Points, History",
	},
};

const relatorioIndicadorAcessoPt: NavModuleI18n = {
	[relatorioIndicadorAcesso.id]: {
		name: "Access Indicator",
		description: "Access Indicator",
		searchTokens: "Access Indicator",
	},
};

const relatorioOrcamentoPt: NavModuleI18n = {
	[relatorioOrcamento.id]: {
		name: "Budgets",
		description: "Budgets report",
		searchTokens: "budgets, report",
	},
};

const relatorioConvidadoPt: NavModuleI18n = {
	[relatorioConvidado.id]: {
		name: "Guests",
		description: "Guests report",
		searchTokens: "guests, report, guests report",
	},
};

const relatorioClienteComCobrancaAutomaticaBloqueadaPt: NavModuleI18n = {
	[relatorioClienteComCobrancaAutomaticaBloqueada.id]: {
		name: "Customers with Automatic Billing Blocked",
		description: "Customers with Automatic Billing Blocked",
		searchTokens: "Customers, Automatic, Billing Blocked",
	},
};

const relatorioFechamentoAcessoPt: NavModuleI18n = {
	[relatorioFechamentoAcesso.id]: {
		name: "Access Closing",
		description: "Access Closing report",
		searchTokens: "Access Closing report, Access Closing, report",
	},
};

const relatorioTotalizadorAcessoPt: NavModuleI18n = {
	[relatorioTotalizadorAcesso.id]: {
		name: "Access Totalizer",
		description: "Access Totalizer",
		searchTokens: "Access, Totalizer",
	},
};

const relatorioTotalizadorTicketsPt: NavModuleI18n = {
	[relatorioTotalizadorTickets.id]: {
		name: "Ticket Totalizer",
		description: "Ticket Totalizer",
		searchTokens: "Ticket Totalizer",
	},
};

const relatorioArmarioPt: NavModuleI18n = {
	[relatorioArmario.id]: {
		name: "Lockers",
		description: "Lockers report",
		searchTokens: "Lockers, report, Lockers report",
	},
};

const relatorioListaAcessoPt: NavModuleI18n = {
	[relatorioListaAcesso.id]: {
		name: "Access list",
		description: "Access list",
		searchTokens: "Access list, Access, list",
	},
};

const relatorioListaChamadaPt: NavModuleI18n = {
	[relatorioListaChamada.id]: {
		name: "Attendance list",
		description: "Attendance list",
		searchTokens: "attendance list, attendance, list",
	},
};

const relatorioFrequenciaOcupacaoPt: NavModuleI18n = {
	[relatorioFrequenciaOcupacao.id]: {
		name: "Frequency and Occupation",
		description: "Frequency and Occupation",
		searchTokens: "Frequency, Occupation",
	},
};

const relatorioFrequenciaTurmasPt: NavModuleI18n = {
	[relatorioFrequenciaTurmas.id]: {
		name: "Class Frequency Report",
		description: "Class Frequency Report",
		searchTokens: "frequency, class, report",
	},
};

const relatorioDescontoOcupacaoPt: NavModuleI18n = {
	[relatorioDescontoOcupacao.id]: {
		name: "Occupancy Discount",
		description: "Occupancy Discount",
		searchTokens: "Occupancy, Discount",
	},
};

const relatorioMapaTurmasPt: NavModuleI18n = {
	[relatorioMapaTurmas.id]: {
		name: "Class Map",
		description: "Class Map",
		searchTokens: "Class, Map",
	},
};

const relatorioConsultaTurmaPt: NavModuleI18n = {
	[relatorioConsultaTurma.id]: {
		name: "Class Consultation",
		description: "Class Consultation",
		searchTokens: "Class Consultation, classes",
	},
};

const relatorioFechamentoCaixaPt: NavModuleI18n = {
	[relatorioFechamentoCaixa.id]: {
		name: "Cashier closing",
		description: "Cashier closing",
		searchTokens: "Cashier closing",
	},
};

const relatorioCompetenciaMensalPt: NavModuleI18n = {
	[relatorioCompetenciaMensal.id]: {
		name: "Monthly Competency",
		description: "Monthly Competency",
		searchTokens: "Monthly, Competency",
	},
};

const relatorioFaturamentoPt: NavModuleI18n = {
	[relatorioFaturamento.id]: {
		name: "Billing",
		description: "Billing report",
		searchTokens: "Billing, report",
	},
};

const relatorioFaturamentoRecebidoPt: NavModuleI18n = {
	[relatorioFaturamentoRecebido.id]: {
		name: "Billing received",
		description: "Billing received",
		searchTokens: "Billing received, Billing, received",
	},
};

const relatorioReceitaPorPeriodoPt: NavModuleI18n = {
	[relatorioReceitaPorPeriodo.id]: {
		name: "Revenue by Period",
		description: "Revenue by Period",
		searchTokens: "Revenue by Period",
	},
};

const relatorioParcelasPt: NavModuleI18n = {
	[relatorioParcelas.id]: {
		name: "Installments",
		description: "Installments report",
		searchTokens: "installments report",
	},
};

const relatorioConsolidadoParcelasPt: NavModuleI18n = {
	[relatorioConsolidadoParcelas.id]: {
		name: "Consolidated payment",
		description: "Consolidated payment",
		searchTokens: "Consolidated payment",
	},
};

const relatorioSaldoContaCorrentePt: NavModuleI18n = {
	[relatorioSaldoContaCorrente.id]: {
		name: "Account Balance",
		description: "Account Balance report",
		searchTokens: "Account Balance report",
	},
};

const relatorioTransacoesPixPt: NavModuleI18n = {
	[relatorioTransacoesPix.id]: {
		name: "Transfers",
		description: "Transfers report",
		searchTokens: "Transfers report",
	},
};

const relatorioConsultaRecibosPt: NavModuleI18n = {
	[relatorioConsultaRecibos.id]: {
		name: "Receipt Inquiry",
		description: "Receipt Inquiry",
		searchTokens: "Receipt Inquiry",
	},
};

const relatorioCupomFiscalPt: NavModuleI18n = {
	[relatorioCupomFiscal.id]: {
		name: "Receipt",
		description: "Receipt",
		searchTokens: "Receipt",
	},
};

const relatorioComissaoProfessorPt: NavModuleI18n = {
	[relatorioComissaoProfessor.id]: {
		name: "Coaches Commission",
		description: "Coaches Commission",
		searchTokens: "Coaches Commission",
	},
};

const relatorioComissaoConsultorPt: NavModuleI18n = {
	[relatorioComissaoConsultor.id]: {
		name: "Consultant Commission",
		description: "Consultant Commission",
		searchTokens: "Consultant, Commission, Consultant Commission",
	},
};

const relatorioPrevisaoRenovacaoPt: NavModuleI18n = {
	[relatorioPrevisaoRenovacao.id]: {
		name: "Renewal",
		description: "Renewal",
		searchTokens: "Renewal",
	},
};

const relatorioBvsPt: NavModuleI18n = {
	[relatorioBvs.id]: {
		name: "VQ (Visitors Quiz)",
		description: "VQ report",
		searchTokens: "vq, vqs",
	},
};

const relatorioRepassePt: NavModuleI18n = {
	[relatorioRepasse.id]: {
		name: "Transfer",
		description: "Transfer report",
		searchTokens: "transfer",
	},
};

const relatorioPesquisaPt: NavModuleI18n = {
	[relatorioPesquisa.id]: {
		name: "Researches",
		description: "Researches report",
		searchTokens: "Researches",
	},
};

const relatorioPersonalPt: NavModuleI18n = {
	[relatorioPersonal.id]: {
		name: "Personal",
		description: "Personal trainer report",
		searchTokens: "Personal trainer report",
	},
};

const relatorioProdutoComVigenciaPt: NavModuleI18n = {
	[relatorioProdutoComVigencia.id]: {
		name: "Product viability",
		description: "Product viability report",
		searchTokens: "product viability, report",
	},
};

const relatorioMovimentoProdutoPt: NavModuleI18n = {
	[relatorioMovimentoProduto.id]: {
		name: "Inventory movement",
		description: "Inventory movement",
		searchTokens: "Inventory movement",
	},
};

const relatorioCardexPt: NavModuleI18n = {
	[relatorioCardex.id]: {
		name: "Kardex",
		description: "Kardex",
		searchTokens: "Kardex",
	},
};

const relatorioControleLogsPt: NavModuleI18n = {
	[relatorioControleLogs.id]: {
		name: "Log control",
		description: "Log control",
		searchTokens: "Log control, Log, control",
	},
};

const relatorioComissaoPt: NavModuleI18n = {
	[relatorioComissao.id]: {
		name: "Commission",
		description: "Commission",
		searchTokens: "Commission",
	},
};

const relatorioDiarioPt: NavModuleI18n = {
	[relatorioDiario.id]: {
		name: "Daily",
		description: "Daily",
		searchTokens: "Daily",
	},
};

const relatorioAgendamentosPt: NavModuleI18n = {
	[relatorioAgendamentos.id]: {
		name: "Schedules",
		description: "Schedules",
		searchTokens: "schedules",
	},
};

const relatorioClientesSemSessaoPt: NavModuleI18n = {
	[relatorioClientesSemSessao.id]: {
		name: "Clients without Session",
		description: "Clients without Session",
		searchTokens: "Clients without Session",
	},
};

const relatorioClientesComRestricoesPt: NavModuleI18n = {
	[relatorioClientesComRestricoes.id]: {
		name: "Clients whith restriction",
		description: "Clients whith restriction",
		searchTokens: "Clients, restriction, report",
	},
};

export const relatorioMenuEn: NavModuleI18n = {
	[relatorioMenuParentItem.id]: {
		name: "Report",
		description: `Report`,
		searchTokens: "report",
	},
	...relatorioAgendamentosPt,
	...relarioClientesAniversariantesPt,
	...relatorioArmarioPt,
	...relatorioBvsPt,
	...relatorioCardexPt,
	...relatorioClienteSimplificadoPt,
	...relatorioClientePt,
	...relatorioClientesCanceladosPt,
	...relatorioClientesTrancadosPt,
	...relatorioClientesComAtestadoPt,
	...relatorioClientesComBonusPt,
	...relatorioClienteComCobrancaAutomaticaBloqueadaPt,
	...relatorioClientesSemSessaoPt,
	...relatorioComissaoPt,
	...relatorioComissaoConsultorPt,
	...relatorioComissaoProfessorPt,
	...relatorioCompetenciaMensalPt,
	...relatorioConsolidadoParcelasPt,
	...relatorioMapaTurmasPt,
	...relatorioConsultaTurmaPt,
	...relatorioConsultaRecibosPt,
	...relatorioContratosPorDuracaoPt,
	...relatorioControleLogsPt,
	...relatorioConvidadoPt,
	...relatorioCupomFiscalPt,
	...relatorioDescontoOcupacaoPt,
	...relatorioDiarioPt,
	...relatorioFaturamentoPt,
	...relatorioFaturamentoRecebidoPt,
	...relatorioFechamentoAcessoPt,
	...relatorioFechamentoCaixaPt,
	...relatorioFrequenciaOcupacaoPt,
	...relatorioFrequenciaTurmasPt,
	...relatorioGeralClientePt,
	...relatorioGympassPt,
	...relatorioHistoricoPontoPt,
	...relatorioIndicadorAcessoPt,
	...relatorioListaAcessoPt,
	...relatorioListaChamadaPt,
	...relatorioMapaTurmasPt,
	...relatorioMovimentoProdutoPt,
	...relatorioOrcamentoPt,
	...relatorioParcelasPt,
	...relatorioPersonalPt,
	...relatorioPesquisaPt,
	...relatorioPrevisaoRenovacaoPt,
	...relatorioProdutoComVigenciaPt,
	...relatorioReceitaPorPeriodoPt,
	...relatorioRepassePt,
	...relatorioSaldoContaCorrentePt,
	...relatorioSaldoCreditoPt,
	...relatorioTotalizadorAcessoPt,
	...relatorioTotalizadorTicketsPt,
	...relatorioTransacoesPixPt,
	...relatorioClientesVisitantesPt,
	...relatorioClientesComRestricoesPt,
};
