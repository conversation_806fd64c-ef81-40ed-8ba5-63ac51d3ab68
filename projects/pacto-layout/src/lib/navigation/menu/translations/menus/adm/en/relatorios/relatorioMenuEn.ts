import { NavModuleI18n } from "ui-kit";

const relatorioGeralClienteEn: NavModuleI18n = {
	relatorioGeralCliente: {
		name: "G<PERSON> de Clientes",
		description: "Relatório geral de clientes",
		searchTokens:
			"relatorio, relatório, clientes, geral, relatorio geral de clientes",
	},
};

const relatorioClienteEn: NavModuleI18n = {
	relatorioCliente: {
		name: "Relatório de Clientes",
		description: "Relatório de Clientes",
		searchTokens: "relatorio, clientes",
	},
};

const clienteEn: NavModuleI18n = {
	cliente: {
		name: "Cliente",
		description: "Cliente",
		searchTokens: "cliente, clientes",
	},
};

const relatorioClienteSimplificadoEn: NavModuleI18n = {
	relatorioClienteSimplificado: {
		name: "Lista de Clientes Simplificada",
		description: "Lista de Clientes Simplificada",
		searchTokens: "cliente, simplificado",
	},
};

const relatorioClientesVisitantesEn: NavModuleI18n = {
	relatorioClientesVisitantes: {
		name: "Visitantes",
		description: "Relatório de Visitantes",
		searchTokens: "relatorio, visitantes",
	},
};

const relatorioClientesCanceladosEn: NavModuleI18n = {
	relatorioClientesCancelados: {
		name: "Clientes Cancelados",
		description: "Relatório de clientes cancelados",
		searchTokens: "relatorio, clientes, cancelados",
	},
};

const relatorioClientesTrancadosEn: NavModuleI18n = {
	relatorioClientesTrancados: {
		name: "Clientes Trancados",
		description: "relatorio, clientes, trancados",
		searchTokens: "",
	},
};

const relatorioClientesComBonusEn: NavModuleI18n = {
	relatorioClientesComBonus: {
		name: "Clientes com Bônus",
		description: "Relatório de clientes com bônus",
		searchTokens: "relatorio, clientes, bonus",
	},
};

const relatorioClientesComAtestadoEn: NavModuleI18n = {
	relatorioClientesComAtestado: {
		name: "Clientes com Atestado",
		description: "Relatório de clientes com atestado",
		searchTokens: "relatorio, clientes, atestado",
	},
};

const relarioClientesAniversariantesEn: NavModuleI18n = {
	relatorioClientesAniversariantes: {
		name: "Aniversariantes",
		description: "Relatório de aniversariantes",
		searchTokens:
			"relatorio, aviversariante, aniversariantes, aniversário, aniversário",
	},
};

const relatorioContratosPorDuracaoEn: NavModuleI18n = {
	relatorioContratosPorDuracao: {
		name: "Contratos por Duração",
		description: "Relatório de contratos por duração",
		searchTokens: "contrato, relatorio, duração",
	},
};

const relatorioGympassEn: NavModuleI18n = {
	relatorioGympass: {
		name: "Gympass",
		description: "Gympass",
		searchTokens: "relatorio, gympass",
	},
};

const relatorioSaldoCreditoEn: NavModuleI18n = {
	relatorioSaldoCredito: {
		name: "Saldo de Créditos",
		description: "Relatório de saldo de créditos",
		searchTokens: "relatorio, saldo, credito",
	},
};

const relatorioHistoricoPontoEn: NavModuleI18n = {
	relatorioHistoricoPonto: {
		name: "Histórico de Pontos",
		description: "Relatório de histórico de pontos",
		searchTokens: "relatorio, historico, pontos",
	},
};

const relatorioIndicadorAcessoEn: NavModuleI18n = {
	relatorioIndicadorAcesso: {
		name: "Indicador de Acesso",
		description: "Indicador de Acesso",
		searchTokens: "indicador, acesso",
	},
};

const relatorioOrcamentoEn: NavModuleI18n = {
	relatorioOrcamento: {
		name: "Orçamentos",
		description: "Orçamentos",
		searchTokens: "relatorio, orçamento",
	},
};

const relatorioConvidadoEn: NavModuleI18n = {
	relatorioConvidado: {
		name: "Convidados",
		description: "Convidados",
		searchTokens: "relatorio, convidados",
	},
};

const relatorioClienteComCobrancaAutomaticaBloqueadaEn: NavModuleI18n = {
	relatorioClienteComCobrancaAutomaticaBloqueada: {
		name: "Cobranças Automáticas Bloqueadas",
		description: "Cobranças Automáticas Bloqueadas",
		searchTokens:
			"clientes, cobrança, automática, bloqueada, cobrança bloqueada",
	},
};

const relatorioFechamentoAcessoEn: NavModuleI18n = {
	relatorioFechamentoAcesso: {
		name: "Fechamento Acessos",
		description: "Relatório de fechamento de acessos",
		searchTokens: "relatorio, fechamento, acessos",
	},
};

const relatorioTotalizadorAcessoEn: NavModuleI18n = {
	relatorioTotalizadorAcesso: {
		name: "Totalizador de Acessos",
		description: "Totalizador de Acessos",
		searchTokens: "totalizador, acessos",
	},
};

const relatorioTotalizadorTicketsEn: NavModuleI18n = {
	relatorioTotalizadorTickets: {
		name: "Totalizador de Tickets",
		description: "Totalizador de tickets",
		searchTokens: "totalizador, tickets",
	},
};

const relatorioArmarioEn: NavModuleI18n = {
	relatorioArmario: {
		name: "Armários",
		description: "Armários",
		searchTokens: "relatorio, armario",
	},
};

const relatorioListaAcessoEn: NavModuleI18n = {
	relatorioListaAcesso: {
		name: "Lista de Acessos",
		description: "Lista de Acessos",
		searchTokens: "lista, acesso",
	},
};

const relatorioListaChamadaEn: NavModuleI18n = {
	relatorioListaChamada: {
		name: "Lista de Chamada",
		description: "Lista de chamada",
		searchTokens: "lista, chamada",
	},
};

const relatorioFrequenciaOcupacaoEn: NavModuleI18n = {
	relatorioFrequenciaOcupacao: {
		name: "Frequência e Ocupação",
		description: "Frequência e Ocupação",
		searchTokens: "frequência, ocupação",
	},
};

const relatorioFrequenciaTurmasEn: NavModuleI18n = {
	relatorioFrequenciaTurmas: {
		name: "Relatório de Frequência de Turmas",
		description: "Frequência",
		searchTokens:
			"frequência, frequencia, turmas, relatório, relatório de frequência de turmas",
	},
};

const relatorioDescontoOcupacaoEn: NavModuleI18n = {
	relatorioDescontoOcupacao: {
		name: "Desconto por Ocupação",
		description: "Desconto por Ocupação",
		searchTokens: "desconto, ocupação",
	},
};

const relatorioMapaTurmasEn: NavModuleI18n = {
	relatorioMapaTurma: {
		name: "Mapa de Turmas",
		description: "Mapa de Turmas",
		searchTokens: "mapa, turma, turmas",
	},
};

const relatorioConsultaTurmaEn: NavModuleI18n = {
	relatorioConsultaTurma: {
		name: "Consulta de Turmas",
		description: "Consulta de Turmas",
		searchTokens: "consulta, turma, turmas",
	},
};

const relatorioFechamentoCaixaEn: NavModuleI18n = {
	relatorioFechamentoCaixa: {
		name: "Fechamento de Caixa por Operador",
		description: "Fechamento de Caixa por Operador",
		searchTokens: "fechamento, caixa, operador",
	},
};

const relatorioCompetenciaMensalEn: NavModuleI18n = {
	relatorioCompetenciaMensal: {
		name: "Competência Mensal",
		description: "Competência Mensal",
		searchTokens: "competência, mensal",
	},
};

const relatorioFaturamentoEn: NavModuleI18n = {
	relatorioFaturamento: {
		name: "Faturamento",
		description: "Relatório de faturamento",
		searchTokens: "relatorio, faturamento",
	},
};

const relatorioFaturamentoRecebidoEn: NavModuleI18n = {
	relatorioFaturamentoRecebido: {
		name: "Faturamento Recebido",
		description: "Faturamento Recebido",
		searchTokens: "faturamento, recebido",
	},
};

const relatorioReceitaPorPeriodoEn: NavModuleI18n = {
	relatorioReceitaPorPeriodo: {
		name: "Receita por Período",
		description: "Receita por período",
		searchTokens: "receita, período",
	},
};

const relatorioParcelasEn: NavModuleI18n = {
	relatorioParcelas: {
		name: "Parcelas",
		description: "Relatório de Parcelas",
		searchTokens: "relatorio, parcelas",
	},
};

const relatorioSaldoContaCorrenteEn: NavModuleI18n = {
	relatorioSaldoContaCorrente: {
		name: "Saldo Conta Corrente",
		description: "Relatório de saldo de conta corrente",
		searchTokens: "relatorio, saldo, conta corrente, conta, corrente",
	},
};

const pedidosPinPadEn: NavModuleI18n = {
	pedidosPinPad: {
		name: "Pedidos Pinpad",
		description: "Pedidos Pinpad",
		searchTokens: "pinpad, pedidos, pedidos pinpad",
	},
};

const relatorioTransacoesPixEn: NavModuleI18n = {
	relatorioTransacoesPix: {
		name: "Transações PIX",
		description: "Relatório de Transações PIX",
		searchTokens: "relatório, transações, pix",
	},
};

const relatorioConsultaRecibosEn: NavModuleI18n = {
	relatorioConsultaRecibos: {
		name: "Consulta de Recibos",
		description: "Consulta de recibos",
		searchTokens: "consulta, recibos",
	},
};

const relatorioCupomFiscalEn: NavModuleI18n = {
	relatorioCupomFiscal: {
		name: "Cupom Fiscal",
		description: "Cupom Fiscal",
		searchTokens: "cupom, fiscal",
	},
};

const relatorioComissaoProfessorEn: NavModuleI18n = {
	relatorioComissaoProfessor: {
		name: "Comissão para Professor",
		description: "Comissão para professor",
		searchTokens: "comissão, professor",
	},
};

const relatorioComissaoConsultorEn: NavModuleI18n = {
	relatorioComissaoConsultor: {
		name: "Comissão para Consultor",
		description: "Comissão para consultor",
		searchTokens: "comissão, consultor",
	},
};

const relatorioPrevisaoRenovacaoEn: NavModuleI18n = {
	relatorioPrevisaoRenovacao: {
		name: "Previsão de Renovação",
		description: "Previsão de Renovação",
		searchTokens:
			"previsão, renovação, relatório de previsão de renovação por contrato",
	},
};

const relatorioBvsEn: NavModuleI18n = {
	relatorioBvs: {
		name: "BVs",
		description: "Relatório de BVs",
		searchTokens: "bv, bvs",
	},
};

const relatorioRepasseEn: NavModuleI18n = {
	relatorioRepasse: {
		name: "Repasse",
		description: "Repasse",
		searchTokens: "repasse",
	},
};

const relatorioPesquisaEn: NavModuleI18n = {
	relatorioPesquisa: {
		name: "Pesquisas",
		description: "Pesquisas",
		searchTokens: "pesquisas",
	},
};

const conviteAulaExperimentalOcupacaoEn: NavModuleI18n = {
	conviteAulaExperimentalOcupacao: {
		name: "Convite Aula Experimental",
		description: "Convite Aula Experimental",
		searchTokens: "convites, aula, experimental",
	},
};

const transacoesPixEn: NavModuleI18n = {
	transacoesPix: {
		name: "Transações Pix",
		description: "Transações Pix",
		searchTokens: "transacoes pix, pix, transações",
	},
};

const relatorioPersonalEn: NavModuleI18n = {
	relatorioPersonal: {
		name: "Personal",
		description: "Personal",
		searchTokens: "relatorio, personal",
	},
};

const relatorioProdutoComVigenciaEn: NavModuleI18n = {
	relatorioProdutoComVigencia: {
		name: "Produtos (com vigência)",
		description: "Produtos (com vigência)",
		searchTokens: "produto, com vigência, relatorio, vigência",
	},
};

const relatorioMovimentoProdutoEn: NavModuleI18n = {
	relatorioMovimentoProduto: {
		name: "Movimento de Produto",
		description: "Movimento de Produto",
		searchTokens: "movimento, produto",
	},
};

const relatorioCardexEn: NavModuleI18n = {
	relatorioCardex: {
		name: "Cardex",
		description: "Cardex",
		searchTokens: "cardex",
	},
};

const relatorioControleLogsEn: NavModuleI18n = {
	relatorioControleLogs: {
		name: "Controle de Logs",
		description: "Controle de Logs",
		searchTokens: "controle, logs",
	},
};

const relatorioComissaoEn: NavModuleI18n = {
	relatorioComissao: {
		name: "Comissão",
		description: "Comissão",
		searchTokens: "comissão",
	},
};

const relatorioDiarioEn: NavModuleI18n = {
	relatorioDiario: {
		name: "Diário",
		description: "Diário",
		searchTokens: "diario, diário",
	},
};

const relatorioAgendamentosEn: NavModuleI18n = {
	relatorioAgendamentos: {
		name: "Agendamentos",
		description: "Agendamentos",
		searchTokens: "agendamentos",
	},
};

const relatorioClientesSemSessaoEn: NavModuleI18n = {
	relatorioClientesSemSessao: {
		name: "Clientes sem Sessão",
		description: "Clientes sem Sessão",
		searchTokens: "clientes, sem sessão, sessão",
	},
};

export const relatorioSgpModalidadesSemTurmaMenuEn: NavModuleI18n = {
	relatorioSgpModalidadesSemTurma: {
		name: "SGP Modalidades sem Turma",
	},
};

export const relatorioSgpModalidadesComTurmaMenuEn: NavModuleI18n = {
	relatorioSgpModalidadesComTurma: {
		name: "SGP Modalidades com Turma",
	},
};

export const relatorioSgpTurmaMenuEn: NavModuleI18n = {
	relatorioSgpTurma: {
		name: "SGP Mapa Estatístico",
	},
};

export const relatorioSgpAvaliacoesFisicasMenuEn: NavModuleI18n = {
	relatorioSgpAvaliacoesFisicas: {
		name: "SGP Avaliações Fisicas",
	},
};

export const relatorioAcessoParentMenuEn: NavModuleI18n = {
	"relatorio-acesso": {
		name: "Relatórios de Acessos",
	},
};

export const relatorioClienteParentMenuEn: NavModuleI18n = {
	"relatorio-cliente": {
		name: "Relatórios de Clientes",
	},
};

export const relatorioComissaoParentMenuEn: NavModuleI18n = {
	"relatorio-comissao": {
		name: "Relatórios de Comissão",
	},
};

export const relatorioEstatisticoParentMenuEn: NavModuleI18n = {
	"relatorio-estatistico": {
		name: "Relatório Estatístico",
	},
};

export const relatorioFinanceiroParentMenuEn: NavModuleI18n = {
	"relatorio-financeiro": {
		name: "Relatório Financeiro",
	},
};

export const relatorioTurmaParentMenuEn: NavModuleI18n = {
	"relatorio-turma": {
		name: "Relatório de Turmas",
	},
};

export const relatorioOutrosParentMenuEn: NavModuleI18n = {
	"relatorio-outros": {
		name: "Outros relatórios",
	},
};

const relatorioClientesComRestricoesEn: NavModuleI18n = {
	relatorioClientesComRestricoes: {
		name: "Clients whith restriction",
	},
};

export const relatorioMenuEn: NavModuleI18n = {
	relatorios: {
		name: "Relatórios",
		description: `Relatórios`,
		searchTokens: "relatorios, relatórios",
	},
	...relatorioAcessoParentMenuEn,
	...relatorioClienteParentMenuEn,
	...relatorioComissaoParentMenuEn,
	...relatorioEstatisticoParentMenuEn,
	...relatorioFinanceiroParentMenuEn,
	...relatorioTurmaParentMenuEn,
	...relatorioOutrosParentMenuEn,
	...relatorioAgendamentosEn,
	...relarioClientesAniversariantesEn,
	...relatorioArmarioEn,
	...relatorioBvsEn,
	...relatorioCardexEn,
	...relatorioClienteSimplificadoEn,
	...relatorioClienteEn,
	...relatorioClientesCanceladosEn,
	...relatorioClientesTrancadosEn,
	...relatorioClientesComAtestadoEn,
	...relatorioClientesComBonusEn,
	...relatorioClienteComCobrancaAutomaticaBloqueadaEn,
	...relatorioClientesSemSessaoEn,
	...relatorioComissaoEn,
	...relatorioComissaoConsultorEn,
	...relatorioComissaoProfessorEn,
	...relatorioCompetenciaMensalEn,
	...relatorioMapaTurmasEn,
	...relatorioConsultaTurmaEn,
	...relatorioConsultaRecibosEn,
	...relatorioContratosPorDuracaoEn,
	...relatorioControleLogsEn,
	...relatorioConvidadoEn,
	...relatorioCupomFiscalEn,
	...relatorioDescontoOcupacaoEn,
	...relatorioDiarioEn,
	...relatorioFaturamentoEn,
	...relatorioFaturamentoRecebidoEn,
	...relatorioFechamentoAcessoEn,
	...relatorioFechamentoCaixaEn,
	...relatorioFrequenciaOcupacaoEn,
	...relatorioFrequenciaTurmasEn,
	...relatorioGeralClienteEn,
	...relatorioGympassEn,
	...relatorioHistoricoPontoEn,
	...relatorioIndicadorAcessoEn,
	...relatorioListaAcessoEn,
	...relatorioListaChamadaEn,
	...relatorioMapaTurmasEn,
	...relatorioMovimentoProdutoEn,
	...relatorioOrcamentoEn,
	...relatorioParcelasEn,
	...relatorioPersonalEn,
	...relatorioPesquisaEn,
	...relatorioPrevisaoRenovacaoEn,
	...relatorioProdutoComVigenciaEn,
	...relatorioReceitaPorPeriodoEn,
	...relatorioRepasseEn,
	...relatorioSaldoContaCorrenteEn,
	...relatorioSaldoCreditoEn,
	...relatorioTotalizadorAcessoEn,
	...relatorioTotalizadorTicketsEn,
	...relatorioTransacoesPixEn,
	...relatorioClientesVisitantesEn,
	...relatorioSgpModalidadesComTurmaMenuEn,
	...relatorioSgpTurmaMenuEn,
	...relatorioSgpModalidadesSemTurmaMenuEn,
	...relatorioSgpAvaliacoesFisicasMenuEn,
	...clienteEn,
	...pedidosPinPadEn,
	...transacoesPixEn,
	...conviteAulaExperimentalOcupacaoEn,
	...relatorioClientesComRestricoesEn,
};
