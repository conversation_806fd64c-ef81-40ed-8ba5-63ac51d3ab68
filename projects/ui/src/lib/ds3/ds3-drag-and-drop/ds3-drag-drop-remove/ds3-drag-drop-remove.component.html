<ng-content select="ds3-drag-drop-title"></ng-content>
<div
	(cdkDropListDropped)="drop($event)"
	(cdkDropListEntered)="onEntered($event)"
	[cdkDropListConnectedTo]="dragDropAddContainerIndexes"
	[cdkDropListData]="data"
	cdkDropList
	class="ds3-drag-drop-remove-container"
	id="ds3-drag-drop-remove-list">
	<div
		*ngFor="let item of dragDropItemArray; let index = index"
		[cdkDragData]="{ dragDropItemArray: dragDropItemArray, index: index }"
		[id]="'ds3-drag-remove-' + index"
		cdkDrag
		class="ds3-drag-drop-remove-drag">
		<ng-container *ngTemplateOutlet="item?.templateRef"></ng-container>
		<div *cdkDragPlaceholder class="ds3-drag-drop-placeholder">
			<i class="pct pct-eye-off" id="ds3-drag-drop-placeholder-remove-icon"></i>
		</div>
	</div>
</div>
