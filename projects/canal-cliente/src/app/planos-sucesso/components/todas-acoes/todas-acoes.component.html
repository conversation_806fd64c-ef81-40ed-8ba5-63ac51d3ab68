<ccl-layout-conteudo>
	<pacto-basic-card>
		<ccl-tab-menu-planos-sucesso></ccl-tab-menu-planos-sucesso>
		<div>
			<pacto-relatorio
				#listaPlanos
				(rowClick)="abrirAcao($event)"
				[filterConfig]="filterConfig"
				[table]="gridConfig"></pacto-relatorio>
		</div>
	</pacto-basic-card>
</ccl-layout-conteudo>

<ng-template #tituloColunaAcoes>
	<span class="titulo">AÇOES</span>
</ng-template>
<ng-template #tituloColunaResponsavel>
	<span class="titulo">RESPONSÁVEL</span>
</ng-template>
<ng-template #tituloColunaDataFinal>
	<span class="titulo">DATA FINAL</span>
</ng-template>
<ng-template #tituloColunaSituacao>
	<span class="titulo">CONCLUSÃO</span>
</ng-template>

<ng-template #celulaSituacao let-acao="item">
	<div class="situacao situacao-{{ acao.situacao }}">
		<i *ngIf="acao && !acao.atrasada" class="fa fa-check-circle-o"></i>
		<i *ngIf="acao && acao.atrasada" class="mdi mdi-alert acao-atrasada"></i>
		<div *ngIf="!acao" class="ghost-circle situacao-ghost"></div>
	</div>
</ng-template>

<ng-template #labelSituacaoFiltro>
	<span>Situação</span>
</ng-template>

<ng-template #celulaDataFinal let-acao="item">
	<div class="datafinal">{{ acao?.datafinal }}</div>
</ng-template>

<ng-template #celulaNomeAcao let-acao="item">
	<span>{{ acao?.nome }}</span>
</ng-template>
