import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

import { MusculoRoutingModule } from "./musculo.routing.module";
import { BaseSharedModule } from "@base-shared/base-shared.module";

import { MusculoListaComponent } from "./components/musculo-lista/musculo-lista.component";
import { MusculoEditComponent } from "./components/musculo-edit/musculo-edit.component";

@NgModule({
	imports: [CommonModule, NgbModule, BaseSharedModule, MusculoRoutingModule],
	declarations: [MusculoListaComponent, MusculoEditComponent],
	entryComponents: [MusculoEditComponent],
})
export class MusculoModule {}
