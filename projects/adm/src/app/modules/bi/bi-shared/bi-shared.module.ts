import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
	BiOrganizeActionsDirective,
	BiOrganizeComponent,
} from "./components/bi-organize/bi-organize.component";
import { Ds3CardModule, Ds3DragAndDropModule, UiModule } from "ui-kit";
import { BiMdlAjudaComponent } from "./components/bi-mdl-ajuda/bi-mdl-ajuda.component";
import { HttpClientModule } from "@angular/common/http";

@NgModule({
	declarations: [
		BiOrganizeComponent,
		BiOrganizeActionsDirective,
		BiMdlAjudaComponent,
	],
	imports: [
		CommonModule,
		HttpClientModule,
		UiModule,
		Ds3DragAndDropModule,
		Ds3CardModule,
	],
	exports: [
		BiOrganizeComponent,
		BiOrganizeActionsDirective,
		BiMdlAjudaComponent,
		UiModule,
		Ds3DragAndDropModule,
		Ds3CardModule,
	],
	entryComponents: [BiMdlAjudaComponent],
})
export class BiSharedModule {}
